# 云主机部署指南

本文档专门针对在网络受限的云主机环境中部署 Faster-Whisper 服务。

## 问题分析

本地运行正常，但在云主机上出现以下错误：
- `Network is unreachable` - 网络不可达
- `Cannot find an appropriate cached snapshot folder` - 无法从 Hugging Face 下载模型
- `local_files_only=False` - 尝试联网下载模型

## 解决方案

### 1. 代码修改

已修改 `app_new.py` 中的模型加载逻辑：
- 设置 `local_files_only=True` - 只使用本地模型
- 移除网络下载逻辑
- 优化错误处理和日志输出

### 2. 部署步骤

#### 步骤 1: 上传项目文件到云主机

```bash
# 上传整个项目目录到云主机
scp -r /path/to/local-Faster-Whisper user@cloud-server:/path/to/deploy/
```

#### 步骤 2: 创建环境配置文件

```bash
# 在云主机上复制环境配置
cp env_cloud_example.txt .env
```

#### 步骤 3: 安装依赖

```bash
# 激活虚拟环境（如果有的话）
source .venv/bin/activate

# 安装 Python 依赖
pip install -r requirements.txt
```

#### 步骤 4: 验证模型文件

```bash
# 检查模型文件是否完整
ls -la models/models--Systran--faster-whisper-small/snapshots/*/model.bin
ls -la models/tts/
```

#### 步骤 5: 使用专用启动脚本

```bash
# 使用云主机专用启动脚本
./start_cloud_server.sh
```

或者直接使用：

```bash
# 设置环境变量后启动
export WHISPER_LOCAL_MODEL_DIR=./models
export LOCAL_MODEL_DIR=./models
export TTS_LOCAL_MODEL_DIR=./models/tts

python -m uvicorn app_new:app --host 0.0.0.0 --port 8001 --log-level info
```

## 环境变量说明

| 变量名 | 说明 | 默认值 | 云主机建议值 |
|--------|------|--------|-------------|
| `WHISPER_LOCAL_MODEL_DIR` | Whisper 模型本地目录 | 空 | `./models` |
| `LOCAL_MODEL_DIR` | 通用模型目录 | `./models` | `./models` |
| `TTS_LOCAL_MODEL_DIR` | TTS 模型本地目录 | 空 | `./models/tts` |
| `WHISPER_MODEL_SIZE` | 模型大小 | `small` | `small` |
| `WHISPER_DEVICE` | 计算设备 | `cpu` | `cpu` |

## 故障排除

### 问题 1: 模型文件不存在

**错误信息：**
```
✗ 未找到本地 Whisper 模型
```

**解决方案：**
1. 检查模型文件是否上传完整
2. 确认模型目录结构正确
3. 重新上传模型文件

### 问题 2: 权限问题

**错误信息：**
```
Permission denied
```

**解决方案：**
```bash
# 给脚本执行权限
chmod +x start_cloud_server.sh

# 检查模型文件权限
chmod -R 755 models/
```

### 问题 3: 端口占用

**错误信息：**
```
[Errno 48] Address already in use
```

**解决方案：**
```bash
# 检查端口占用
lsof -i :8001

# 使用不同端口
export PORT=8002
./start_cloud_server.sh
```

## 验证部署

### 检查服务状态

```bash
# 健康检查
curl http://localhost:8001/health

# 查看服务信息
curl http://localhost:8001/
```

### 测试语音识别

```bash
# 使用 curl 测试
curl -X POST "http://localhost:8001/transcribe" \
     -H "Content-Type: multipart/form-data" \
     -F "audio_file=@test.wav"
```

## 性能优化建议

### 云主机配置

1. **CPU 优化：**
   - 使用 `int8` 计算类型减少内存占用
   - 根据 CPU 核心数调整工作进程数

2. **内存优化：**
   - 使用 `small` 模型而不是 `medium` 或 `large`
   - 监控内存使用情况

3. **存储优化：**
   - 定期清理临时文件
   - 使用 SSD 存储提升 I/O 性能

### 监控和日志

```bash
# 查看实时日志
tail -f whisper_service.log

# 监控系统资源
htop
```

## 安全注意事项

1. **网络安全：**
   - 配置防火墙只开放必要端口
   - 使用 HTTPS（推荐）
   - 设置适当的上传文件大小限制

2. **文件系统安全：**
   - 限制上传文件类型
   - 定期清理临时文件
   - 设置适当的文件权限

## 常见问题

### Q: 云主机没有 GPU，能否使用 GPU 版本？
A: 不推荐。云主机的 GPU 通常需要额外配置，且成本较高。CPU 版本对于大多数应用场景已经足够。

### Q: 如何更新模型？
A: 在本地下载新模型后，上传到云主机的 `models` 目录，然后重启服务。

### Q: 如何备份和恢复？
A: 备份整个项目目录，包括 `models` 文件夹和配置文件。

---

## 技术支持

如果在部署过程中遇到问题，请：

1. 检查日志文件：`whisper_service.log`
2. 确认模型文件完整性
3. 验证环境变量设置
4. 检查系统资源使用情况

部署成功后，服务将在指定端口提供语音识别和 TTS 服务。
