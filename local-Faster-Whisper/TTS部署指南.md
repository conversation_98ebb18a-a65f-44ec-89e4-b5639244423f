# Coqui TTS 服务部署指南

本文档介绍如何在现有的 Faster-Whisper 项目中部署和使用 Coqui TTS 服务。

## 🎯 概述

我们已经在 Faster-Whisper 项目基础上集成了 Coqui TTS 服务，提供以下功能：

- **文字转语音合成**：将文字转换为高质量的语音
- **多语言支持**：支持多种语言的语音合成
- **多说话人**：支持不同的说话人声音
- **灵活输出**：支持内存输出和文件保存
- **参数调节**：可调节语速等参数

## 📋 前置要求

- Python 3.8+
- 8GB+ RAM（推荐16GB+）
- 已安装 Faster-Whisper 项目依赖

## 🚀 快速部署

### 1. 安装 TTS 依赖

```bash
# 激活虚拟环境（如果有的话）
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装新的依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件，添加TTS相关配置
nano .env
```

在 `.env` 文件中确保包含以下TTS配置：

```bash
# TTS 配置
TTS_MODEL_NAME=tts_models/multilingual/multi-dataset/xtts_v2
TTS_GPU=false
TTS_LANGUAGE=zh-cn
# TTS_SPEAKER_WAV=./speaker.wav  # 可选，用于声音克隆
TTS_OUTPUT_DIR=./tts_outputs
```

### 3. 启动服务

```bash
# 方式 1: 使用 Python 脚本
python app.py

# 方式 2: 使用 Uvicorn
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 验证服务

服务启动后，可以通过以下方式验证TTS功能：

```bash
# 检查服务健康状态
curl http://localhost:8000/health

# 获取TTS模型信息
curl http://localhost:8000/tts/info

# 测试语音合成
curl -X POST http://localhost:8000/tts/synthesize \
  -H "Content-Type: application/json" \
  -d '{"text": "你好，欢迎使用TTS服务", "language": "zh-cn"}' \
  --output test.wav
```

## 🎵 使用 TTS 服务

### API 接口列表

| 接口 | 方法 | 描述 |
|-----|------|------|
| `/tts/info` | GET | 获取TTS模型信息 |
| `/tts/models` | GET | 获取可用TTS模型列表 |
| `/tts/languages` | GET | 获取支持的语言列表 |
| `/tts/speakers` | GET | 获取可用说话人列表 |
| `/tts/synthesize` | POST | 文字转语音（返回音频数据） |
| `/tts/synthesize/file` | POST | 文字转语音（保存为文件） |

### Python 调用示例

```python
import requests

# 基本语音合成
def text_to_speech(text, language="zh-cn", speed=1.0):
    url = "http://localhost:8000/tts/synthesize"
    data = {
        "text": text,
        "language": language,
        "speed": speed
    }

    response = requests.post(url, json=data)

    if response.status_code == 200:
        # 保存音频文件
        with open("output.wav", "wb") as f:
            f.write(response.content)
        print("✅ 语音合成成功")
        return True
    else:
        print(f"❌ 合成失败: {response.status_code}")
        return False

# 保存到文件
def text_to_speech_file(text, filename, language="zh-cn"):
    url = "http://localhost:8000/tts/synthesize/file"
    data = {
        "text": text,
        "output_filename": filename,
        "language": language
    }

    response = requests.post(url, json=data)

    if response.status_code == 200:
        result = response.json()
        if result["success"]:
            print(f"✅ 文件合成成功: {result['data']['filename']}")
            return result["data"]
        else:
            print(f"❌ 合成失败: {result['error']}")
            return None
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return None

# 使用示例
if __name__ == "__main__":
    # 基本合成
    text_to_speech("你好，欢迎使用文字转语音服务！")

    # 文件合成
    text_to_speech_file("这是一个测试文件", "test.wav")
```

### cURL 调用示例

```bash
# 基本语音合成
curl -X POST http://localhost:8000/tts/synthesize \
  -H "Content-Type: application/json" \
  -d '{
    "text": "你好，这是一个语音合成的示例",
    "language": "zh-cn",
    "speed": 1.0
  }' \
  --output speech.wav

# 保存为文件
curl -X POST http://localhost:8000/tts/synthesize/file \
  -H "Content-Type: application/json" \
  -d '{
    "text": "保存为文件的语音合成测试",
    "output_filename": "test_output.wav",
    "language": "zh-cn",
    "speed": 0.9
  }'

# 获取服务信息
curl http://localhost:8000/tts/info
curl http://localhost:8000/tts/languages
curl http://localhost:8000/tts/speakers
```

## 🧪 测试 TTS 功能

运行内置的测试脚本：

```bash
python test_tts.py
```

测试脚本会自动检查：
- ✅ 服务状态
- ✅ TTS模型初始化
- ✅ 语音合成功能
- ✅ 文件保存功能
- ✅ 语言和说话人支持

## ⚙️ 配置选项

### TTS 模型选择

| 模型 | 描述 | 特点 |
|-----|------|------|
| `tts_models/multilingual/multi-dataset/xtts_v2` | XTTS v2 多语言模型 | 高质量，支持声音克隆 |
| `tts_models/en/ljspeech/tacotron2-DDC_ph` | Tacotron2 英文模型 | 快速，适合英文 |
| `tts_models/zh-cn/css10/vits` | VITS 中文模型 | 高质量中文合成 |

### 环境变量配置

```bash
# TTS 模型配置
TTS_MODEL_NAME=tts_models/multilingual/multi-dataset/xtts_v2
TTS_GPU=false                    # 是否使用GPU
TTS_LANGUAGE=zh-cn              # 默认语言
TTS_SPEAKER_WAV=./speaker.wav   # 可选：说话人音频文件
TTS_OUTPUT_DIR=./tts_outputs    # 输出目录
```

## 🔧 故障排除

### 常见问题

1. **模型下载慢或失败**
   ```bash
   # 检查网络连接
   ping huggingface.co

   # 使用代理（如果需要）
   export HTTP_PROXY=http://proxy.example.com:8080
   export HTTPS_PROXY=http://proxy.example.com:8080
   ```

2. **内存不足**
   ```bash
   # 使用CPU模式
   export TTS_GPU=false

   # 重启服务
   pkill -f uvicorn
   python app.py
   ```

3. **合成质量不佳**
   - 尝试不同的说话人
   - 调整语速参数 (0.5-2.0)
   - 使用更高质量的模型

4. **GPU 相关问题**
   ```bash
   # 检查CUDA版本
   nvidia-smi

   # 安装正确的PyTorch版本
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
   ```

## 📊 性能优化

### GPU 加速
```bash
export TTS_GPU=true
export CUDA_VISIBLE_DEVICES=0  # 指定GPU设备
```

### 内存优化
- 使用CPU模式可以减少内存使用
- 避免并发大量请求
- 定期重启服务释放内存

### 模型选择
- 对于实时应用：选择较快的模型
- 对于高质量要求：选择XTTS v2等高级模型
- 对于特定语言：选择专用语言模型

## 🎯 高级用法

### 声音克隆

1. 准备说话人音频文件（3-10秒，清澈的语音）
2. 设置环境变量：
   ```bash
   export TTS_SPEAKER_WAV=./my_speaker.wav
   ```
3. 重启服务

### 批量处理

```python
import requests
import time

def batch_tts(texts, output_dir="./tts_outputs"):
    """批量生成语音文件"""
    url = "http://localhost:8000/tts/synthesize/file"

    for i, text in enumerate(texts):
        data = {
            "text": text,
            "output_filename": f"speech_{i+1}.wav",
            "language": "zh-cn"
        }

        response = requests.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                print(f"✅ 生成第{i+1}个文件成功")
            else:
                print(f"❌ 生成第{i+1}个文件失败: {result['error']}")
        else:
            print(f"❌ 请求失败: {response.status_code}")

        time.sleep(0.5)  # 避免请求过于频繁

# 使用示例
texts = [
    "这是第一个句子。",
    "这是第二个句子。",
    "这是第三个句子。"
]

batch_tts(texts)
```

## 📞 支持

如果遇到问题，请：

1. 查看服务日志
2. 运行测试脚本 `python test_tts.py`
3. 检查防火墙和网络设置
4. 查看 [常见问题解答](../README.md#故障排除)

---

**🎉 恭喜！** 您已经成功部署了 Coqui TTS 服务。现在您可以将文字转换为高质量的语音了！
