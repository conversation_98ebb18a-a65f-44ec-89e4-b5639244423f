# Faster-Whisper 服务配置文件
# 复制此文件为 .env 并根据需要修改配置

# 服务器配置
HOST=0.0.0.0
PORT=8000

# Whisper 模型配置
# 模型大小: tiny, base, small, medium, large-v2, large-v3
WHISPER_MODEL_SIZE=base

# 设备配置: auto, cpu, cuda
WHISPER_DEVICE=auto

# 计算类型: default, int8, float16
WHISPER_COMPUTE_TYPE=default

# 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# 文件上传限制 (字节)
MAX_FILE_SIZE=26214400

# =============================================================================
# 模型本地存储配置
# =============================================================================
# Whisper 模型本地目录 - 优先从此目录加载 Whisper 模型，没有再联网下载
WHISPER_LOCAL_MODEL_DIR=./models/whisper

# TTS 模型本地目录 - 优先从此目录加载 TTS 模型，没有再联网下载
TTS_LOCAL_MODEL_DIR=./models/tts

# 通用本地模型目录（兼容性配置）- 如果上面两个目录未设置，将使用此目录
LOCAL_MODEL_DIR=./models

# Whisper 模型下载目录（高级配置）
WHISPER_DOWNLOAD_ROOT=./models/whisper

# TTS 默认下载目录（通常由 TTS 库管理）
# TTS_MODELS_DIR=./models/tts

# TTS 配置 - 支持中英文双模型
# =============================================================================
# 英文TTS配置
# =============================================================================
# 英文TTS模型名称
TTS_EN_MODEL_NAME=tts_models/en/ljspeech/tacotron2-DDC_ph

# 英文TTS是否使用GPU
TTS_EN_GPU=false

# 英文TTS默认语言
TTS_EN_LANGUAGE=en

# =============================================================================
# 中文TTS配置
# =============================================================================
# 中文TTS模型名称（推荐使用中文专用模型）
TTS_ZH_MODEL_NAME=tts_models/zh-CN/baker/tacotron2-DDC-GST

# 中文TTS是否使用GPU
TTS_ZH_GPU=false

# 中文TTS默认语言
TTS_ZH_LANGUAGE=zh-cn

# =============================================================================
# 兼容性配置（向后兼容）
# =============================================================================
# 原有TTS配置（自动指向英文TTS）
TTS_MODEL_NAME=tts_models/en/ljspeech/tacotron2-DDC_ph
TTS_GPU=false
TTS_LANGUAGE=en

# TTS说话人音频文件路径（可选，用于声音克隆）
# TTS_SPEAKER_WAV=./speaker.wav

# TTS输出目录
TTS_OUTPUT_DIR=./tts_outputs
