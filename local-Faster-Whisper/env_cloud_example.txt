# 云主机专用环境配置文件
# 复制此文件为 .env 并根据需要修改配置
# 此配置针对网络受限的云主机环境优化

# 服务器配置
HOST=0.0.0.0
PORT=8001

# Whisper 模型配置 - 使用本地模型
WHISPER_MODEL_SIZE=small
WHISPER_DEVICE=cpu
WHISPER_COMPUTE_TYPE=int8

# 本地模型目录配置 - 云主机必须使用本地模型
WHISPER_LOCAL_MODEL_DIR=./models
LOCAL_MODEL_DIR=./models
WHISPER_DOWNLOAD_ROOT=./models

# TTS 模型本地目录
TTS_LOCAL_MODEL_DIR=./models/tts

# TTS 配置 - 支持中英文双模型
# 英文TTS配置
TTS_EN_MODEL_NAME=tts_models/en/ljspeech/tacotron2-DDC_ph
TTS_EN_GPU=false
TTS_EN_LANGUAGE=en

# 中文TTS配置
TTS_ZH_MODEL_NAME=tts_models/zh-CN/baker/tacotron2-DDC-GST
TTS_ZH_GPU=false
TTS_ZH_LANGUAGE=zh-cn

# 兼容性配置
TTS_MODEL_NAME=tts_models/en/ljspeech/tacotron2-DDC_ph
TTS_GPU=false
TTS_LANGUAGE=en

# 输出目录
TTS_OUTPUT_DIR=./tts_outputs

# 日志配置
LOG_LEVEL=INFO

# 文件上传限制
MAX_FILE_SIZE=26214400
