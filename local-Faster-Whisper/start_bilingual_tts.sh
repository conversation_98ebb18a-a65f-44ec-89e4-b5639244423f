#!/bin/bash
# 中英文双模型TTS服务启动脚本
# 启动支持中英文的语音AI服务

echo "🚀 启动中英文双模型TTS服务..."
echo "📝 英文模型: tts_models/en/ljspeech/tacotron2-DDC_ph"
echo "📝 中文模型: tts_models/zh-CN/baker/tacotron2-DDC-GST"
echo ""

# 设置环境变量
export TTS_EN_MODEL_NAME="tts_models/en/ljspeech/tacotron2-DDC_ph"
export TTS_EN_GPU="false"
export TTS_EN_LANGUAGE="en"

export TTS_ZH_MODEL_NAME="tts_models/zh-CN/baker/tacotron2-DDC-GST"
export TTS_ZH_GPU="false"
export TTS_ZH_LANGUAGE="zh-cn"

# 激活虚拟环境
if [ -f ".venv/bin/activate" ]; then
    source .venv/bin/activate
    echo "✅ 已激活虚拟环境"
else
    echo "⚠️  未找到虚拟环境，请手动激活"
fi

# 创建输出目录
mkdir -p tts_outputs
echo "📂 已创建输出目录: tts_outputs"

# 启动服务
echo ""
echo "🌐 启动FastAPI服务..."
echo "📡 服务地址: http://0.0.0.0:8001"
echo "📖 API文档: http://localhost:8001/docs"
echo ""

uvicorn app_new:app --host 0.0.0.0 --port 8001 --log-level info --reload

echo ""
echo "🎉 服务已停止"
