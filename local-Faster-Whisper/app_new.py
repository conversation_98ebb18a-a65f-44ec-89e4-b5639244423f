# app.py
import os
import uuid
import logging
import sys
import shutil
from contextlib import asynccontextmanager  # 1.【修改】导入 asynccontextmanager
from fastapi import FastAPI, UploadFile, File, HTTPException, Request, Form, WebSocket, WebSocketDisconnect
try:
    # Optional: state enums for cleaner disconnect checks (Starlette >=0.27)
    from starlette.websockets import WebSocketState
except Exception:
    WebSocketState = None
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from faster_whisper import WhisperModel
from tts_service import TTSTextToSpeechService
from typing import Optional

# ... (从你的启动脚本中获取配置)
MODEL_SIZE = os.getenv("WHISPER_MODEL_SIZE", "tiny")
DEVICE = os.getenv("WHISPER_DEVICE", "cpu")

# =============================================================================
# 模型本地存储配置
# =============================================================================
# Whisper 模型本地目录
WHISPER_LOCAL_MODEL_DIR = os.getenv("WHISPER_LOCAL_MODEL_DIR", "")
WHISPER_DOWNLOAD_ROOT = os.getenv("WHISPER_DOWNLOAD_ROOT", "")

# TTS 模型本地目录
TTS_LOCAL_MODEL_DIR = os.getenv("TTS_LOCAL_MODEL_DIR", "")

# 通用本地模型目录（兼容性配置）
LOCAL_MODEL_DIR = os.getenv("LOCAL_MODEL_DIR", "./models")

# 调试开关：如果设置为 1/true/yes，会把收到的音频另存到 debug_uploads 并保留文件
DEBUG_SAVE_AUDIO = "1"

# TTS 配置 - 支持中英文双模型
# 英文TTS配置
TTS_EN_MODEL_NAME = os.getenv("TTS_EN_MODEL_NAME", "tts_models/en/ljspeech/tacotron2-DDC_ph")
TTS_EN_GPU = os.getenv("TTS_EN_GPU", "false").lower() == "true"
TTS_EN_LANGUAGE = os.getenv("TTS_EN_LANGUAGE", "en")

# 中文TTS配置
TTS_ZH_MODEL_NAME = os.getenv("TTS_ZH_MODEL_NAME", "tts_models/zh-CN/baker/tacotron2-DDC-GST")
TTS_ZH_GPU = os.getenv("TTS_ZH_GPU", "false").lower() == "true"
TTS_ZH_LANGUAGE = os.getenv("TTS_ZH_LANGUAGE", "zh-cn")

# 兼容性配置（向后兼容）
TTS_MODEL_NAME = os.getenv("TTS_MODEL_NAME", TTS_EN_MODEL_NAME)
TTS_GPU = os.getenv("TTS_GPU", "false").lower() == "true"
TTS_LANGUAGE = os.getenv("TTS_LANGUAGE", TTS_EN_LANGUAGE)
TTS_SPEAKER_WAV = os.getenv("TTS_SPEAKER_WAV")

# 配置日志 - 确保所有日志都能输出
logging.basicConfig(
    level=logging.INFO,  # 设置为 INFO 级别，确保所有重要日志都输出
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),  # 输出到控制台
        logging.FileHandler('whisper_service.log', encoding='utf-8')  # 同时输出到文件
    ]
)
logger = logging.getLogger(__name__)

# 打印 Hugging Face 端点与联网补齐配置，便于排查
HF_ENDPOINT = os.getenv("HF_ENDPOINT")
if HF_ENDPOINT:
    logger.info(f"使用 Hugging Face 端点: {HF_ENDPOINT}")
ALLOW_HF_DOWNLOAD_FLAG = str(os.getenv("ALLOW_HF_DOWNLOAD", "0")).lower() in ("1", "true", "yes", "on")
logger.info(f"允许联网补齐缺失快照: {ALLOW_HF_DOWNLOAD_FLAG}")

# 2.【修改】定义新的 lifespan 管理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    # --- 启动逻辑 (原 startup_event 的内容) ---
    logger.info(f"正在加载模型 {MODEL_SIZE} 到设备 {DEVICE}...")

    # 确定 Whisper 模型的本地目录
    if WHISPER_LOCAL_MODEL_DIR:
        whisper_model_dir = WHISPER_LOCAL_MODEL_DIR
        logger.info(f"使用专门的 Whisper 模型目录: {whisper_model_dir}")
    else:
        whisper_model_dir = LOCAL_MODEL_DIR
        logger.info(f"使用通用模型目录作为 Whisper 目录: {whisper_model_dir}")

    # 确定 Whisper 下载目录
    if WHISPER_DOWNLOAD_ROOT:
        whisper_download_root = WHISPER_DOWNLOAD_ROOT
    else:
        whisper_download_root = whisper_model_dir

    try:
        # 确保 Whisper 模型目录存在
        os.makedirs(whisper_model_dir, exist_ok=True)
        if whisper_download_root != whisper_model_dir:
            os.makedirs(whisper_download_root, exist_ok=True)

        # 先尝试从本地目录加载模型，没有再联网下载
        logger.info(f"尝试从本地目录加载 Whisper 模型: {whisper_model_dir}")

        try:
            # 设置 download_root 为本地目录，优先使用本地模型
            app.state.model = WhisperModel(
                MODEL_SIZE,
                device=DEVICE,
                compute_type="int8",
                download_root=whisper_download_root,
                local_files_only=True  # 只使用本地模型，不尝试网络下载
            )
            logger.info(f"Whisper 模型加载成功！使用目录: {whisper_download_root}")
        except Exception as local_e:
            logger.warning(f"从本地目录加载失败: {local_e}")
            # 尝试使用默认缓存目录，但也只使用本地文件
            try:
                app.state.model = WhisperModel(
                    MODEL_SIZE,
                    device=DEVICE,
                    compute_type="int8",
                    local_files_only=True  # 只使用本地模型，不尝试网络下载
                )
                logger.info("Whisper 模型从默认缓存目录加载成功！")
            except Exception as cache_e:
                logger.error(f"从默认缓存目录加载也失败: {cache_e}")
                # 可选：允许联网一次以补全快照（通过环境变量 ALLOW_HF_DOWNLOAD=1 控制）
                if ALLOW_HF_DOWNLOAD_FLAG:
                    logger.warning("允许联网下载缺失快照（ALLOW_HF_DOWNLOAD=1）…")
                    try:
                        app.state.model = WhisperModel(
                            MODEL_SIZE,
                            device=DEVICE,
                            compute_type="int8",
                            download_root=whisper_download_root,
                            local_files_only=False  # 允许联网补齐
                        )
                        logger.info("Whisper 模型通过联网补齐后加载成功！")
                    except Exception as net_e:
                        logger.error(f"联网补齐失败: {net_e}")
                        raise RuntimeError(f"无法加载 Whisper 模型，已尝试本地、默认缓存与可选联网方式") from net_e
                else:
                    raise RuntimeError(f"无法加载 Whisper 模型，已尝试本地目录和默认缓存目录") from cache_e

    except Exception as e:
        logger.error(f"Whisper 模型加载失败: {e}")
        # 如果模型加载失败，应用将无法启动
        raise RuntimeError(f"模型 {MODEL_SIZE} 加载失败") from e

    # 确定 TTS 模型的本地目录
    if TTS_LOCAL_MODEL_DIR:
        tts_model_dir = TTS_LOCAL_MODEL_DIR
        logger.info(f"使用专门的 TTS 模型目录: {tts_model_dir}")
    else:
        tts_model_dir = LOCAL_MODEL_DIR
        logger.info(f"使用通用模型目录作为 TTS 目录: {tts_model_dir}")

    # 确保 TTS 模型目录存在
    os.makedirs(tts_model_dir, exist_ok=True)

    # 初始化英文/中文TTS服务（支持惰性加载与线程限制）
    TTS_LAZY_INIT = str(os.getenv("TTS_LAZY_INIT", "0")).lower() in ("1", "true", "yes", "on")
    TTS_NUM_THREADS = os.getenv("TTS_NUM_THREADS")
    if TTS_LAZY_INIT:
        logger.info("TTS采用惰性加载（TTS_LAZY_INIT=1），在首次调用时再初始化模型")
    if TTS_NUM_THREADS:
        logger.info(f"TTS_NUM_THREADS={TTS_NUM_THREADS}")

    # 初始化英文TTS服务
    logger.info(f"正在初始化英文TTS服务，模型: {TTS_EN_MODEL_NAME}, GPU: {TTS_EN_GPU}")
    try:
        app.state.tts_en = TTSTextToSpeechService(
            model_name=TTS_EN_MODEL_NAME,
            gpu=TTS_EN_GPU,
            language=TTS_EN_LANGUAGE,
            local_model_dir=tts_model_dir,
            auto_init=not TTS_LAZY_INIT
        )
        logger.info("英文TTS服务初始化成功！")
    except Exception as e:
        logger.error(f"英文TTS服务初始化失败: {e}")
        app.state.tts_en = None

    # 初始化中文TTS服务
    logger.info(f"正在初始化中文TTS服务，模型: {TTS_ZH_MODEL_NAME}, GPU: {TTS_ZH_GPU}")
    try:
        app.state.tts_zh = TTSTextToSpeechService(
            model_name=TTS_ZH_MODEL_NAME,
            gpu=TTS_ZH_GPU,
            language=TTS_ZH_LANGUAGE,
            local_model_dir=tts_model_dir,
            auto_init=not TTS_LAZY_INIT
        )
        logger.info("中文TTS服务初始化成功！")
    except Exception as e:
        logger.error(f"中文TTS服务初始化失败: {e}")
        app.state.tts_zh = None

    # 兼容性：将通用 tts 服务默认指向中文（若中文不可用则退回英文）
    if app.state.tts_zh is not None:
        app.state.tts = app.state.tts_zh
        logger.info("通用 TTS 服务已默认指向中文服务 (tts_zh)")
    else:
        app.state.tts = app.state.tts_en
        logger.info("中文 TTS 不可用，通用 TTS 回退指向英文服务 (tts_en)")

    yield  # 这行代码之前是启动部分，之后是关闭部分

    # --- 关闭逻辑 (可选，用于清理资源) ---
    logger.info("服务关闭，正在清理资源...")
    # 可以在这里添加清理代码，例如释放模型占用的内存
    app.state.model = None
    if hasattr(app.state, 'tts_en') and app.state.tts_en:
        app.state.tts_en = None
    if hasattr(app.state, 'tts_zh') and app.state.tts_zh:
        app.state.tts_zh = None
    # 兼容性清理
    if hasattr(app.state, 'tts') and app.state.tts:
        app.state.tts = None
    # import torch; torch.cuda.empty_cache() # 如果使用GPU，可以加上这句


# 3.【修改】创建 FastAPI 实例时，关联 lifespan
app = FastAPI(lifespan=lifespan)

# 开发环境放开 CORS（含 WS 握手）。注意：allow_credentials=False 才能配合 "*"。
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ------------------------------------------------------------------
# 【删除】下面这段旧的代码需要被完全删除或注释掉
# @app.on_event("startup")
# async def startup_event():
#     logger.info(f"正在加载模型 {MODEL_SIZE} 到设备 {DEVICE}...")
#     try:
#         app.state.model = WhisperModel(MODEL_SIZE, device=DEVICE, compute_type="int8")
#         logger.info("模型加载成功！")
#     except Exception as e:
#         logger.error(f"模型加载失败: {e}")
#         raise RuntimeError(f"模型 {MODEL_SIZE} 加载失败")
# ------------------------------------------------------------------


# --- API 端点 (这部分代码无需修改) ---
@app.get("/")
async def root():
    """根路径，返回服务信息"""
    return {
        "service": "Faster-Whisper 语音识别与 TTS 服务",
        "version": "1.0.0",
        "status": "running",
        "services": {
            "whisper": "语音识别服务 (ASR)",
            "tts": "文字转语音服务"
        },
        "endpoints": {
            "whisper": {
                "transcribe": "/transcribe - POST 上传音频文件进行转录",
                "ws_stream": "/ws/stream - WebSocket 实时流式转录"
            },
            "tts": {
                "synthesize": "/tts/synthesize - POST 将文字合成语音",
                "synthesize_to_file": "/tts/synthesize/file - POST 合成语音并保存为文件",
                "models": "/tts/models - GET 获取可用TTS模型列表",
                "languages": "/tts/languages - GET 获取TTS支持的语言列表",
                "speakers": "/tts/speakers - GET 获取可用说话人列表",
                "info": "/tts/info - GET 获取TTS模型信息"
            },
            "system": {
                "health": "/health - GET 健康检查"
            }
        }
    }


@app.post("/transcribe")
async def transcribe_audio(request: Request, audio_file: UploadFile = File(...)):
    if not hasattr(app.state, 'model') or app.state.model is None:
        raise HTTPException(status_code=503, detail="模型正在加载或加载失败，请稍后再试")
        
    temp_dir = "uploads"
    os.makedirs(temp_dir, exist_ok=True)
    # 根据上传文件名/Content-Type 决定保存扩展名，避免把 WAV 错存为 webm
    name_ext = os.path.splitext(audio_file.filename or "")[1].lower()
    ct = (audio_file.content_type or "").lower()
    ct_map = {
        "audio/wav": ".wav",
        "audio/x-wav": ".wav",
        "audio/webm": ".webm",
        "audio/ogg": ".ogg",
        "audio/mpeg": ".mp3",
        "audio/mp4": ".m4a",
        "video/mp4": ".mp4",
    }
    ext = name_ext if name_ext in {".wav", ".webm", ".ogg", ".mp3", ".m4a", ".mp4"} else ct_map.get(ct, ".wav")
    temp_filename = os.path.join(temp_dir, f"{uuid.uuid4()}{ext}")

    try:
        with open(temp_filename, "wb") as f:
            content = await audio_file.read()
            f.write(content)
        # 如果开启调试保存，复制一份到 debug_uploads 目录，保留原始文件名以便排查
        if DEBUG_SAVE_AUDIO:
            debug_dir = "debug_uploads"
            os.makedirs(debug_dir, exist_ok=True)
            safe_name = f"debug_{uuid.uuid4()}_{audio_file.filename}"
            debug_path = os.path.join(debug_dir, safe_name)
            try:
                shutil.copyfile(temp_filename, debug_path)
                logger.info(f"已保存调试音频到 {debug_path}")
            except Exception as e:
                logger.warning(f"调试音频保存失败: {e}")
        
        logger.info(f"收到音频文件: {audio_file.filename}, 类型: {audio_file.content_type}, 大小: {len(content)} bytes -> 临时保存为: {temp_filename}")
        
        # 读取可选语言参数（language/lang/language_code/locale）；中文场景可强制 'zh'
        try:
            form = await request.form()
        except Exception:
            form = {}
        lang = (form.get('language') or form.get('lang') or form.get('language_code') or form.get('locale'))
        if isinstance(lang, str):
            lang = lang.strip()
            low = lang.lower()
            if low.startswith('zh'):
                lang = 'zh'
            elif low.startswith('en'):
                lang = 'en'
            elif low in ('auto', 'detect'):
                lang = None
        else:
            lang = None
        
        # 让 faster-whisper 直接吃我们保存的文件；如传入语言可提升稳定性
        if lang:
            segments, info = app.state.model.transcribe(temp_filename, beam_size=5, language=lang, vad_filter=True)
        else:
            segments, info = app.state.model.transcribe(temp_filename, beam_size=5, vad_filter=True)
        
        logger.info(f"检测到语言 '{info.language}'，概率 {info.language_probability}")
        
        result_text = "".join(segment.text for segment in segments)
        
        logger.info(f"识别结果: {result_text}")
        
        return {"text": result_text}

    except Exception as e:
        logger.error(f"处理音频时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理音频时发生内部错误: {e}")
    finally:
        # 根据调试开关决定是否删除临时文件
        if os.path.exists(temp_filename):
            if DEBUG_SAVE_AUDIO:
                logger.info(f"DEBUG_SAVE_AUDIO 开启，保留临时文件 {temp_filename}")
            else:
                try:
                    os.remove(temp_filename)
                except Exception as e:
                    logger.warning(f"删除临时文件失败: {e}")


# ------------------------------
# WebSocket 实时流式端点
# 前端协议：
#  - 首包：text JSON => { event: 'start', sampleRate: 16000, language: 'zh' }
#  - 音频流：binary => Int16 PCM 小端，单声道，持续发送
#  - 结束：text JSON => { event: 'end' }
#  - 服务器：
#     * 接收 start 后返回 { event: 'ack' }
#     * 收到 end 后转录累计音频，返回 { event: 'final', text }
#     * 错误：{ event: 'error', message }
# ------------------------------

@app.websocket("/ws/stream")
async def ws_stream(websocket: WebSocket):
    await websocket.accept()
    logger.info("WebSocket /ws/stream 已连接")

    buffer = bytearray()
    sample_rate = 16000
    language = None
    total_bytes = 0

    try:
        # 等待 start
        while True:
            # 若对端已断开，避免继续调用 receive()
            try:
                if WebSocketState and getattr(websocket, 'application_state', None) == WebSocketState.DISCONNECTED:
                    logger.info("WebSocket 已断开（在 start 阶段）")
                    return
            except Exception:
                pass
            try:
                msg = await websocket.receive()
            except RuntimeError as e:
                if 'disconnect' in str(e).lower():
                    logger.info("WebSocket 已断开（start 阶段 RuntimeError）")
                    return
                raise
            if "text" in msg and msg["text"]:
                import json
                try:
                    data = json.loads(msg["text"])
                except Exception:
                    await websocket.send_text('{"event":"error","message":"invalid json"}')
                    continue
                evt = (data.get("event") or "").lower()
                if evt == "start":
                    sr = data.get("sampleRate") or data.get("sample_rate")
                    if isinstance(sr, int) and sr > 0:
                        sample_rate = sr
                    lang = data.get("language") or data.get("lang")
                    if isinstance(lang, str) and lang.strip():
                        low = lang.strip().lower()
                        if low.startswith('zh'):
                            language = 'zh'
                        elif low.startswith('en'):
                            language = 'en'
                        elif low in ('auto', 'detect'):
                            language = None
                        else:
                            language = lang.strip()
                    logger.info(f"WS start: sample_rate={sample_rate}, language={language}")
                    await websocket.send_text('{"event":"ack"}')
                    break
                else:
                    await websocket.send_text('{"event":"error","message":"expected start"}')
            else:
                # 非 text 的第一包，忽略直到收到 start
                continue

        # 接收音频与 end
        while True:
            try:
                msg = await websocket.receive()
            except RuntimeError as e:
                if 'disconnect' in str(e).lower():
                    logger.info("WebSocket 已断开（stream 阶段 RuntimeError）")
                    return
                raise
            if "bytes" in msg and msg["bytes"]:
                # 直接累加 Int16 PCM 原始字节
                chunk = msg["bytes"]
                buffer.extend(chunk)
                total_bytes += len(chunk)
                if total_bytes % (16000 * 2 * 2) < len(chunk):  # 每约2秒打印一次(16kHz*2bytes*2s)
                    logger.info(f"WS audio received: total_bytes={total_bytes}")
            elif "text" in msg and msg["text"]:
                import json
                try:
                    data = json.loads(msg["text"])
                except Exception:
                    await websocket.send_text('{"event":"error","message":"invalid json"}')
                    continue
                evt = (data.get("event") or "").lower()
                if evt == "end":
                    # 将累计的 PCM 写成临时 WAV 文件并转录
                    import tempfile, wave
                    try:
                        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp:
                            tmp_path = tmp.name
                        with wave.open(tmp_path, "wb") as wf:
                            wf.setnchannels(1)
                            wf.setsampwidth(2)  # 16-bit
                            wf.setframerate(sample_rate)
                            wf.writeframes(bytes(buffer))

                        logger.info(f"WS end: writing WAV, bytes={total_bytes}, path={tmp_path}")

                        if not hasattr(app.state, 'model') or app.state.model is None:
                            await websocket.send_text('{"event":"error","message":"model not ready"}')
                        else:
                            if language:
                                segments, info = app.state.model.transcribe(tmp_path, beam_size=5, language=language, vad_filter=True)
                            else:
                                segments, info = app.state.model.transcribe(tmp_path, beam_size=5, vad_filter=True)
                            result_text = "".join(seg.text for seg in segments)
                            logger.info(f"WS transcription done. lang={getattr(info,'language',None)}, prob={getattr(info,'language_probability',None)} text={result_text}")
                            await websocket.send_text(
                                '{"event":"final","text":' + __import__('json').dumps(result_text, ensure_ascii=False) + '}'
                            )
                    except Exception as e:
                        logger.exception("WS 处理失败")
                        await websocket.send_text('{"event":"error","message":' + __import__('json').dumps(str(e)) + '}')
                    finally:
                        # 如需保存调试音频，复制到 debug_uploads
                        try:
                            save_flag = str(os.getenv("DEBUG_SAVE_AUDIO", "1" if DEBUG_SAVE_AUDIO else "0")).lower() in ("1","true","yes","on")
                        except Exception:
                            save_flag = False
                        if save_flag:
                            try:
                                debug_dir = "debug_uploads"
                                os.makedirs(debug_dir, exist_ok=True)
                                safe_name = f"ws_debug_{uuid.uuid4()}_{sample_rate}hz_{total_bytes}b.wav"
                                debug_path = os.path.join(debug_dir, safe_name)
                                shutil.copyfile(tmp_path, debug_path)
                                logger.info(f"WS 调试音频已保存: {debug_path}")
                            except Exception as e:
                                logger.warning(f"WS 调试音频保存失败: {e}")
                        try:
                            if 'tmp_path' in locals() and os.path.exists(tmp_path):
                                # 若开启调试保存，则保留原始临时文件；否则删除
                                if not save_flag:
                                    os.remove(tmp_path)
                        except Exception:
                            pass
                    # 正常结束
                    await websocket.close()
                    break
                else:
                    # 其它事件忽略
                    continue

    except WebSocketDisconnect:
        logger.info("WebSocket 断开连接")
    except RuntimeError as e:
        # Starlette 在收到断开后再次调用 receive() 会抛出 RuntimeError
        if 'disconnect' in str(e).lower():
            logger.info("WebSocket 已断开（RuntimeError: disconnect），停止接收")
        else:
            logger.exception("WebSocket 运行时异常")
        try:
            await websocket.close()
        except Exception:
            pass
    except Exception as e:
        logger.exception("WebSocket 会话异常")
        try:
            await websocket.send_text('{"event":"error","message":' + __import__('json').dumps(str(e)) + '}')
        except Exception:
            pass
        try:
            await websocket.close()
        except Exception:
            pass


# =========================== TTS 接口 ===========================

# ------------------------------------------------------------
# Aliases to match Nginx proxy prefix (/whisper)
# These allow hitting /whisper/* directly even without proxy rewrite
# ------------------------------------------------------------
@app.post("/whisper/transcribe")
async def whisper_transcribe(request: Request, audio_file: UploadFile = File(...)):
    return await transcribe_audio(request, audio_file)


@app.websocket("/whisper/ws/stream")
async def whisper_ws_stream(websocket: WebSocket):
    # Reuse the same handler as /ws/stream
    await ws_stream(websocket)

class TTSSynthesisRequest(BaseModel):
    """TTS合成请求模型"""
    text: str
    speaker: Optional[str] = None
    language: Optional[str] = None
    speed: float = 1.0


class TTSSynthesisFileRequest(BaseModel):
    """TTS合成文件请求模型"""
    text: str
    output_filename: str
    speaker: Optional[str] = None
    language: Optional[str] = None
    speed: float = 1.0


@app.get("/tts/info")
async def get_tts_model_info():
    """获取TTS模型信息"""
    if not hasattr(app.state, 'tts') or app.state.tts is None:
        raise HTTPException(status_code=503, detail="TTS服务未初始化")

    try:
        info = app.state.tts.get_model_info()
        return {
            "success": True,
            "data": info
        }
    except Exception as e:
        logger.error(f"获取TTS模型信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取TTS模型信息失败: {e}")


@app.get("/tts/models")
async def get_available_tts_models():
    """获取可用TTS模型列表"""
    if not hasattr(app.state, 'tts') or app.state.tts is None:
        raise HTTPException(status_code=503, detail="TTS服务未初始化")

    try:
        models = app.state.tts.get_available_models()
        return {
            "success": True,
            "data": {
                "models": models,
                "current_model": app.state.tts.model_name
            }
        }
    except Exception as e:
        logger.error(f"获取TTS模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取TTS模型列表失败: {e}")


@app.get("/tts/languages")
async def get_tts_supported_languages():
    """获取TTS支持的语言列表"""
    if not hasattr(app.state, 'tts') or app.state.tts is None:
        raise HTTPException(status_code=503, detail="TTS服务未初始化")

    try:
        languages = app.state.tts.get_supported_languages()
        return {
            "success": True,
            "data": {
                "languages": languages,
                "current_language": app.state.tts.language
            }
        }
    except Exception as e:
        logger.error(f"获取TTS语言列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取TTS语言列表失败: {e}")


@app.get("/tts/speakers")
async def get_tts_speakers():
    """获取可用说话人列表"""
    if not hasattr(app.state, 'tts') or app.state.tts is None:
        raise HTTPException(status_code=503, detail="TTS服务未初始化")

    try:
        speakers = app.state.tts.get_speakers()
        return {
            "success": True,
            "data": {
                "speakers": speakers
            }
        }
    except Exception as e:
        logger.error(f"获取TTS说话人列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取TTS说话人列表失败: {e}")


@app.post("/tts/synthesize")
async def synthesize_speech(request: TTSSynthesisRequest):
    """
    将文字合成语音并返回音频数据

    请求体参数:
    - text: 要合成的文字
    - speaker: 说话人（可选）
    - language: 语言（可选）
    - speed: 语速（0.5-2.0之间，默认1.0）
    """
    if not hasattr(app.state, 'tts') or app.state.tts is None:
        raise HTTPException(status_code=503, detail="TTS服务未初始化")

    try:
        # 执行语音合成
        result = app.state.tts.synthesize_to_bytes(
            text=request.text,
            speaker=request.speaker,
            language=request.language,
            speed=request.speed
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        # 返回音频数据
        from fastapi.responses import Response
        audio_data = result["data"]["audio_bytes"]

        return Response(
            content=audio_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=speech.wav",
                "X-Language": result["data"]["language"],
                "X-Speed": str(result["data"]["speed"])
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"TTS合成失败: {e}")
        raise HTTPException(status_code=500, detail=f"TTS合成失败: {str(e)}")


@app.post("/tts/synthesize/file")
async def synthesize_speech_to_file(request: TTSSynthesisFileRequest):
    """
    将文字合成语音并保存为文件

    请求体参数:
    - text: 要合成的文字
    - output_filename: 输出文件名
    - speaker: 说话人（可选）
    - language: 语言（可选）
    - speed: 语速（0.5-2.0之间，默认1.0）
    """
    if not hasattr(app.state, 'tts') or app.state.tts is None:
        raise HTTPException(status_code=503, detail="TTS服务未初始化")

    try:
        # 创建输出目录
        output_dir = "tts_outputs"
        os.makedirs(output_dir, exist_ok=True)

        # 构建输出文件路径
        output_path = os.path.join(output_dir, request.output_filename)

        # 检查文件是否已存在
        if os.path.exists(output_path):
            # 如果文件已存在，添加时间戳避免覆盖
            import time
            name, ext = os.path.splitext(request.output_filename)
            timestamp = int(time.time())
            output_filename = f"{name}_{timestamp}{ext}"
            output_path = os.path.join(output_dir, output_filename)

        # 执行语音合成
        result = app.state.tts.synthesize_to_file(
            text=request.text,
            output_path=output_path,
            speaker=request.speaker,
            language=request.language,
            speed=request.speed
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        # 更新结果中的文件路径为相对路径
        result["data"]["output_path"] = output_path
        result["data"]["filename"] = os.path.basename(output_path)

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"TTS合成到文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"TTS合成到文件失败: {str(e)}")


# =============================================================================
# 中英文TTS专用接口
# =============================================================================

# 英文TTS接口
@app.post("/tts/en/synthesize")
async def synthesize_english_speech(request: TTSSynthesisRequest):
    """
    英文TTS语音合成接口

    请求体参数:
    - text: 要合成的英文文字
    - speaker: 说话人（可选）
    - language: 语言（可选，默认en）
    - speed: 语速（0.5-2.0之间，默认1.0）
    """
    if not hasattr(app.state, 'tts_en') or app.state.tts_en is None:
        raise HTTPException(status_code=503, detail="英文TTS服务未初始化")
    # 惰性加载：确保已初始化
    try:
        if not app.state.tts_en.is_initialized:
            app.state.tts_en.ensure_initialized()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"英文TTS初始化失败: {e}")

    try:
        # 执行英文语音合成
        result = app.state.tts_en.synthesize_to_bytes(
            text=request.text,
            speaker=request.speaker,
            language=request.language or "en",
            speed=request.speed
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        # 返回音频数据
        from fastapi.responses import Response
        audio_data = result["data"]["audio_bytes"]

        return Response(
            content=audio_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=english_speech.wav",
                "X-Language": result["data"]["language"],
                "X-Speed": str(result["data"]["speed"])
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"英文TTS合成失败: {e}")
        raise HTTPException(status_code=500, detail=f"英文TTS合成失败: {str(e)}")


@app.post("/tts/en/synthesize/file")
async def synthesize_english_speech_to_file(request: TTSSynthesisFileRequest):
    """
    英文TTS语音合成并保存为文件接口

    请求体参数:
    - text: 要合成的英文文字
    - output_filename: 输出文件名
    - speaker: 说话人（可选）
    - language: 语言（可选，默认en）
    - speed: 语速（0.5-2.0之间，默认1.0）
    """
    if not hasattr(app.state, 'tts_en') or app.state.tts_en is None:
        raise HTTPException(status_code=503, detail="英文TTS服务未初始化")
    try:
        if not app.state.tts_en.is_initialized:
            app.state.tts_en.ensure_initialized()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"英文TTS初始化失败: {e}")

    try:
        # 创建输出目录
        output_dir = "tts_outputs"
        os.makedirs(output_dir, exist_ok=True)

        # 构建输出文件路径
        output_path = os.path.join(output_dir, request.output_filename)

        # 检查文件是否已存在
        if os.path.exists(output_path):
            import time
            name, ext = os.path.splitext(request.output_filename)
            timestamp = int(time.time())
            output_filename = f"{name}_{timestamp}{ext}"
            output_path = os.path.join(output_dir, output_filename)

        # 执行英文语音合成
        result = app.state.tts_en.synthesize_to_file(
            text=request.text,
            output_path=output_path,
            speaker=request.speaker,
            language=request.language or "en",
            speed=request.speed
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        # 返回文件信息
        file_size = os.path.getsize(output_path)
        return {
            "success": True,
            "data": {
                "file_path": output_path,
                "file_size": file_size,
                "language": result["data"]["language"],
                "speed": result["data"]["speed"]
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"英文TTS文件合成失败: {e}")
        raise HTTPException(status_code=500, detail=f"英文TTS文件合成失败: {str(e)}")


# 中文TTS接口
@app.post("/tts/zh/synthesize")
async def synthesize_chinese_speech(request: TTSSynthesisRequest):
    """
    中文TTS语音合成接口

    请求体参数:
    - text: 要合成的中文文字
    - speaker: 说话人（可选）
    - language: 语言（可选，默认zh-cn）
    - speed: 语速（0.5-2.0之间，默认1.0）
    """
    if not hasattr(app.state, 'tts_zh') or app.state.tts_zh is None:
        raise HTTPException(status_code=503, detail="中文TTS服务未初始化")
    try:
        if not app.state.tts_zh.is_initialized:
            app.state.tts_zh.ensure_initialized()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"中文TTS初始化失败: {e}")

    try:
        # 执行中文语音合成
        result = app.state.tts_zh.synthesize_to_bytes(
            text=request.text,
            speaker=request.speaker,
            language=request.language or "zh-cn",
            speed=request.speed
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        # 返回音频数据
        from fastapi.responses import Response
        audio_data = result["data"]["audio_bytes"]

        return Response(
            content=audio_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=chinese_speech.wav",
                "X-Language": result["data"]["language"],
                "X-Speed": str(result["data"]["speed"])
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"中文TTS合成失败: {e}")
        raise HTTPException(status_code=500, detail=f"中文TTS合成失败: {str(e)}")


@app.post("/tts/zh/synthesize/file")
async def synthesize_chinese_speech_to_file(request: TTSSynthesisFileRequest):
    """
    中文TTS语音合成并保存为文件接口

    请求体参数:
    - text: 要合成的中文文字
    - output_filename: 输出文件名
    - speaker: 说话人（可选）
    - language: 语言（可选，默认zh-cn）
    - speed: 语速（0.5-2.0之间，默认1.0）
    """
    if not hasattr(app.state, 'tts_zh') or app.state.tts_zh is None:
        raise HTTPException(status_code=503, detail="中文TTS服务未初始化")
    try:
        if not app.state.tts_zh.is_initialized:
            app.state.tts_zh.ensure_initialized()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"中文TTS初始化失败: {e}")

    try:
        # 创建输出目录
        output_dir = "tts_outputs"
        os.makedirs(output_dir, exist_ok=True)

        # 构建输出文件路径
        output_path = os.path.join(output_dir, request.output_filename)

        # 检查文件是否已存在
        if os.path.exists(output_path):
            import time
            name, ext = os.path.splitext(request.output_filename)
            timestamp = int(time.time())
            output_filename = f"{name}_{timestamp}{ext}"
            output_path = os.path.join(output_dir, output_filename)

        # 执行中文语音合成
        result = app.state.tts_zh.synthesize_to_file(
            text=request.text,
            output_path=output_path,
            speaker=request.speaker,
            language=request.language or "zh-cn",
            speed=request.speed
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])

        # 返回文件信息
        file_size = os.path.getsize(output_path)
        return {
            "success": True,
            "data": {
                "file_path": output_path,
                "file_size": file_size,
                "language": result["data"]["language"],
                "speed": result["data"]["speed"]
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"中文TTS文件合成失败: {e}")
        raise HTTPException(status_code=500, detail=f"中文TTS文件合成失败: {str(e)}")


# 获取中英文TTS服务状态
@app.get("/tts/status")
async def get_tts_services_status():
    """获取中英文TTS服务状态"""
    return {
        "success": True,
        "data": {
            "english_tts": {
                "status": "ready" if (hasattr(app.state, 'tts_en') and app.state.tts_en and app.state.tts_en.is_initialized) else "not_available",
                "model": TTS_EN_MODEL_NAME,
                "initialized": hasattr(app.state, 'tts_en') and app.state.tts_en and app.state.tts_en.is_initialized
            },
            "chinese_tts": {
                "status": "ready" if (hasattr(app.state, 'tts_zh') and app.state.tts_zh and app.state.tts_zh.is_initialized) else "not_available",
                "model": TTS_ZH_MODEL_NAME,
                "initialized": hasattr(app.state, 'tts_zh') and app.state.tts_zh and app.state.tts_zh.is_initialized
            }
        }
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    if not hasattr(app.state, 'model') or app.state.model is None:
        raise HTTPException(status_code=503, detail="Whisper服务未初始化")

    return {
        "status": "healthy",
        "services": {
            "whisper": {
                "status": "ready",
                "model_loaded": app.state.model is not None
            },
            "english_tts": {
                "status": "ready" if (hasattr(app.state, 'tts_en') and app.state.tts_en and app.state.tts_en.is_initialized) else "not_available",
                "model": TTS_EN_MODEL_NAME,
                "initialized": hasattr(app.state, 'tts_en') and app.state.tts_en and app.state.tts_en.is_initialized
            },
            "chinese_tts": {
                "status": "ready" if (hasattr(app.state, 'tts_zh') and app.state.tts_zh and app.state.tts_zh.is_initialized) else "not_available",
                "model": TTS_ZH_MODEL_NAME,
                "initialized": hasattr(app.state, 'tts_zh') and app.state.tts_zh and app.state.tts_zh.is_initialized
            },
            "tts": {  # 兼容性保留
                "status": "ready" if (hasattr(app.state, 'tts') and app.state.tts and app.state.tts.is_initialized) else "not_available",
                "initialized": hasattr(app.state, 'tts') and app.state.tts and app.state.tts.is_initialized
            }
        }
    }

# ------------------------------------------------------------
# Register TTS and health alias routes under /whisper
# ------------------------------------------------------------
def _register_alias_routes():
    try:
        # TTS info + lists
        app.add_api_route("/whisper/tts/info", get_tts_model_info, methods=["GET"])  # type: ignore
        app.add_api_route("/whisper/tts/models", get_available_tts_models, methods=["GET"])  # type: ignore
        app.add_api_route("/whisper/tts/languages", get_tts_supported_languages, methods=["GET"])  # type: ignore
        app.add_api_route("/whisper/tts/speakers", get_tts_speakers, methods=["GET"])  # type: ignore
        # General TTS synthesize
        app.add_api_route("/whisper/tts/synthesize", synthesize_speech, methods=["POST"])  # type: ignore
        app.add_api_route("/whisper/tts/synthesize/file", synthesize_speech_to_file, methods=["POST"])  # type: ignore
        # English / Chinese specific
        app.add_api_route("/whisper/tts/en/synthesize", synthesize_english_speech, methods=["POST"])  # type: ignore
        app.add_api_route("/whisper/tts/en/synthesize/file", synthesize_english_speech_to_file, methods=["POST"])  # type: ignore
        app.add_api_route("/whisper/tts/zh/synthesize", synthesize_chinese_speech, methods=["POST"])  # type: ignore
        app.add_api_route("/whisper/tts/zh/synthesize/file", synthesize_chinese_speech_to_file, methods=["POST"])  # type: ignore
        # Health alias
        app.add_api_route("/whisper/health", health_check, methods=["GET"])  # type: ignore
    except Exception as e:
        try:
            logger.warning(f"Alias route registration warning: {e}")
        except Exception:
            pass

_register_alias_routes()
