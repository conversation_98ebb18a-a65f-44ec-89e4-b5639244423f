#!/bin/bash

# 云主机启动脚本 - 优化本地模型加载
# 用于在网络受限的云主机环境中运行 Faster-Whisper 服务

echo "========================================="
echo "云主机 Faster-Whisper 服务启动脚本"
echo "========================================="

# 优先加载 .env（如果存在），使脚本默认值能被覆盖
if [ -f .env ]; then
  # 导出所有在 .env 中赋值的变量
  set -a
  . ./.env
  set +a
fi

# 设置环境变量 - 强制使用本地模型
export WHISPER_MODEL_SIZE="${WHISPER_MODEL_SIZE:-small}"
export WHISPER_DEVICE="${WHISPER_DEVICE:-cpu}"
export WHISPER_LOCAL_MODEL_DIR="${WHISPER_LOCAL_MODEL_DIR:-./models}"
export LOCAL_MODEL_DIR="${LOCAL_MODEL_DIR:-./models}"
export TTS_LOCAL_MODEL_DIR="${TTS_LOCAL_MODEL_DIR:-./models/tts}"

# 设置主机和端口
export HOST="${HOST:-0.0.0.0}"
export PORT="${PORT:-8001}"

echo "配置信息:"
echo "- Whisper 模型大小: $WHISPER_MODEL_SIZE"
echo "- 设备: $WHISPER_DEVICE"
echo "- 本地模型目录: $WHISPER_LOCAL_MODEL_DIR"
echo "- TTS模型目录: $TTS_LOCAL_MODEL_DIR"
echo "- 服务器地址: $HOST:$PORT"
if [ -n "$HF_ENDPOINT" ]; then
  echo "- Hugging Face 端点: $HF_ENDPOINT"
fi
if [ -n "$ALLOW_HF_DOWNLOAD" ]; then
  echo "- 允许联网补齐: $ALLOW_HF_DOWNLOAD"
fi
echo ""

# 检查本地模型文件是否存在
echo "检查本地模型文件..."
# 以 WHISPER_LOCAL_MODEL_DIR 为根检查 whisper 模型目录
MODEL_ROOT="${WHISPER_LOCAL_MODEL_DIR:-./models}"
MODEL_PATH="$MODEL_ROOT/models--Systran--faster-whisper-$WHISPER_MODEL_SIZE"
if [ -d "$MODEL_PATH" ]; then
    echo "✓ 找到本地 Whisper 模型: $MODEL_PATH"
    # 通过 find 检查 snapshots 目录及关键文件是否存在
SNAP_DIR=$(find "$MODEL_PATH/snapshots" -mindepth 1 -maxdepth 1 -type d 2>/dev/null | head -n 1)
if [ -n "$SNAP_DIR" ]; then
    # 尝试在 snapshots 下查找 model.bin（可能为文件或符号链接）
    FOUND_MODEL=$(find "$SNAP_DIR" -maxdepth 1 -type f -name "model.bin" 2>/dev/null | head -n 1)
    if [ -n "$FOUND_MODEL" ]; then
        echo "✓ 检测到快照目录与模型文件: $SNAP_DIR"
    else
        echo "! 检测到 HuggingFace 本地缓存结构，但未发现 materialized 的 model.bin"
        echo "! 继续启动，由应用在本地缓存中解析快照（local_files_only）"
        echo "— 快照目录: $SNAP_DIR"
    fi
else
    echo "! 未检测到 snapshots 子目录，但存在缓存根目录。尝试继续启动。"
    echo "— 当前目录结构提示："
    ls -la "$MODEL_PATH" 2>/dev/null || true
fi
else
    echo "✗ 未找到本地 Whisper 模型: $MODEL_PATH"
    echo "请确保模型文件已正确放置在 ./models 目录中（需包含 snapshots 子目录）。"
    exit 1
fi

# 检查TTS模型文件（基于 TTS_LOCAL_MODEL_DIR）
TTS_ROOT="${TTS_LOCAL_MODEL_DIR:-./models/tts}"
TTS_EN_PATH="$TTS_ROOT/tts_models--en--ljspeech--tacotron2-DDC_ph"
TTS_ZH_PATH="$TTS_ROOT/tts_models--zh-CN--baker--tacotron2-DDC-GST"
TTS_XTTS_PATH="$TTS_ROOT/tts_models--multilingual--multi-dataset--xtts_v2"

if [ -d "$TTS_EN_PATH" ] && [ -f "$TTS_EN_PATH/model_file.pth" ]; then
    echo "✓ 找到英文TTS模型"
else
    echo "! 未找到完整英文TTS模型，将尝试从网络下载"
fi

if [ -d "$TTS_ZH_PATH" ] && [ -f "$TTS_ZH_PATH/model_file.pth" ]; then
    echo "✓ 找到中文TTS模型"
else
    echo "! 未找到完整中文TTS模型，将尝试从网络下载"
fi

if [ -d "$TTS_XTTS_PATH" ] && [ -f "$TTS_XTTS_PATH/model.pth" ]; then
    echo "✓ 找到XTTS多语言模型"
else
    echo "! 未找到完整XTTS模型，将尝试从网络下载"
fi

echo ""
echo "启动服务..."
echo "========================================="

# 选择 Python 解释器：优先使用本地虚拟环境
PYTHON_BIN="${PYTHON_BIN:-python}"
if [ -x ".venv/bin/python" ]; then
  PYTHON_BIN=".venv/bin/python"
fi
echo "使用 Python: $(command -v "$PYTHON_BIN" || echo "$PYTHON_BIN")"

# 启动服务
"$PYTHON_BIN" -m uvicorn app_new:app --host $HOST --port $PORT --log-level info
