# 本地模型加载功能说明

## 概述

本项目已改造为支持优先从本地目录加载模型的功能。如果本地没有模型文件，会自动从网络下载并保存到本地目录供下次使用。

## 主要改进

### 1. Whisper 模型本地加载
- ✅ 优先从 `./models` 目录加载 Whisper 模型
- ✅ 支持 `base`、`small`、`tiny` 等模型大小
- ✅ 自动下载新模型并保存到本地
- ✅ 支持 CPU/GPU 设备选择

### 2. TTS 模型本地加载
- ✅ 自动扫描本地模型目录查找 TTS 模型文件
- ✅ 支持 `.pth`、`.bin` 等模型文件格式
- ✅ 支持 `config.json` 等配置文件
- ✅ 兼容中英文双模型配置

### 3. 配置管理
- ✅ 新增 `LOCAL_MODEL_DIR` 环境变量
- ✅ 向后兼容原有配置
- ✅ 支持自定义模型目录路径

## 使用方法

### 1. 环境配置

复制 `env.example` 为 `.env` 并修改配置：

```bash
cp env.example .env
```

编辑 `.env` 文件：

```bash
# 本地模型目录 - 优先从此目录加载模型，没有再联网下载
LOCAL_MODEL_DIR=./models

# Whisper 模型配置
WHISPER_MODEL_SIZE=base
WHISPER_DEVICE=cpu

# TTS 配置
TTS_EN_MODEL_NAME=tts_models/en/ljspeech/tacotron2-DDC_ph
TTS_ZH_MODEL_NAME=tts_models/zh-CN/baker/tacotron2-DDC-GST
```

### 2. 启动服务

```bash
# 使用默认配置
python app_new.py

# 或使用环境变量
LOCAL_MODEL_DIR=./my_models python app_new.py
```

### 3. 测试功能

运行测试脚本验证本地模型加载：

```bash
python test_local_models.py
```

## 模型存储结构

```
models/
├── models--Systran--faster-whisper-base/     # Whisper 模型
│   ├── blobs/
│   ├── refs/
│   └── snapshots/
│       └── xxx/
│           ├── config.json
│           ├── model.bin
│           ├── tokenizer.json
│           └── vocabulary.txt
├── models--Systran--faster-whisper-tiny/     # 其他 Whisper 模型
└── tts_models/                               # TTS 模型（如果手动下载）
    ├── en/
    └── zh-CN/
```

## 工作原理

### Whisper 模型加载流程

1. 检查本地 `./models` 目录
2. 尝试从本地目录加载指定大小的模型
3. 如果本地没有，使用 `download_root` 参数下载到本地
4. 下次启动时优先使用已下载的本地模型

### TTS 模型加载流程

1. 扫描本地模型目录查找模型文件（`.pth`、`.bin` 等）
2. 查找对应的配置文件（`config.json` 等）
3. 如果找到本地文件，使用 `model_path` 和 `config_path` 参数加载
4. 如果没有找到，使用模型名称从网络下载

## 优势

### 🚀 性能优势
- **首次加载**: 从网络下载模型到本地
- **后续加载**: 直接从本地加载，启动速度大幅提升
- **离线使用**: 下载完成后可以在无网络环境下使用

### 💾 存储优势
- **统一管理**: 所有模型文件集中存储
- **版本控制**: 本地模型文件可备份和版本管理
- **空间复用**: 多个项目可共享同一模型目录

### 🔧 运维优势
- **可预测性**: 避免网络下载的不确定性
- **可控性**: 可预先下载所需模型
- **可移植性**: 模型文件可轻松迁移到其他机器

## 故障排除

### Whisper 模型加载失败

```bash
# 检查模型目录权限
ls -la ./models/

# 重新下载模型
rm -rf ./models/models--Systran--faster-whisper-*
python app_new.py
```

### TTS 模型加载失败

```bash
# 检查本地模型文件
find ./models -name "*.pth" -o -name "*config*"

# 清理缓存重新下载
rm -rf ~/.cache/tts/
python app_new.py
```

### 磁盘空间不足

```bash
# 检查磁盘使用情况
df -h

# 清理不需要的模型
rm -rf ./models/models--Systran--faster-whisper-large*
```

## 最佳实践

### 1. 预下载模型

首次使用前预下载所需模型：

```bash
# 下载基础 Whisper 模型
python -c "from faster_whisper import WhisperModel; WhisperModel('base', download_root='./models')"

# 下载 TTS 模型
python -c "from TTS.api import TTS; TTS('tts_models/en/ljspeech/tacotron2-DDC_ph')"
```

### 2. 定期清理

定期清理不需要的模型文件：

```bash
# 查看模型大小
du -sh ./models/*

# 删除大模型节省空间
rm -rf ./models/models--Systran--faster-whisper-large*
```

### 3. 备份重要模型

```bash
# 备份模型目录
tar -czf models_backup.tar.gz ./models/

# 恢复模型
tar -xzf models_backup.tar.gz
```

## 兼容性

- ✅ 向后兼容原有配置
- ✅ 支持所有现有的环境变量
- ✅ 无需修改 API 接口
- ✅ 自动降级到网络下载模式

## 技术细节

### Whisper 模型参数

```python
WhisperModel(
    model_size=MODEL_SIZE,
    device=DEVICE,
    compute_type="int8",
    download_root=LOCAL_MODEL_DIR,  # 本地模型目录
    local_files_only=False          # 允许下载
)
```

### TTS 模型参数

```python
TTS(
    model_path=model_path,          # 本地模型文件路径
    config_path=config_path,        # 本地配置文件路径
    gpu=self.gpu
)
```

---

*如有问题，请查看日志文件 `whisper_service.log` 获取详细信息。*
