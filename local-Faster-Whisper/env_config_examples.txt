# =============================================================================
# 环境变量配置示例
# =============================================================================
# 复制相应的配置到你的 .env 文件中

# -----------------------------------------------------------------------------
# 配置方案1: 分离配置 - Whisper 和 TTS 使用不同的目录
# -----------------------------------------------------------------------------
WHISPER_LOCAL_MODEL_DIR=./models/whisper
TTS_LOCAL_MODEL_DIR=./models/tts

# 可选: 为 Whisper 指定不同的下载目录
WHISPER_DOWNLOAD_ROOT=./cache/whisper

# -----------------------------------------------------------------------------
# 配置方案2: 通用配置 - 所有模型使用同一目录（兼容旧配置）
# -----------------------------------------------------------------------------
# 注释掉上面的配置，只使用这一行
# LOCAL_MODEL_DIR=./models

# -----------------------------------------------------------------------------
# 配置方案3: 高级配置 - 完全自定义目录结构
# -----------------------------------------------------------------------------
# WHISPER_LOCAL_MODEL_DIR=./custom/whisper/models
# WHISPER_DOWNLOAD_ROOT=./custom/whisper/cache
# TTS_LOCAL_MODEL_DIR=./custom/tts/models
# TTS_MODELS_DIR=./custom/tts/cache

# -----------------------------------------------------------------------------
# 完整的 .env 文件示例
# -----------------------------------------------------------------------------
# 服务器配置
HOST=0.0.0.0
PORT=8000

# Whisper 模型配置
WHISPER_MODEL_SIZE=small
WHISPER_DEVICE=cpu

# TTS 配置 - 支持中英文双模型
TTS_EN_MODEL_NAME=tts_models/en/ljspeech/tacotron2-DDC_ph
TTS_EN_GPU=false
TTS_EN_LANGUAGE=en

TTS_ZH_MODEL_NAME=tts_models/zh-CN/baker/tacotron2-DDC-GST
TTS_ZH_GPU=false
TTS_ZH_LANGUAGE=zh-cn

# 文件上传限制 (字节)
MAX_FILE_SIZE=26214400

# 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# =============================================================================
# 模型存储目录结构示例
# =============================================================================
# 配置方案1后的目录结构:
# models/
# ├── whisper/           # Whisper 模型存储目录
# │   └── models--Systran--faster-whisper-small/
# │       └── snapshots/
# │           └── xxx/
# │               ├── model.bin
# │               ├── config.json
# │               └── ...
# └── tts/              # TTS 模型存储目录
#     └── tts_models/
#         └── en/
#             └── ljspeech/
#                 └── tacotron2-DDC_ph/
