user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
	worker_connections 768;
	# multi_accept on;
}

http {

	##
	# Basic Settings
	##

	sendfile on;
	tcp_nopush on;
	types_hash_max_size 2048;
	# server_tokens off;

	# server_names_hash_bucket_size 64;
	# server_name_in_redirect off;

	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	##
	# SSL Settings
	##

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3; # Dropping SSLv3, ref: POODLE
	ssl_prefer_server_ciphers on;

	##
	# Logging Settings
	##

	access_log /var/log/nginx/access.log;
	error_log /var/log/nginx/error.log;

	##
	# Gzip Settings
	##

	gzip on;

	# gzip_vary on;
	# gzip_proxied any;
	# gzip_comp_level 6;
	# gzip_buffers 16 8k;
	# gzip_http_version 1.1;
	# gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

	##
	# Virtual Host Configs
	##

    # Application server for frontend (SPA) and backend proxy
    server {
        # Make this the default server for port 80 (IPv4/IPv6)
        listen 443 ssl;
        server_name www.englishread.xyz;
        
	ssl_certificate /etc/letsencrypt/live/www.englishread.xyz/fullchain.pem;
	ssl_certificate_key /etc/letsencrypt/live/www.englishread.xyz/privkey.pem;
	ssl_protocols TLSv1.2 TLSv1.3;
	ssl_ciphers HIGH:!aNULL:!MD5;

	location /.well-known/acme-challenge/ {
	    root /var/www/myapp;   # 或者你的前端静态目录
	        allow all;
	}

        # Frontend: serve built SPA from /var/www/myapp
        root /var/www/myapp;
        index index.html;
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Backend API: proxy to Spring Boot on localhost:8081
        location /api/ {
            proxy_pass http://127.0.0.1:8081/api/;
            # SSE/WebSocket friendly settings
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Cache-Control "no-cache";
            proxy_buffering off;
            proxy_set_header X-Accel-Buffering no;
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
	 # Local Faster-Whisper service (HTTP + WebSocket) on localhost:8001
        # Proxied under /whisper/* so frontend can call same-origin
				           # HTTP endpoints (e.g., /whisper/transcribe, /whisper/tts/*)
					           location /whisper/ {
						               proxy_pass http://127.0.0.1:8001/;
							                   proxy_http_version 1.1;
									               proxy_set_header Host $host;
										                   proxy_set_header X-Real-IP $remote_addr;
												               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
													                   proxy_set_header X-Forwarded-Proto $scheme;
															               proxy_read_timeout 3600s;
																                   proxy_send_timeout 3600s;
																		           }

																			           # WebSocket endpoint for Faster-Whisper streaming: /whisper/ws/stream
																				           location /whisper/ws/ {
																					               proxy_pass http://127.0.0.1:8001/ws/;
																						                   proxy_http_version 1.1;
																								               proxy_set_header Upgrade $http_upgrade;
																									                   proxy_set_header Connection "upgrade";
																											               proxy_set_header Host $host;
																												                   proxy_read_timeout 3600s;
																														               proxy_send_timeout 3600s;
																															               }
    }

    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}


#mail {
#	# See sample authentication script at:
#	# http://wiki.nginx.org/ImapAuthenticateWithApachePhpScript
#
#	# auth_http localhost/auth.php;
#	# pop3_capabilities "TOP" "USER";
#	# imap_capabilities "IMAP4rev1" "UIDPLUS";
#
#	server {
#		listen     localhost:110;
#		protocol   pop3;
#		proxy      on;
#	}
#
#	server {
#		listen     localhost:143;
#		protocol   imap;
#		proxy      on;
#	}
#}
