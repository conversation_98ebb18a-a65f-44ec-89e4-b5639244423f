import os
import logging
import io
import tempfile
from typing import Optional, Dict, Any, List
from TTS.api import TTS
import torch
import numpy as np
from scipy.io.wavfile import write as write_wav

logger = logging.getLogger(__name__)

# 全局兼容：为 Coqui TTS 类补齐缺省属性，防止库内部访问缺失字段
try:
    if not hasattr(TTS, 'is_multi_lingual'):
        setattr(TTS, 'is_multi_lingual', False)
        logger.info("Compat: 为 TTS 类补齐缺省属性 is_multi_lingual=False")
except Exception:
    # 某些环境可能禁止为类动态赋值，忽略即可
    pass
try:
    if not hasattr(TTS, 'is_multilingual'):
        setattr(TTS, 'is_multilingual', False)
        logger.info("Compat: 为 TTS 类补齐缺省属性 is_multilingual=False")
except Exception:
    pass

class TTSTextToSpeechService:
    """
    Coqui TTS 文字转语音服务
    支持多种语言和说话人
    """

    def __init__(
        self,
        model_name: str = "tts_models/multilingual/multi-dataset/xtts_v2",
        gpu: bool = False,
        speaker_wav: Optional[str] = None,
        language: str = "zh-cn",
        local_model_dir: Optional[str] = None,
        auto_init: bool = True,
        num_threads: Optional[int] = None
    ):
        """
        初始化TTS服务

        Args:
            model_name: TTS模型名称
            gpu: 是否使用GPU
            speaker_wav: 说话人音频文件路径（可选，用于克隆声音）
            language: 默认语言
            local_model_dir: 本地模型目录（可选）
        """
        self.model_name = model_name
        self.gpu = gpu
        self.speaker_wav = speaker_wav
        self.language = language
        self.local_model_dir = local_model_dir
        self.tts = None
        self.is_initialized = False

        # 线程/并发设置（避免云主机CPU打满导致“卡死”）
        try:
            threads_env = int(os.getenv('TTS_NUM_THREADS', '0'))
        except Exception:
            threads_env = 0
        if num_threads is None:
            num_threads = threads_env if threads_env > 0 else None
        try:
            if num_threads and num_threads > 0:
                import torch
                torch.set_num_threads(num_threads)
                torch.set_num_interop_threads(max(1, num_threads // 2))
                os.environ.setdefault('OMP_NUM_THREADS', str(num_threads))
                os.environ.setdefault('MKL_NUM_THREADS', str(num_threads))
                os.environ.setdefault('NUMEXPR_NUM_THREADS', str(num_threads))
                logger.info(f"TTS 线程限制: num_threads={num_threads}")
        except Exception as e:
            logger.warning(f"设置TTS线程数失败: {e}")

        # 模型配置
        self.device = "cuda" if gpu and torch.cuda.is_available() else "cpu"

        # 将缓存目录环境变量指向本地模型目录，避免落到用户主目录
        try:
            if self.local_model_dir:
                os.environ.setdefault('TTS_CACHE_DIR', self.local_model_dir)
                os.environ.setdefault('COQUI_TTS_CACHE_DIR', self.local_model_dir)
        except Exception:
            pass

        # 初始化模型（可选惰性加载）
        if auto_init:
            self._initialize_model()

    def _apply_compat_shims(self):
        """为不同 TTS 版本打上兼容补丁，避免属性缺失导致的崩溃。

        - 一些版本内部访问 `self.is_multi_lingual`（注意单词中间的下划线和拼写），
          若不存在会引发 AttributeError。这里在实例上补齐该属性。
        - 若仅存在 `is_multi_lingual` 也同步到 `is_multilingual`，反之亦然。
        """
        try:
            if not hasattr(self, 'tts') or self.tts is None:
                return

            tts_obj = self.tts

            # 推断多语言能力
            inferred_multi = False  # 默认为False，保守处理
            try:
                if hasattr(tts_obj, 'is_multi_lingual'):
                    inferred_multi = bool(getattr(tts_obj, 'is_multi_lingual'))
                elif hasattr(tts_obj, 'is_multilingual'):
                    inferred_multi = bool(getattr(tts_obj, 'is_multilingual'))
                elif hasattr(tts_obj, 'languages'):
                    langs = getattr(tts_obj, 'languages')
                    try:
                        inferred_multi = bool(langs and len(langs) > 0)
                    except Exception:
                        inferred_multi = bool(langs)
                else:
                    # 基于 model_name 做个保守猜测
                    name = (self.model_name or '').lower()
                    inferred_multi = any(k in name for k in (
                        'xtts', 'multilingual', 'multi-dataset', 'vits-multilingual'
                    ))
            except Exception:
                inferred_multi = False

            # 使用monkey patch的方式强制添加属性
            try:
                # 直接在对象的__dict__中添加属性，绕过property限制
                if not hasattr(tts_obj, 'is_multi_lingual'):
                    tts_obj.__dict__['is_multi_lingual'] = inferred_multi
                    logger.info(f"Compat: 通过__dict__补齐 is_multi_lingual={inferred_multi}")
            except Exception as e:
                logger.warning(f"Compat: 通过__dict__设置 is_multi_lingual 失败: {e}")
                # 尝试直接设置属性
                try:
                    object.__setattr__(tts_obj, 'is_multi_lingual', inferred_multi)
                    logger.info(f"Compat: 通过object.__setattr__补齐 is_multi_lingual={inferred_multi}")
                except Exception as e2:
                    logger.warning(f"Compat: 通过object.__setattr__设置 is_multi_lingual 失败: {e2}")

            # 同样处理 is_multilingual
            try:
                if not hasattr(tts_obj, 'is_multilingual'):
                    tts_obj.__dict__['is_multilingual'] = inferred_multi
                    logger.info(f"Compat: 通过__dict__补齐 is_multilingual={inferred_multi}")
            except Exception as e:
                logger.warning(f"Compat: 通过__dict__设置 is_multilingual 失败: {e}")
                try:
                    object.__setattr__(tts_obj, 'is_multilingual', inferred_multi)
                    logger.info(f"Compat: 通过object.__setattr__补齐 is_multilingual={inferred_multi}")
                except Exception as e2:
                    logger.warning(f"Compat: 通过object.__setattr__设置 is_multilingual 失败: {e2}")

        except Exception as e:
            logger.warning(f"Compat: 打补丁出现异常: {e}")

    def _ensure_multilingual_attrs(self):
        """确保TTS对象具有必要的多语言属性，在每次调用前执行"""
        if not hasattr(self, 'tts') or self.tts is None:
            logger.warning("_ensure_multilingual_attrs: TTS对象不存在")
            return

        try:
            logger.info("_ensure_multilingual_attrs: 开始检查和补齐属性")
            # 快速检查并补齐缺失的属性
            if not hasattr(self.tts, 'is_multi_lingual'):
                self.tts.__dict__['is_multi_lingual'] = False
                logger.info("_ensure_multilingual_attrs: 补齐 is_multi_lingual=False")
            else:
                logger.info(f"_ensure_multilingual_attrs: is_multi_lingual 已存在，值为 {getattr(self.tts, 'is_multi_lingual')}")

            if not hasattr(self.tts, 'is_multilingual'):
                self.tts.__dict__['is_multilingual'] = False
                logger.info("_ensure_multilingual_attrs: 补齐 is_multilingual=False")
            else:
                logger.info(f"_ensure_multilingual_attrs: is_multilingual 已存在，值为 {getattr(self.tts, 'is_multilingual')}")
        except Exception as e:
            logger.error(f"确保多语言属性时出错: {e}")

    def ensure_initialized(self):
        if not self.is_initialized:
            self._initialize_model()

    def _find_local_model_files(self) -> Optional[Dict[str, str]]:
        """
        查找本地TTS模型文件

        Returns:
            包含模型路径信息的字典，如果未找到则返回None
        """
        if not self.local_model_dir:
            return None

        try:
            # 排除的目录和文件模式（避免误识别其他模型文件）
            exclude_patterns = [
                "faster-whisper",  # 排除Whisper模型
                "whisper",         # 排除Whisper相关文件
                ".git",           # 排除Git目录
                "__pycache__",    # 排除Python缓存
                "node_modules",   # 排除Node.js依赖
            ]

            # 从模型名称中提取关键词，用于匹配对应的本地文件
            model_keywords = []
            if self.model_name:
                # 例如: "tts_models/en/ljspeech/tacotron2-DDC_ph"
                # 转换为: "tts_models--en--ljspeech--tacotron2-DDC_ph"
                normalized_model_name = self.model_name.replace("/", "--").replace("_", "-").replace(".", "-")
                model_keywords = normalized_model_name.split("-")

                # 保留有意义的关键词（长度>2，去除通用词）
                exclude_words = {"tts", "models", "dataset", "multi", "v2", "v1", "v3"}
                model_keywords = [kw for kw in model_keywords if len(kw) > 2 and kw.lower() not in exclude_words]

            logger.info(f"TTS模型名称: {self.model_name}")
            logger.info(f"标准化名称: {normalized_model_name}")
            logger.info(f"匹配关键词: {model_keywords}")

            # 递归搜索模型文件
            for root, dirs, files in os.walk(self.local_model_dir):
                # 检查是否在排除目录中
                should_exclude = False
                for exclude_pattern in exclude_patterns:
                    if exclude_pattern in root:
                        should_exclude = True
                        break

                if should_exclude:
                    continue

                # 首先检查目录名是否匹配模型
                dir_name = os.path.basename(root)
                is_matching_dir = False

                if model_keywords:
                    # 检查目录名是否包含所有重要的模型关键词
                    matching_keywords = []
                    for kw in model_keywords:
                        if kw.lower() in dir_name.lower():
                            matching_keywords.append(kw)

                    # 对于英文模型，需要匹配至少2个关键词（如"ljspeech", "tacotron2"）
                    # 对于中文模型，需要匹配至少2个关键词（如"baker", "tacotron2"）
                    min_matches = 2
                    if len(matching_keywords) >= min_matches:
                        is_matching_dir = True
                        logger.info(f"找到匹配的模型目录: {dir_name}")
                        logger.info(f"匹配关键词: {matching_keywords} (需要至少{min_matches}个)")
                else:
                    # 如果没有关键词，检查是否是TTS模型目录
                    is_matching_dir = dir_name.startswith("tts_models")

                if not is_matching_dir:
                    continue

                # 在匹配的目录中查找模型文件
                model_path = None
                config_path = None

                for file in files:
                    file_path = os.path.join(root, file)

                    # 查找模型文件（主要模型文件）
                    if file.endswith('.pth') and not file.startswith('speakers_') and model_path is None:
                        model_path = file_path
                        logger.info(f"找到TTS模型文件: {file_path}")

                    # 查找配置文件
                    if file.endswith('.json') and 'config' in file.lower() and config_path is None:
                        config_path = file_path
                        logger.info(f"找到TTS配置文件: {file_path}")

                # 如果找到了模型文件，返回结果
                if model_path:
                    result = {"model_path": model_path}
                    if config_path:
                        result["config_path"] = config_path
                    logger.info(f"TTS本地模型文件查找完成: 模型={model_path}, 配置={config_path}")
                    return result

            logger.info(f"在目录 {self.local_model_dir} 中未找到匹配的本地TTS模型文件")
            return None

        except Exception as e:
            logger.warning(f"查找本地TTS模型文件时出错: {e}")
            return None

    def _initialize_model(self):
        """初始化TTS模型"""
        try:
            logger.info(f"正在初始化TTS模型: {self.model_name}, 设备: {self.device}")

            # 先尝试查找本地模型文件
            local_model_files = self._find_local_model_files()

            # 处理PyTorch 2.6+的权重加载兼容性问题
            # 方法1: 尝试添加安全全局变量
            try:
                import torch.serialization
                if hasattr(torch.serialization, 'add_safe_globals'):
                    try:
                        # 尝试添加TTS相关的安全全局变量
                        from TTS.tts.configs.xtts_config import XttsConfig
                        torch.serialization.add_safe_globals([XttsConfig])
                        logger.info("已添加TTS安全全局变量支持")
                    except ImportError:
                        logger.warning("无法导入TTS配置类，尝试其他方法")
                    except Exception as e:
                        logger.warning(f"添加安全全局变量失败: {e}")
            except ImportError:
                logger.warning("torch.serialization不可用，跳过安全全局变量设置")

            # 方法2: 使用环境变量禁用严格检查
            original_weights_only = os.environ.get('TORCH_FORCE_WEIGHTS_ONLY_LOAD', '')
            os.environ['TORCH_FORCE_WEIGHTS_ONLY_LOAD'] = '0'

            try:
                # 优先使用本地模型文件
                if local_model_files:
                    model_path = local_model_files["model_path"]
                    config_path = local_model_files.get("config_path")

                    logger.info(f"使用本地TTS模型文件: {model_path}")
                    if config_path:
                        logger.info(f"使用本地TTS配置文件: {config_path}")

                    try:
                        # 修正可能在配置中遗留的绝对路径（例如 scale_stats.npy 指向本地电脑用户目录）
                        patched_config_path = None
                        try:
                            import json
                            with open(config_path, 'r', encoding='utf-8') as cf:
                                cfg = json.load(cf)
                            # 可能字段位置：cfg['audio']['stats_path'] 或 cfg['audio']['stats_path']['path']
                            stats_path = None
                            if isinstance(cfg, dict) and 'audio' in cfg and isinstance(cfg['audio'], dict):
                                ap = cfg['audio']
                                # 常见键名
                                for key in ('stats_path', 'stats_file', 'scale_stats_path'):
                                    if key in ap:
                                        stats_path = ap[key]
                                        break
                            # 如果是绝对路径且不存在，则在本地模型目录中寻找替代
                            if stats_path and isinstance(stats_path, str) and os.path.isabs(stats_path) and not os.path.exists(stats_path):
                                model_dir = os.path.dirname(model_path)
                                # 常见文件名
                                candidates = [
                                    'scale_stats.npy',
                                    'stats.npy'
                                ]
                                found = None
                                for name in candidates:
                                    p = os.path.join(model_dir, name)
                                    if os.path.exists(p):
                                        found = p
                                        break
                                if not found:
                                    # 在模型根内全局搜索一次
                                    for root, _dirs, files in os.walk(model_dir):
                                        if 'scale_stats.npy' in files:
                                            found = os.path.join(root, 'scale_stats.npy')
                                            break
                                if found:
                                    # 覆盖配置中的路径
                                    ap[key] = found  # 使用上面循环命中的 key
                                    # 将修正后的配置写入临时文件
                                    import tempfile
                                    fd, patched = tempfile.mkstemp(prefix='tts_cfg_', suffix='.json')
                                    os.close(fd)
                                    with open(patched, 'w', encoding='utf-8') as pf:
                                        json.dump(cfg, pf, ensure_ascii=False, indent=2)
                                    patched_config_path = patched
                                    logger.warning(f"TTS配置中 {key} 指向无效绝对路径，已修正为: {found}")
                        except Exception as patch_e:
                            logger.warning(f"TTS配置自动修正失败，继续使用原配置: {patch_e}")

                        # 使用本地模型文件创建TTS实例（优先使用修正后的配置）
                        effective_cfg = patched_config_path or config_path
                        self.tts = TTS(
                            model_path=model_path,
                            config_path=effective_cfg,
                            gpu=self.gpu
                        ).to(self.device)
                        # 兼容补丁：补齐多语言标志
                        self._apply_compat_shims()
                        logger.info("TTS模型从本地文件加载成功！")
                    except Exception as local_e:
                        logger.warning(f"本地TTS模型文件加载失败: {local_e}，尝试网络下载")
                        # 如果本地文件加载失败，降级到网络下载
                        self.tts = TTS(model_name=self.model_name, gpu=self.gpu).to(self.device)
                        self._apply_compat_shims()
                        logger.info("TTS模型从网络下载成功！")
                else:
                    # 使用模型名称从网络下载
                    logger.info(f"未找到本地TTS模型，使用网络下载: {self.model_name}")
                    self.tts = TTS(model_name=self.model_name, gpu=self.gpu).to(self.device)
                    self._apply_compat_shims()
                    logger.info("TTS模型从网络下载成功！")
            finally:
                # 恢复原始环境变量
                if original_weights_only:
                    os.environ['TORCH_FORCE_WEIGHTS_ONLY_LOAD'] = original_weights_only
                else:
                    os.environ.pop('TORCH_FORCE_WEIGHTS_ONLY_LOAD', None)

            self.is_initialized = True
            logger.info("TTS模型初始化成功")

        except Exception as e:
            logger.error(f"TTS模型初始化失败: {e}")
            # 如果是权重加载问题，尝试更直接的方法
            if "weights_only" in str(e) or "WeightsUnpickler" in str(e):
                logger.info("检测到权重加载问题，尝试直接禁用权重检查...")
                try:
                    # 直接使用monkey patch禁用权重检查
                    import torch.serialization
                    original_load = torch.load

                    def patched_load(*args, **kwargs):
                        kwargs['weights_only'] = False
                        return original_load(*args, **kwargs)

                    torch.load = patched_load
                    torch.serialization.load = patched_load

                    self.tts = TTS(model_name=self.model_name).to(self.device)
                    self._apply_compat_shims()

                    # 恢复原始函数
                    torch.load = original_load
                    torch.serialization.load = original_load

                    self.is_initialized = True
                    logger.info("TTS模型初始化成功（禁用权重检查）")
                    return

                except Exception as e2:
                    logger.error(f"禁用权重检查也失败: {e2}")

            raise RuntimeError(f"TTS模型初始化失败: {e}")

    def _force_add_multilingual_flags(self):
        """在运行时为 TTS 实例/类补齐 is_multi_lingual 与 is_multilingual 标志。"""
        updated = False
        try:
            if hasattr(self, 'tts') and self.tts is not None:
                # 推断多语言能力
                inferred_multi = False
                try:
                    # 基于模型名称推断
                    name = (self.model_name or '').lower()
                    inferred_multi = any(k in name for k in (
                        'xtts', 'multilingual', 'multi-dataset', 'vits-multilingual'
                    ))
                except Exception:
                    inferred_multi = False

                # 尝试实例级
                try:
                    if not hasattr(self.tts, 'is_multi_lingual'):
                        setattr(self.tts, 'is_multi_lingual', inferred_multi)
                        updated = True
                        logger.debug(f"设置实例 is_multi_lingual = {inferred_multi}")
                except Exception as e:
                    logger.debug(f"设置实例 is_multi_lingual 失败: {e}")

                try:
                    if not hasattr(self.tts, 'is_multilingual'):
                        setattr(self.tts, 'is_multilingual', inferred_multi)
                        updated = True
                        logger.debug(f"设置实例 is_multilingual = {inferred_multi}")
                except Exception as e:
                    logger.debug(f"设置实例 is_multilingual 失败: {e}")

                # 类级兜底
                try:
                    cls = self.tts.__class__
                    if not hasattr(cls, 'is_multi_lingual'):
                        setattr(cls, 'is_multi_lingual', inferred_multi)
                        updated = True
                        logger.debug(f"设置类 is_multi_lingual = {inferred_multi}")
                    if not hasattr(cls, 'is_multilingual'):
                        setattr(cls, 'is_multilingual', inferred_multi)
                        updated = True
                        logger.debug(f"设置类 is_multilingual = {inferred_multi}")
                except Exception as e:
                    logger.debug(f"设置类级多语言标志失败: {e}")
        except Exception as e:
            logger.warning(f"补齐多语言标志时出现异常: {e}")

        if updated:
            logger.info("Compat: 运行时补齐多语言标志 is_multi_lingual/is_multilingual")
        return updated

    def get_available_models(self) -> List[str]:
        """获取可用的TTS模型列表"""
        try:
            return TTS.list_models()
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return []

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if not self.is_initialized:
            return {"error": "模型未初始化"}

        try:
            info = {
                "model_name": self.model_name,
                "device": self.device,
                "language": self.language,
                "gpu_available": torch.cuda.is_available(),
                "gpu_used": self.gpu and torch.cuda.is_available(),
                "speaker_wav": self.speaker_wav
            }
            return info
        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            return {"error": str(e)}

    def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        if not self.is_initialized:
            return []

        try:
            if hasattr(self.tts, 'languages'):
                try:
                    langs = getattr(self.tts, 'languages')
                    # 某些版本 TTS.languages 可能是属性访问，内部引用未定义字段，需保护
                    if langs is None:
                        return []
                    if isinstance(langs, (list, tuple, set)):
                        return list(langs)
                    # 其他可迭代类型
                    try:
                        return list(langs)
                    except Exception:
                        return []
                except Exception as e:
                    logger.warning(f"访问 TTS.languages 失败，将视为不支持多语言: {e}")
                    return []
            return []
        except Exception as e:
            logger.error(f"获取语言列表失败: {e}")
            return []

    def get_speakers(self) -> List[str]:
        """获取可用的说话人列表"""
        if not self.is_initialized:
            return []

        try:
            speakers = self.tts.speakers if hasattr(self.tts, 'speakers') else []
            # 确保返回的是列表
            if speakers is None:
                return []
            elif isinstance(speakers, list):
                return speakers
            else:
                # 如果是其他类型，尝试转换
                return list(speakers) if hasattr(speakers, '__iter__') else []
        except Exception as e:
            logger.error(f"获取说话人列表失败: {e}")
            return []

    def synthesize_to_file(
        self,
        text: str,
        output_path: str,
        speaker: Optional[str] = None,
        language: Optional[str] = None,
        speed: float = 1.0
    ) -> Dict[str, Any]:
        """
        将文字合成语音并保存到文件

        Args:
            text: 要合成的文字
            output_path: 输出文件路径
            speaker: 说话人（可选）
            language: 语言（可选，使用默认语言）
            speed: 语速（0.5-2.0之间）

        Returns:
            包含合成结果的字典
        """
        if not self.is_initialized:
            return {"success": False, "error": "TTS服务未初始化"}

        if not text or not text.strip():
            return {"success": False, "error": "输入文字不能为空"}

        try:
            # 限制语速范围
            speed = max(0.5, min(2.0, speed))

            # 使用指定的语言或默认语言
            target_language = language or self.language

            logger.info(f"开始合成语音: 文字长度={len(text)}, 语言={target_language}, 语速={speed}")

            # 确保TTS对象具有必要的属性
            self._ensure_multilingual_attrs()

            # 处理多说话人模型
            if speaker is None:
                # 尝试获取默认说话人
                speakers = self.get_speakers()
                if speakers:
                    speaker = speakers[0]  # 使用第一个说话人作为默认
                    logger.info(f"使用默认说话人: {speaker}")
                else:
                    # 如果没有说话人列表，尝试使用None（某些模型支持）
                    logger.info("未指定说话人，尝试使用模型默认设置")

            # 执行TTS合成
            # 检查模型是否支持多语言（仅依据显式标识，避免误判）
            is_multi_lingual = False
            try:
                # 先尝试补齐缺失的属性，避免后续访问时出错
                self._force_add_multilingual_flags()

                # 优先读取旧属性，其次新属性；不再通过 languages 推断
                if hasattr(self.tts, 'is_multi_lingual'):
                    is_multi_lingual = bool(getattr(self.tts, 'is_multi_lingual'))
                elif hasattr(self.tts, 'is_multilingual'):
                    is_multi_lingual = bool(getattr(self.tts, 'is_multilingual'))
            except Exception as e:
                logger.warning(f"检测多语言支持失败，按单语言处理: {e}")
                is_multi_lingual = False

            def _call_to_file(pass_lang: bool):
                # 在每次调用前确保属性存在
                self._ensure_multilingual_attrs()

                if speaker:
                    if pass_lang:
                        self.tts.tts_to_file(text=text, file_path=output_path, speaker=speaker, language=target_language, speed=speed)
                    else:
                        self.tts.tts_to_file(text=text, file_path=output_path, speaker=speaker, speed=speed)
                else:
                    if pass_lang:
                        self.tts.tts_to_file(text=text, file_path=output_path, language=target_language, speed=speed)
                    else:
                        self.tts.tts_to_file(text=text, file_path=output_path, speed=speed)

            try:
                _call_to_file(is_multi_lingual)
            except Exception as ae:
                logger.warning(f"TTS调用失败: {ae}")
                # 直接尝试不带language参数的调用
                try:
                    logger.info("尝试不带language参数的TTS调用")
                    _call_to_file(False)
                except Exception as fallback_e:
                    logger.error(f"不带language参数的调用也失败: {fallback_e}")
                    # 最后尝试最基础的调用
                    try:
                        logger.info("尝试最基础的TTS调用")
                        self.tts.tts_to_file(text=text, file_path=output_path)
                        logger.info("使用基础TTS调用成功")
                    except Exception as basic_e:
                        logger.error(f"基础TTS调用也失败: {basic_e}")
                        raise ae

            # 获取文件大小
            file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0

            logger.info(f"语音合成完成: 输出文件={output_path}, 大小={file_size} bytes")

            return {
                "success": True,
                "message": "语音合成成功",
                "data": {
                    "output_path": output_path,
                    "file_size": file_size,
                    "language": target_language,
                    "speed": speed,
                    "speaker": speaker
                }
            }

        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return {"success": False, "error": f"语音合成失败: {str(e)}"}

    def synthesize_to_bytes(
        self,
        text: str,
        speaker: Optional[str] = None,
        language: Optional[str] = None,
        speed: float = 1.0
    ) -> Dict[str, Any]:
        """
        将文字合成语音并返回音频数据

        Args:
            text: 要合成的文字
            speaker: 说话人（可选）
            language: 语言（可选，使用默认语言）
            speed: 语速（0.5-2.0之间）

        Returns:
            包含音频数据的字典
        """
        if not self.is_initialized:
            return {"success": False, "error": "TTS服务未初始化"}

        if not text or not text.strip():
            return {"success": False, "error": "输入文字不能为空"}

        try:
            # 限制语速范围
            speed = max(0.5, min(2.0, speed))

            # 使用指定的语言或默认语言
            target_language = language or self.language

            logger.info(f"开始合成语音到内存: 文字长度={len(text)}, 语言={target_language}, 语速={speed}")

            # 确保TTS对象具有必要的属性
            self._ensure_multilingual_attrs()

            # 处理多说话人模型
            if speaker is None:
                # 尝试获取默认说话人
                speakers = self.get_speakers()
                if speakers:
                    speaker = speakers[0]  # 使用第一个说话人作为默认
                    logger.info(f"使用默认说话人: {speaker}")
                else:
                    # 如果没有说话人列表，尝试使用None（某些模型支持）
                    logger.info("未指定说话人，尝试使用模型默认设置")

            # 创建临时文件进行合成
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # 执行TTS合成到临时文件
                # 检查模型是否支持多语言（仅依据显式标识，避免误判）
                is_multi_lingual = False
                try:
                    # 先尝试补齐缺失的属性，避免后续访问时出错
                    self._force_add_multilingual_flags()

                    if hasattr(self.tts, 'is_multi_lingual'):
                        is_multi_lingual = bool(getattr(self.tts, 'is_multi_lingual'))
                    elif hasattr(self.tts, 'is_multilingual'):
                        is_multi_lingual = bool(getattr(self.tts, 'is_multilingual'))
                except Exception as e:
                    logger.warning(f"检测多语言支持失败，按单语言处理: {e}")
                    is_multi_lingual = False

                def _call_to_temp(pass_lang: bool):
                    # 在每次调用前确保属性存在
                    self._ensure_multilingual_attrs()

                    if speaker:
                        if pass_lang:
                            self.tts.tts_to_file(text=text, file_path=temp_path, speaker=speaker, language=target_language, speed=speed)
                        else:
                            self.tts.tts_to_file(text=text, file_path=temp_path, speaker=speaker, speed=speed)
                    else:
                        if pass_lang:
                            self.tts.tts_to_file(text=text, file_path=temp_path, language=target_language, speed=speed)
                        else:
                            self.tts.tts_to_file(text=text, file_path=temp_path, speed=speed)

                try:
                    _call_to_temp(is_multi_lingual)
                except Exception as ae:
                    logger.warning(f"TTS调用失败: {ae}")
                    # 直接尝试不带language参数的调用
                    try:
                        logger.info("尝试不带language参数的TTS调用")
                        _call_to_temp(False)
                    except Exception as fallback_e:
                        logger.error(f"不带language参数的调用也失败: {fallback_e}")
                        # 最后尝试最基础的调用
                        try:
                            logger.info("尝试最基础的TTS调用")
                            self.tts.tts_to_file(text=text, file_path=temp_path)
                            logger.info("使用基础TTS调用成功")
                        except Exception as basic_e:
                            logger.error(f"基础TTS调用也失败: {basic_e}")
                            raise ae

                # 读取音频数据
                with open(temp_path, "rb") as f:
                    audio_bytes = f.read()

                file_size = len(audio_bytes)

                logger.info(f"语音合成完成: 数据大小={file_size} bytes")

                return {
                    "success": True,
                    "message": "语音合成成功",
                    "data": {
                        "audio_bytes": audio_bytes,
                        "file_size": file_size,
                        "language": target_language,
                        "speed": speed,
                        "speaker": speaker,
                        "content_type": "audio/wav"
                    }
                }

            finally:
                # 清理临时文件
                try:
                    os.remove(temp_path)
                except Exception:
                    pass

        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return {"success": False, "error": f"语音合成失败: {str(e)}"}

    def set_language(self, language: str):
        """设置默认语言"""
        self.language = language
        logger.info(f"TTS默认语言设置为: {language}")

    def set_speaker_wav(self, speaker_wav: str):
        """设置说话人音频文件"""
        if os.path.exists(speaker_wav):
            self.speaker_wav = speaker_wav
            logger.info(f"TTS说话人音频设置为: {speaker_wav}")
        else:
            logger.error(f"说话人音频文件不存在: {speaker_wav}")

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'tts') and self.tts:
            try:
                # 清理TTS模型占用的GPU内存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except Exception:
                pass
