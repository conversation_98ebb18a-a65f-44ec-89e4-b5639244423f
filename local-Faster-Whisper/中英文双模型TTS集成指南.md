# 中英文双模型TTS集成指南

## 🎯 概述

本文档介绍如何在 `app_new.py` 中集成中英文双模型TTS服务，提供分别的API接口用于英文和中文语音合成。

## 📋 功能特性

- ✅ **双模型支持**: 英文和中文分别使用专用模型
- ✅ **独立接口**: `/tts/en/*` 和 `/tts/zh/*` 两个接口系列
- ✅ **向后兼容**: 保持原有接口可用性
- ✅ **统一管理**: 共享的服务生命周期管理
- ✅ **配置灵活**: 环境变量独立配置

## 🏗️ 系统架构

```
app_new.py
├── 英文TTS服务 (tts_en)
│   ├── 模型: tts_models/en/ljspeech/tacotron2-DDC_ph
│   ├── 接口: /tts/en/*
│   └── 默认语言: en
│
├── 中文TTS服务 (tts_zh)
│   ├── 模型: tts_models/zh-CN/baker/tacotron2-DDC-GST
│   ├── 接口: /tts/zh/*
│   └── 默认语言: zh-cn
│
└── 兼容性层 (tts)
    └── 指向英文TTS服务
```

## ⚙️ 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 英文TTS配置
TTS_EN_MODEL_NAME=tts_models/en/ljspeech/tacotron2-DDC_ph
TTS_EN_GPU=false
TTS_EN_LANGUAGE=en

# 中文TTS配置
TTS_ZH_MODEL_NAME=tts_models/zh-CN/baker/tacotron2-DDC-GST
TTS_ZH_GPU=false
TTS_ZH_LANGUAGE=zh-cn
```

### 推荐模型配置

| 语言 | 推荐模型 | 特点 |
|-----|---------|------|
| 英文 | `tts_models/en/ljspeech/tacotron2-DDC_ph` | 高质量英文TTS |
| 中文 | `tts_models/zh-CN/baker/tacotron2-DDC-GST` | 中文专用模型，音质优秀 |

## 🌐 API接口

### 英文TTS接口

#### 1. 语音合成（返回音频数据）
```http
POST /tts/en/synthesize
Content-Type: application/json

{
  "text": "Hello, this is English text!",
  "language": "en",
  "speed": 1.0
}
```

#### 2. 语音合成并保存文件
```http
POST /tts/en/synthesize/file
Content-Type: application/json

{
  "text": "Hello, this is English text!",
  "output_filename": "english_speech.wav",
  "language": "en",
  "speed": 1.0
}
```

### 中文TTS接口

#### 1. 语音合成（返回音频数据）
```http
POST /tts/zh/synthesize
Content-Type: application/json

{
  "text": "你好，这是中文文本！",
  "language": "zh-cn",
  "speed": 1.0
}
```

#### 2. 语音合成并保存文件
```http
POST /tts/zh/synthesize/file
Content-Type: application/json

{
  "text": "你好，这是中文文本！",
  "output_filename": "chinese_speech.wav",
  "language": "zh-cn",
  "speed": 1.0
}
```

### 服务状态查询

#### 获取TTS服务状态
```http
GET /tts/status
```

响应示例：
```json
{
  "success": true,
  "data": {
    "english_tts": {
      "status": "ready",
      "model": "tts_models/en/ljspeech/tacotron2-DDC_ph",
      "initialized": true
    },
    "chinese_tts": {
      "status": "ready",
      "model": "tts_models/zh-CN/baker/tacotron2-DDC-GST",
      "initialized": true
    }
  }
}
```

#### 健康检查
```http
GET /health
```

## 🚀 快速开始

### 1. 配置环境变量

```bash
# 复制配置模板
cp env.example .env

# 编辑配置（可选，使用默认配置即可）
nano .env
```

### 2. 启动服务

```bash
# 使用专用启动脚本
./start_bilingual_tts.sh

# 或手动启动
uvicorn app_new:app --host 0.0.0.0 --port 8001 --log-level info
```

### 3. 测试接口

```bash
# 测试英文TTS
curl -X POST "http://localhost:8001/tts/en/synthesize" \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello World!", "language": "en"}' \
  --output english_test.wav

# 测试中文TTS
curl -X POST "http://localhost:8001/tts/zh/synthesize" \
  -H "Content-Type: application/json" \
  -d '{"text": "你好世界！", "language": "zh-cn"}' \
  --output chinese_test.wav

# 查看服务状态
curl http://localhost:8001/tts/status
```

## 🧪 测试脚本

使用提供的测试脚本验证功能：

```bash
# 运行综合测试
python test_bilingual_tts.py

# 查看生成的音频文件
ls -la tts_outputs/
```

## 📊 性能对比

| 模型 | 语言 | 首次加载时间 | 单次合成时间 | 音质评分 |
|-----|------|------------|-----------|---------|
| Tacotron2-DDC_ph | 英文 | ~10秒 | 1.0-2.5秒 | ⭐⭐⭐⭐⭐ |
| Tacotron2-DDC-GST | 中文 | ~15秒 | 1.2-2.5秒 | ⭐⭐⭐⭐⭐ |

## 🔧 高级配置

### 自定义模型

```bash
# 使用其他英文模型
TTS_EN_MODEL_NAME=tts_models/en/ek1/tacotron2

# 使用其他中文模型
TTS_ZH_MODEL_NAME=tts_models/zh-CN/css10/vits
```

### GPU加速

```bash
# 启用GPU加速
TTS_EN_GPU=true
TTS_ZH_GPU=true
```

### 语速调节

```bash
# 语速范围：0.5-2.0
"speed": 0.8  # 慢速
"speed": 1.0  # 正常
"speed": 1.5  # 快速
```

## 🔍 故障排除

### 常见问题

#### 1. 中文TTS服务初始化失败
```
错误：TTS模型初始化失败
解决：检查模型名称是否正确，确保网络连接正常
```

#### 2. 音频文件为空
```
错误：生成的音频文件大小为0
解决：检查模型是否正确加载，查看日志中的错误信息
```

#### 3. 接口返回503错误
```
错误：HTTP 503 Service Unavailable
解决：等待模型初始化完成，或检查模型配置
```

### 日志查看

```bash
# 查看实时日志
tail -f whisper_service.log

# 查看完整日志
cat whisper_service.log
```

## 📝 开发说明

### 代码结构

```
app_new.py
├── 配置部分
│   ├── 英文TTS配置 (TTS_EN_*)
│   └── 中文TTS配置 (TTS_ZH_*)
├── 服务初始化 (lifespan)
│   ├── 初始化英文TTS服务
│   └── 初始化中文TTS服务
├── API接口
│   ├── 英文TTS接口 (/tts/en/*)
│   ├── 中文TTS接口 (/tts/zh/*)
│   └── 状态查询接口 (/tts/status)
└── 兼容性层
    └── 原有接口保持可用
```

### 扩展开发

如需添加更多语言，可以按照以下步骤：

1. 添加新的环境变量配置
2. 在lifespan中初始化新的TTS服务
3. 创建对应的API接口
4. 更新状态查询接口

## 🎯 最佳实践

1. **模型选择**: 为每种语言选择专用模型以获得最佳效果
2. **资源管理**: 合理配置GPU/CPU使用，避免资源浪费
3. **错误处理**: 妥善处理模型加载失败的情况
4. **日志记录**: 启用详细日志以便问题排查
5. **性能监控**: 定期检查服务状态和性能指标

## 📚 相关文档

- [Faster-Whisper集成指南](./app_new集成TTS指南.md)
- [环境配置说明](./env.example)
- [API文档](http://localhost:8001/docs) (服务运行时)

---

**🎉 恭喜！中英文双模型TTS集成完成！**

现在您可以：
- 使用 `/tts/en/*` 接口进行英文语音合成
- 使用 `/tts/zh/*` 接口进行中文语音合成
- 通过 `/tts/status` 查看服务状态
- 享受高品质的中英文语音服务
