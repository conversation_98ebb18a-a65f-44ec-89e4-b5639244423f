#!/bin/bash

# app_new.py TTS服务启动脚本
# 启动集成TTS功能的Faster-Whisper服务

echo "🚀 启动 app_new.py (集成TTS功能)..."
echo "========================================"

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ Python 未安装或不在PATH中"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
python -c "import fastapi, uvicorn, faster_whisper, TTS" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少必要的依赖，请运行: pip install -r requirements.txt"
    exit 1
fi

echo "✅ 依赖检查通过"

# 创建必要的目录
echo "📁 创建输出目录..."
mkdir -p uploads
mkdir -p tts_outputs
mkdir -p debug_uploads

# 设置默认环境变量（如果没有设置的话）
export WHISPER_MODEL_SIZE=${WHISPER_MODEL_SIZE:-"small"}
export WHISPER_DEVICE=${WHISPER_DEVICE:-"cpu"}
export TTS_MODEL_NAME=${TTS_MODEL_NAME:-"tts_models/multilingual/multi-dataset/xtts_v2"}
export TTS_GPU=${TTS_GPU:-"false"}
export TTS_LANGUAGE=${TTS_LANGUAGE:-"zh-cn"}

echo "🔧 配置信息:"
echo "   Whisper 模型: $WHISPER_MODEL_SIZE"
echo "   Whisper 设备: $WHISPER_DEVICE"
echo "   TTS 模型: $TTS_MODEL_NAME"
echo "   TTS GPU: $TTS_GPU"
echo "   TTS 语言: $TTS_LANGUAGE"

# 启动服务
echo ""
echo "🌟 启动服务..."
echo "   服务地址: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"
echo "   TTS测试: python test_tts_app_new.py"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

python app_new.py
