# Faster-Whisper 本地语音识别与 TTS 服务

基于 [Faster-Whisper](https://github.com/guillaumekln/faster-whisper) 和 [Coqui TTS](https://github.com/coqui-ai/TTS) 的高性能本地语音识别转文字和文字转语音服务，提供简单易用的 REST API 接口。

## ✨ 特性

### 语音识别 (ASR)
- 🚀 **高性能**: 基于 Faster-Whisper，比原版 Whisper 快 4 倍，内存使用更少
- 🌍 **多语言支持**: 支持中文、英文、日文等 90+ 种语言
- 🔧 **易于部署**: 简单的安装和配置过程
- 📝 **完整 API**: 提供 RESTful API 和交互式文档
- ⚙️ **灵活配置**: 支持多种模型大小和设备类型
- 📊 **详细输出**: 提供词级别时间戳和置信度

### 文字转语音 (TTS)
- 🎵 **高质量合成**: 基于 Coqui TTS，支持多种高级模型
- 🗣️ **多说话人**: 支持多种预定义说话人
- 🌐 **多语言输出**: 支持多种语言的语音合成
- 🎛️ **参数调节**: 可调节语速、说话人等参数
- 💾 **灵活输出**: 支持内存输出和文件保存
- 🎭 **声音克隆**: 可选的支持声音克隆功能

## 🛠️ 系统要求

### 基础要求
- Python 3.8+
- 8GB+ RAM (推荐 16GB+，ASR + TTS)
- 可选: NVIDIA GPU (支持 CUDA，提升性能)

### ASR (语音识别) 要求
- 模型大小: tiny/base (4GB RAM), small (8GB RAM), medium+ (16GB+ RAM)

### TTS (文字转语音) 要求
- XTTS v2 模型: ~8GB RAM
- GPU 推荐用于实时合成性能

## 📦 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd local-Faster-Whisper
```

### 2. 创建虚拟环境

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\\Scripts\\activate   # Windows
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 配置服务

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 5. 启动服务

```bash
# 方式 1: 使用 Python 脚本
python start.py

# 方式 2: 使用 Shell 脚本 (Linux/Mac)
chmod +x run.sh
./run.sh

# 方式 3: 直接启动
python -m uvicorn app:app --host 0.0.0.0 --port 8000
```

## 📋 配置说明

### 环境变量配置 (.env)

```bash
# 服务器配置
HOST=0.0.0.0              # 服务器地址
PORT=8000                 # 端口号

# 模型配置
WHISPER_MODEL_SIZE=base   # 模型大小: tiny, base, small, medium, large-v3
WHISPER_DEVICE=auto       # 设备: auto, cpu, cuda
WHISPER_COMPUTE_TYPE=default  # 计算类型: default, int8, float16

# 其他配置
LOG_LEVEL=INFO           # 日志级别
MAX_FILE_SIZE=26214400   # 最大文件大小 (25MB)

# TTS 配置
TTS_MODEL_NAME=tts_models/multilingual/multi-dataset/xtts_v2  # TTS模型名称
TTS_GPU=false            # 是否使用GPU进行TTS推理
TTS_LANGUAGE=zh-cn       # 默认TTS语言
# TTS_SPEAKER_WAV=./speaker.wav  # 可选：说话人音频文件路径（用于声音克隆）
TTS_OUTPUT_DIR=./tts_outputs    # TTS输出目录
```

### 模型大小说明

| 模型 | 参数量 | 内存使用 | 速度 | 准确性 |
|-----|-------|---------|------|-------|
| tiny | 39M | ~1GB | 最快 | 一般 |
| base | 74M | ~1GB | 快 | 良好 |
| small | 244M | ~2GB | 中等 | 很好 |
| medium | 769M | ~5GB | 慢 | 优秀 |
| large-v3 | 1550M | ~10GB | 最慢 | 最佳 |

## 🌐 API 使用

服务启动后，可以通过以下方式使用：

### API 文档

- 交互式文档: http://localhost:8000/docs
- OpenAPI JSON: http://localhost:8000/openapi.json

### 主要接口

#### 1. 健康检查

```bash
curl http://localhost:8000/health
```

#### 2. 获取模型信息

```bash
curl http://localhost:8000/info
```

#### 3. 语音转录

```bash
curl -X POST http://localhost:8000/transcribe \\
  -F "file=@your_audio.wav" \\
  -F "language=zh" \\
  -F "task=transcribe"
```

#### 4. TTS 文字转语音

##### 基本合成（返回音频数据）
```bash
curl -X POST http://localhost:8000/tts/synthesize \
  -H "Content-Type: application/json" \
  -d '{
    "text": "你好，欢迎使用TTS服务",
    "language": "zh-cn",
    "speed": 1.0
  }' \
  --output speech.wav
```

##### 保存到文件
```bash
curl -X POST http://localhost:8000/tts/synthesize/file \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是一个测试句子",
    "output_filename": "test.wav",
    "language": "zh-cn",
    "speed": 0.9
  }'
```

##### 获取TTS信息
```bash
# 获取模型信息
curl http://localhost:8000/tts/info

# 获取支持的语言
curl http://localhost:8000/tts/languages

# 获取可用说话人
curl http://localhost:8000/tts/speakers
```

#### 5. 使用 Python 调用

##### ASR 语音识别
```python
import requests

# 上传音频文件进行转录
url = "http://localhost:8000/transcribe"
files = {"file": open("audio.wav", "rb")}
data = {
    "language": "zh",  # 可选：语言代码
    "task": "transcribe"  # transcribe 或 translate
}

response = requests.post(url, files=files, data=data)
result = response.json()

if result["success"]:
    print("转录文本:", result["data"]["text"])
    print("检测语言:", result["data"]["language"])

    # 获取详细片段信息
    for segment in result["data"]["segments"]:
        print(f"[{segment['start']:.2f}s - {segment['end']:.2f}s]: {segment['text']}")
```

##### TTS 文字转语音
```python
import requests

# 文字转语音合成
tts_url = "http://localhost:8000/tts/synthesize"
tts_data = {
    "text": "你好，这是一个TTS合成的示例",
    "language": "zh-cn",
    "speed": 1.0
}

response = requests.post(tts_url, json=tts_data)

if response.status_code == 200:
    # 保存音频文件
    with open("output.wav", "wb") as f:
        f.write(response.content)
    print("✅ 语音合成成功，已保存为 output.wav")
else:
    print(f"❌ 合成失败: {response.status_code}")

# 获取TTS服务信息
info_response = requests.get("http://localhost:8000/tts/info")
if info_response.status_code == 200:
    info = info_response.json()
    print("TTS模型信息:", info["data"])
```

### 响应格式

```json
{
  "success": true,
  "message": "转录成功",
  "data": {
    "language": "zh",
    "language_probability": 0.99,
    "duration": 10.5,
    "text": "完整的转录文本",
    "segments": [
      {
        "start": 0.0,
        "end": 3.5,
        "text": "这是第一段文本",
        "words": [
          {
            "start": 0.0,
            "end": 0.5,
            "word": "这是",
            "probability": 0.98
          }
        ]
      }
    ]
  }
}
```

## 🎯 高级用法

### 自定义启动参数

```bash
python start.py --help

# 自定义配置启动
python start.py \\
  --host 0.0.0.0 \\
  --port 8080 \\
  --model small \\
  --device cuda \\
  --log-level DEBUG
```

### 批量处理脚本

```python
import os
import requests
from pathlib import Path

def transcribe_directory(audio_dir, output_dir, server_url="http://localhost:8000"):
    """批量转录目录中的音频文件"""
    audio_dir = Path(audio_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    for audio_file in audio_dir.glob("*.wav"):
        print(f"正在处理: {audio_file.name}")
        
        with open(audio_file, "rb") as f:
            files = {"file": f}
            response = requests.post(f"{server_url}/transcribe", files=files)
            
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                # 保存转录结果
                output_file = output_dir / f"{audio_file.stem}.txt"
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(result["data"]["text"])
                print(f"✅ 转录完成: {output_file}")
            else:
                print(f"❌ 转录失败: {result['message']}")
        else:
            print(f"❌ 请求失败: {response.status_code}")

# 使用示例
# transcribe_directory("./audio_files", "./transcripts")
```

## 🔧 故障排除

### 常见问题

1. **模型下载慢**
   - 使用较小的模型 (tiny, base)
   - 检查网络连接
   - 考虑使用代理

2. **内存不足**
   - 使用较小的模型
   - 设置 `compute_type=int8`
   - 减少 `beam_size` 参数

3. **CUDA 错误**
   - 检查 CUDA 和 PyTorch 版本兼容性
   - 设置 `device=cpu` 使用 CPU

4. **转录质量差**
   - 使用更大的模型
   - 确保音频质量良好
   - 指定正确的语言代码

#### TTS 常见问题

1. **TTS 合成速度慢**
   - 使用 GPU 加速: 设置 `TTS_GPU=true`
   - 降低语速参数 (0.5-1.0)
   - 使用较短的文本

2. **TTS 内存不足**
   - 使用 CPU 模式: 设置 `TTS_GPU=false`
   - 重启服务释放内存
   - 减少并发请求

3. **TTS 合成质量差**
   - 尝试不同的说话人
   - 调整语速参数
   - 使用更高质量的模型

4. **TTS 模型下载失败**
   - 检查网络连接
   - 使用代理（如果需要）
   - 手动下载模型到本地

### 性能优化

1. **GPU 加速**
   ```bash
   # 安装 CUDA 版本的 PyTorch
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
   
   # 设置使用 GPU
   export WHISPER_DEVICE=cuda
   ```

2. **内存优化**
   ```bash
   # 使用 int8 量化 (ASR)
   export WHISPER_COMPUTE_TYPE=int8
   ```

3. **TTS 优化**
   ```bash
   # 启用 GPU 加速 (TTS)
   export TTS_GPU=true

   # 使用更快的模型 (如果可用)
   export TTS_MODEL_NAME=tts_models/en/ljspeech/tacotron2-DDC_ph
   ```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 🧪 测试 TTS 服务

项目提供了完整的 TTS 测试工具：

```bash
# 运行 TTS 测试脚本
python test_tts.py
```

测试脚本会自动检查：
- ✅ 服务健康状态
- ✅ TTS 模型初始化
- ✅ 语音合成功能
- ✅ 文件保存功能
- ✅ 语言和说话人支持

## 📞 支持

如有问题，请通过以下方式联系：

- GitHub Issues: 提交技术问题
- Email: 发送邮件咨询

---

**注意**: 首次运行时，系统会自动下载所选的 Whisper 模型，这可能需要一些时间。模型文件会保存在本地，后续启动会更快。
 source /Users/<USER>/Desktop/local-Faster-Whisper/.venv/bin/activate
 python start.py --model=tiny
 python test_client.py --url http://localhost:8008 --file test.m4a



 local-Faster-Whisper/app_new.py:
新增别名接口
POST /whisper/transcribe → 等价 /transcribe
WS /whisper/ws/stream → 等价 /ws/stream
注册 TTS 别名路由（统一挂在 /whisper/tts 下）
GET /whisper/tts/info|models|languages|speakers|status
POST /whisper/tts/synthesize
POST /whisper/tts/synthesize/file
POST /whisper/tts/en/synthesize、/whisper/tts/en/synthesize/file
POST /whisper/tts/zh/synthesize、/whisper/tts/zh/synthesize/file
GET /whisper/health → 等价 /health
说明：Nginx 当前已将 /whisper/* 反代到 8001 根路径，因此前端请求 /whisper/... 实际会落到原路径（例如 /whisper/transcribe → /transcribe）。本次在服务内增加同名别名，是为在缺失反代或直连服务时也能直接命中一致的 REST 路径，前后路径完全对齐。

前端与反代对应关系

STT 上传: /whisper/transcribe（已存在，新增服务内别名）
实时 WS: /whisper/ws/stream（已存在，新增服务内别名）
TTS 系列: /whisper/tts/...（服务内原有 /tts/... 已实现，本次新增 /whisper/tts/... 别名）
验证建议

健康检查: curl http://<域名或IP>/whisper/health
STT: curl -F audio_file=@sample.wav http://<域名或IP>/whisper/transcribe
TTS 状态: curl http://<域名或IP>/whisper/tts/status
TTS 合成: curl -X POST http://<域名或IP>/whisper/tts/zh/synthesize -H 'Content-Type: application/json' -d '{"text":"你好世界"}' --output out.wav
WebSocket: 前端 StreamTest/ChatWindow 语音输入连接应为 /whisper/ws/stream