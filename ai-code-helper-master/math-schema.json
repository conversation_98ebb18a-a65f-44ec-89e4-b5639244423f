{"type": "object", "properties": {"id": {"type": "string", "description": "唯一ID（如 psle_2020_p1_q5）"}, "type": {"type": "string", "enum": ["concept", "example", "problem", "solution", "definition"], "description": "内容类型"}, "content": {"type": "string", "description": "题干或概念正文"}, "answer": {"type": ["string", "null"], "description": "答案或解答摘要"}, "latex": {"type": ["string", "null"], "description": "LaTeX 公式（便于渲染）"}, "sympy_expr": {"type": ["string", "null"], "description": "可计算表达式（便于验证答案）"}, "graph": {"type": ["string", "null"], "description": "关联图形文件路径或SVG"}, "topic": {"type": "string", "description": "主题（如 Algebra / Geometry）"}, "grade": {"type": "string", "description": "年级/水平（如 P5, Sec2）"}, "difficulty": {"type": "string", "enum": ["基础", "中等", "挑战"], "description": "难度等级"}, "source": {"type": "string", "description": "来源（试卷/教材/练习）"}, "metadata": {"type": "object", "properties": {"chapter": {"type": ["string", "null"], "description": "章节"}, "page": {"type": ["integer", "null"], "description": "页码"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "额外标签，方便检索"}}, "required": []}}, "required": ["id", "type", "content", "topic", "grade"]}