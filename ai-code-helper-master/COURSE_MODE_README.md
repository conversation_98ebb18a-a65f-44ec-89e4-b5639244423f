# 课程模式使用指南

## 🎓 课程模式介绍

课程模式提供了一个专为数学学习的RAG（检索增强生成）聊天界面，具有以下特色：

- 📚 **左侧知识面板**：展示数学公式定理、典型例题和解题方法
- 💬 **右侧聊天窗口**：支持RAG增强的智能数学问答
- 🔍 **知识库检索**：自动从数学知识库中检索相关内容
- 🎨 **现代化UI**：美观的界面设计，支持语音输入和图片上传

## 🚀 快速开始

### 1. 访问课程模式

启动Spring Boot应用后，通过以下方式访问：

```
http://localhost:8081/course/math-rag-chat
```

或使用简短路径：
```
http://localhost:8081/course
```

### 2. 使用方法

1. **浏览知识库**：左侧面板有三个标签页
   - 公式定理：数学公式和定理
   - 典型例题：经典数学题目
   - 解题方法：解题技巧和方法

2. **智能问答**：
   - 在输入框输入数学问题
   - 系统会自动进行知识库检索
   - AI基于检索到的知识提供答案

3. **多媒体支持**：
   - 支持语音输入（点击麦克风图标）
   - 支持图片上传（点击图片图标）

## 🔧 技术架构

### 后端配置

- **RAG检索**：使用嵌入模型进行向量检索
- **流式响应**：支持实时流式聊天
- **错误处理**：RAG检索失败时自动降级到普通聊天

### 前端特性

- **响应式设计**：适配不同屏幕尺寸
- **实时状态**：显示AI思考状态和连接状态
- **SSE支持**：使用Server-Sent Events进行流式通信

## 📊 状态指示器

- 🟢 **在线**：服务正常运行
- 🟡 **思考中**：正在检索知识库和生成回答
- 🔴 **错误**：服务暂时不可用

## ⚠️ 注意事项

### RAG检索状态

如果看到"知识库服务暂时不可用"的提示，说明：
- 嵌入模型服务不可用
- 知识库检索失败
- 系统会自动降级到普通聊天模式

### 故障排除

1. **页面无法加载**：
   - 检查Spring Boot应用是否正常启动
   - 确认端口8081没有被占用

2. **RAG检索失败**：
   - 检查嵌入模型配置（Gemini/ModelScope/本地）
   - 查看应用日志中的详细错误信息

## 🔗 相关配置

### 嵌入模型配置（application-postgresql.yml）

```yaml
embedding:
  model-type: gemini  # 或 local, modelscope
  gemini:
    api-key: YOUR_API_KEY
    model-name: gemini-embedding-001
```

### 知识库配置

系统会自动加载以下知识库文件：
- `data/volume-cube-detailed-guide.json`
- `data/final-test.json`

## 📝 更新日志

- **v1.0**：初始版本，支持RAG增强数学问答
- **v1.1**：添加错误处理和降级机制
- **v1.2**：优化UI界面和用户体验
