https://sgexam.com/primary-6-maths/  这里有一堆数学试卷 新加坡的小六

https://www.ixl.com/math/grade-6?noredir=true&partner=google&campaign=19759269241&adGroup=147421929260&gad_source=1&gad_campaignid=19759269241&gbraid=0AAAAADrr3ApN8_Gd99iJxBgk-z51Nhzb8&gclid=Cj0KCQjwqqDFBhDhARIsAIHTlksfS9GuUcZCku-60JS29zkdAD4Gr6tpgPryjnBzp1RdKtNq-izrXAYaAofUEALw_wcB  又找到一个小六数学题库


在新加坡教育里，MOE-aligned 指的就是：

👉 课程内容、练习题、教学方法，都和新加坡教育部 (Ministry of Education, MOE) 的官方大纲保持一致。

⸻

具体含义
 1. Syllabus 对齐
 • 比如数学、科学的考点、知识点顺序，全部按照新加坡教育部制定的 syllabus 来出题和讲解。
 2. 考试要求对齐
 • 新加坡的 PSLE (小六会考)、O-level、A-level 等考试，都有固定的出题方式和考点分布。
 • “MOE-aligned” 意味着 Geniebook 给的练习和课程，和这些官方考试要求一致。
 3. 教学方法对齐（CPA Approach）
 • 新加坡 MOE 强调 CPA（Concrete–Pictorial–Abstract）教学法：
 • 先用具体事物（Concrete）
 • 再用图示（Pictorial）
 • 最后抽象公式（Abstract）
 • 这是新加坡数学闻名世界的核心教学法。

https://www.pitaya.com.sg/download/list?grade=&subject=&categoryId=&pageSize=18&page=1

<EMAIL> 密码 123456

https://geniebook.com/tuition/primary-6/maths


 Manim（Python 动画库）：
这是由 3Blue1Brown 开发的数学动画引擎，能生成你看到的 几乎一模一样的可视化效果（函数动画、几何演示、数列数形结合）。
🔑 Gatekeep 可能就是在 Manim 或类似引擎的基础上封装，再加 prompt/AI 调度。
 • p5.js / Processing.js：
用 JavaScript 做交互式动画，适合网页版演示。
 • Desmos API（非开源但可嵌入）：
他们的函数绘图引擎和 Gatekeep 展示效果类似，可以集成。




新加坡 JC 学科资料获取渠道
 1. 官方课程大纲（Syllabus）
 • 来源：新加坡考试与评鉴局（SEAB）
 • 网站：seab.gov.sg
 • 内容：包含 GCE A-Level (JC 使用) 的详细大纲（数学、物理、化学、经济学、GP 等）。
 • 用途：你们可以基于大纲建立 知识点图谱。
 2. MOE（新加坡教育部）官网
 • 网站：moe.gov.sg
 • 内容：官方的中学 & JC 课程结构、教学目标。
 • 用途：帮助对齐不同年级的学习进度。
 3. 各大 JC 学院网站（例如 Hwa Chong Institution, Raffles Institution, Victoria JC）
 • 他们通常会公开部分教学资源、学科指南、学生手册。
 • 可以直接下载 PDF 作为素材。
 4. 公开题库/历年真题
 • 来源：
 • Ten-Year Series (TYS)（新加坡畅销的历年试卷系列，出版商 SAP Education）
 • 学生论坛（如 KiasuParents、Reddit r/SGExams）常有共享资料。
 • 用途：作为 错题—知识点对应 的关键数据源。
 5. 教材出版社资源
 • Marshall Cavendish, Shing Lee, Hodder Education 等出版社都有对应 A-Level 材料。
 • 很多时候能直接买到电子书或教师用书。

⸻

🚀 建议你的行动
 • 第一步（快速）： 先从 SEAB 官方大纲 入手，把每个学科拆成知识点树 → 这是知识库的“骨架”。
 • 第二步： 从 历年真题 + Ten-Year Series 补充错题样本 → 这是“血肉”。
 • 第三步： 找 社区/出版社资料 做扩展。





这样，你们就能在 1–2 周内拼出一个 数学/物理/化学/经济的知识库雏形，支持 demo。

https://www.seab.gov.sg/files/A%20Level%20Syllabus%20Sch%20Cddts/2025/8865_y25_sy.pdf