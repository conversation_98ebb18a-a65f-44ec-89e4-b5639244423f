# 从内存向量存储迁移到 PostgreSQL 向量数据库指南

本指南将帮助你将现有的 AI 代码助手项目从 `InMemoryEmbeddingStore` 迁移到基于 PostgresML 的持久化向量数据库。

## 🎯 迁移优势

| 特性 | InMemoryEmbeddingStore | PostgreSQL + pgvector |
|------|----------------------|----------------------|
| 数据持久化 | ❌ 重启丢失 | ✅ 永久保存 |
| 性能扩展 | ❌ 内存限制 | ✅ 索引优化 |
| 并发支持 | ⚠️ 有限 | ✅ 数据库级别 |
| 数据备份 | ❌ 不支持 | ✅ 数据库备份 |
| 查询灵活性 | ❌ 基础 | ✅ SQL + 向量 |
| 元数据存储 | ⚠️ 简单 | ✅ JSONB 支持 |

## 📋 前置要求

### 1. PostgresML 容器环境

确保你的 PostgresML 容器正在运行：

```bash
# 检查容器状态
docker ps | grep postgresml

# 如果没有运行，启动容器
docker run \
    -it \
    -v postgresml_data:/var/lib/postgresql \
    -p 5433:5432 \
    -p 8000:8000 \
    ghcr.io/postgresml/postgresml:2.9.3 \
    sudo -u postgresml psql -d postgresml
```

### 2. 验证数据库连接

```bash
# 连接测试
psql -h localhost -p 5433 -U postgresml -d postgresml

# 检查 pgvector 扩展
SELECT * FROM pg_extension WHERE extname = 'vector';
```

## 🔧 迁移步骤

### 步骤 1: 数据库初始化

运行数据库初始化脚本：

```bash
# 在项目根目录执行
psql -h localhost -p 5433 -U postgresml -d postgresml \
     -f src/main/resources/db/init_postgresml.sql
```

或者直接在 PostgresML 容器中执行：

```sql
-- 1. 创建 pgvector 扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 2. 创建向量表
CREATE TABLE IF NOT EXISTS embeddings (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB,
    embedding vector(768),
    file_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 创建索引
CREATE INDEX IF NOT EXISTS embeddings_embedding_idx 
ON embeddings USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);
```

### 步骤 2: 应用配置更新

你的 `application.yml` 已经包含了必要的数据库配置：

```yaml
spring:
  datasource:
    url: *******************************************
    username: postgresml
    password: 
    driver-class-name: org.postgresql.Driver
```

### 步骤 3: 依赖项检查

确保 `pom.xml` 包含了必要的依赖：

```xml
<!-- PostgreSQL数据库支持 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    <scope>runtime</scope>
</dependency>
<!-- 连接池 -->
<dependency>
    <groupId>com.zaxxer</groupId>
    <artifactId>HikariCP</artifactId>
</dependency>
```

### 步骤 4: 启动应用测试

```bash
# 启动应用
./mvnw spring-boot:run

# 查看启动日志，应该看到：
# 🗄️ 正在初始化 PostgreSQL 向量数据库...
# ✅ pgvector 扩展已创建或已存在
# ✅ embeddings 表已创建或已存在
# ✅ 向量索引已创建或已存在
# 🎉 PostgreSQL 向量数据库初始化完成！
```

## 🧪 验证迁移结果

### 1. 运行集成测试

```bash
# 运行 PostgreSQL 向量存储测试
./mvnw test -Dtest=PostgresVectorStoreTest

# 运行 RAG 服务测试
./mvnw test -Dtest=RagServiceTest
```

### 2. 检查数据库状态

```sql
-- 连接数据库
psql -h localhost -p 5433 -U postgresml -d postgresml

-- 查看表结构
\d embeddings

-- 查看数据
SELECT id, content, file_name, created_at FROM embeddings LIMIT 5;

-- 查看向量数量
SELECT COUNT(*) as total_vectors FROM embeddings;

-- 查看索引
\di embeddings*
```

### 3. 测试 API 接口

```bash
# 测试知识库统计
curl -X GET http://localhost:8081/api/ai/rag/stats

# 应该返回类似：
{
  "storeType": "PostgreSQL 向量数据库 (pgvector)",
  "description": "包含Java学习路线、面试题、求职指南、项目建议等知识文档，支持持久化存储和高性能向量检索",
  "documentCount": 4,
  "embeddingModel": "text-embedding-004"
}

# 测试 RAG 搜索
curl -X POST http://localhost:8081/api/ai/rag/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Java学习路线", "maxResults": 3}'
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 数据库连接失败

**错误**: `Connection refused` 或 `Could not connect to server`

**解决方案**:
```bash
# 检查容器状态
docker ps | grep postgresml

# 重启容器
docker start <container_id>

# 检查端口
netstat -tlnp | grep 5433
```

#### 2. pgvector 扩展未安装

**错误**: `extension "vector" does not exist`

**解决方案**:
```sql
-- 连接数据库并创建扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 验证安装
SELECT * FROM pg_extension WHERE extname = 'vector';
```

#### 3. 向量维度不匹配

**错误**: `dimension mismatch` 或 `vector dimension`

**解决方案**:
- 确认 text-embedding-004 模型的向量维度是 768
- 检查数据库表定义中的向量维度设置

#### 4. 权限问题

**错误**: `permission denied` 或 `access denied`

**解决方案**:
```sql
-- 检查用户权限
\du postgresml

-- 赋予必要权限
GRANT ALL PRIVILEGES ON DATABASE postgresml TO postgresml;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgresml;
```

#### 5. Google AI API 配置问题

**错误**: `Invalid API key` 或 `Authentication failed`

**解决方案**:
- 检查 `application.yml` 中的 API 密钥配置
- 验证 API 密钥是否有效且有足够的配额

## 📊 性能优化建议

### 1. 向量索引优化

```sql
-- 调整 IVFFlat 索引参数
DROP INDEX IF EXISTS embeddings_embedding_idx;
CREATE INDEX embeddings_embedding_idx 
ON embeddings USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 200);  -- 根据数据量调整

-- 为大数据集考虑 HNSW 索引
CREATE INDEX embeddings_hnsw_idx 
ON embeddings USING hnsw (embedding vector_cosine_ops);
```

### 2. 数据库连接池优化

在 `application.yml` 中调整连接池设置：

```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### 3. 查询性能优化

```sql
-- 添加复合索引
CREATE INDEX embeddings_metadata_file_idx 
ON embeddings USING GIN (metadata, file_name);

-- 分析表统计信息
ANALYZE embeddings;
```

## 🔄 数据迁移（如需要）

如果你有现有的向量数据需要迁移：

### 1. 导出现有数据

```java
// 在迁移前运行的一次性代码
@Component
public class DataMigrationService {
    
    public void exportVectorData() {
        // 从 InMemoryEmbeddingStore 导出数据的逻辑
        // 保存为 JSON 或其他格式
    }
}
```

### 2. 导入到 PostgreSQL

```java
public void importVectorData(String dataFile) {
    // 读取导出的数据并批量插入到 PostgreSQL
    PostgresVectorStore postgresStore = new PostgresVectorStore(dataSource, embeddingModel);
    // 批量插入逻辑
}
```

## 🎉 迁移完成检查清单

- [ ] PostgresML 容器正常运行
- [ ] pgvector 扩展已安装
- [ ] embeddings 表已创建
- [ ] 向量索引已建立
- [ ] 应用启动成功
- [ ] 集成测试通过
- [ ] API 接口正常响应
- [ ] 数据持久化验证
- [ ] 性能表现满意

## 📝 注意事项

1. **数据备份**: 在生产环境中，请确保定期备份 PostgreSQL 数据库
2. **监控**: 建议设置数据库性能监控和告警
3. **扩容**: 随着数据量增长，考虑数据库硬件升级或分片策略
4. **安全**: 在生产环境中设置强密码和适当的网络安全策略

完成迁移后，你的 AI 代码助手将拥有持久化的向量存储能力，支持更大规模的知识库和更高的并发访问！ 