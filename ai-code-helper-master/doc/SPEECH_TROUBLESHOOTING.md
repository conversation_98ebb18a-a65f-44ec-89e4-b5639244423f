# 语音合成故障排除指南

## 常见问题解决方案

### 1. 连接失败错误 (ConnectionFailure)

**错误信息:** 
```
语音合成被取消: Error, 错误代码: ConnectionFailure, 错误详情: Connection failed (no connection to the remote host). Internal error: 11. Error details: Failed with error: WS_OPEN_ERROR_UNDERLYING_IO_ERROR wss://eastus.tts.speech.microsoft.com/cognitiveservices/websocket/v1
```

**可能原因:**
1. 网络连接问题
2. 防火墙阻止了连接
3. 代理服务器配置问题
4. Azure服务区域暂时不可用
5. DNS解析问题

**解决步骤:**

#### 步骤1: 测试网络连接
```bash
# 测试网络连接
curl -I https://eastus.api.cognitive.microsoft.com

# 测试DNS解析
nslookup eastus.tts.speech.microsoft.com
```

#### 步骤2: 检查防火墙设置
确保以下域名和端口可以访问：
- `*.api.cognitive.microsoft.com` (HTTPS, 端口443)
- `*.tts.speech.microsoft.com` (HTTPS/WSS, 端口443)
- `*.cognitiveservices.azure.com` (HTTPS, 端口443)

#### 步骤3: 尝试不同的Azure区域
在 `application-postgresql.yml` 中更改区域：
```yaml
azure:
  speech:
    region: "westus2"  # 尝试其他区域
```

可用区域列表：
- `eastus`, `eastus2`
- `westus`, `westus2`, `westcentralus`
- `southcentralus`, `northcentralus`
- `westeurope`, `northeurope`
- `southeastasia`, `eastasia`

#### 步骤4: 检查代理设置
如果使用企业网络，可能需要配置代理：
```bash
# 设置HTTP代理
export http_proxy=http://proxy.company.com:8080
export https_proxy=http://proxy.company.com:8080

# 或在Java启动参数中设置
-Dhttp.proxyHost=proxy.company.com -Dhttp.proxyPort=8080
-Dhttps.proxyHost=proxy.company.com -Dhttps.proxyPort=8080
```

### 2. 认证失败错误

**错误信息:**
```
Authentication error (401). Please check subscription information and region name
```

**解决方案:**
1. 验证Azure语音服务密钥是否正确
2. 确认服务区域与密钥匹配
3. 检查Azure订阅是否有效

### 3. 超时错误

**错误信息:**
```
TimeoutException
```

**解决方案:**
1. 增加超时时间设置
2. 检查网络稳定性
3. 尝试较短的文本内容

### 4. 服务不可用错误

**错误信息:**
```
Service unavailable (503)
```

**解决方案:**
1. 检查Azure服务状态：https://azure.status.microsoft
2. 等待几分钟后重试
3. 切换到其他Azure区域

## 故障排除工具

### 使用内置网络测试接口
```bash
# 测试网络连接
curl http://localhost:8081/api/ai/speech/test-network
```

### 查看详细日志
在 `application-postgresql.yml` 中启用详细日志：
```yaml
logging:
  level:
    com.hujun.aicodehelper.services.SpeechSynthesisService: DEBUG
    com.microsoft.cognitiveservices.speech: DEBUG
```

### 检查Azure服务健康状态
访问 Azure 状态页面：https://azure.status.microsoft/status

## 最佳实践

### 1. 网络优化
- 使用稳定的网络连接
- 避免在网络高峰期使用
- 考虑使用CDN或就近的Azure区域

### 2. 错误处理
- 实现重试机制（已内置）
- 使用备用区域
- 提供用户友好的错误信息

### 3. 监控和告警
- 设置服务可用性监控
- 配置连接失败告警
- 记录详细的错误日志

## 联系支持

如果以上解决方案都无效，请：

1. 收集以下信息：
   - 完整的错误信息
   - 使用的Azure区域
   - 网络配置信息
   - 尝试的时间和频率

2. 联系Azure技术支持：
   - Azure Portal → 帮助+支持 → 新建支持请求
   - 选择"技术"问题类型
   - 服务类型选择"认知服务 - 语音"

3. 或在GitHub上报告问题：
   - 提供详细的环境信息
   - 包含重现步骤
   - 附上相关日志

## 临时解决方案

如果语音服务暂时不可用，可以考虑：

1. **使用浏览器TTS**：
   - 在前端使用Web Speech API
   - 不依赖外部服务
   - 但功能和质量有限

2. **本地TTS服务**：
   - 使用开源TTS引擎
   - 如eSpeak、Festival等
   - 需要额外的部署和配置

3. **其他云服务**：
   - Google Cloud Text-to-Speech
   - Amazon Polly
   - 百度语音合成等

记住定期检查和更新Azure服务密钥，并监控服务的使用配额。