# 增量向量化测试指南

## 修复内容

已成功实施了增量更新机制，解决了每次启动都要重新向量化的问题：

### 1. 新增功能

#### PostgresVectorStore 新增方法：
- `hasVectorsForFile(fileName)`: 检查文件是否已有向量数据
- `hasVectorForContentHash(contentHash)`: 检查内容哈希是否已存在
- `getExistingFileNames()`: 获取已存在的文件名列表
- `removeVectorsForFile(fileName)`: 删除指定文件的向量数据

#### DocumentHashUtil 工具类：
- `calculateContentHash(content)`: 计算内容SHA-256哈希
- `calculateFileHash(filePath)`: 计算文件内容哈希
- `generateDocumentId(fileName, contentHash)`: 生成文档唯一标识

#### RagConfig 增量更新逻辑：
- 检查现有向量数据数量
- 对比文件列表，识别新文件和变更文件
- 使用内容哈希检测文件内容变化
- 只处理需要更新的文档
- 详细的启动日志，显示处理和跳过的文档数量

### 2. 工作流程

1. **启动时检查**: 检查数据库中现有向量数量
2. **文件对比**: 比较文件系统中的文档与数据库中的记录
3. **内容哈希验证**: 对于已存在的文件，验证内容是否发生变化
4. **增量处理**: 只对新文件或变更文件进行向量化
5. **统计报告**: 显示总文档数、已处理数、跳过数

### 3. 日志输出示例

```
=== 开始初始化 RAG 知识库 ===
📊 数据库中现有向量数量: 245
📄 正在从Markdown文件加载文档...
✅ 发现 4 篇Markdown文档
🔍 正在检查哪些文档需要重新处理...
📋 数据库中已存在 3 个文件的向量数据
🔄 检测到文件内容变化，重新处理: 程序员常见面试题.md
📚 总文档数: 4, 需要处理: 2, 跳过: 2
📄 待处理文档 1: 新技术文档.md (长度: 5432 字符)
📄 待处理文档 2: 程序员常见面试题.md (长度: 3456 字符)
✂️ 正在切割文档，最大片段长度: 1000字符，重叠: 200字符
🚀 开始向量化处理 2 篇文档，使用当前配置的嵌入模型
✅ 向量化完成！耗时: 1234 毫秒
🎉 RAG 知识库初始化完成！
=== RAG 配置信息 ===
📚 总文档数量: 4
🔄 已处理文档: 2
⏭️ 跳过文档: 2
📊 向量数据总量: 312
```

## 测试步骤

### 1. 首次启动测试
```bash
# 启动应用，观察所有文档被处理
./mvnw spring-boot:run
```

### 2. 重启测试
```bash
# 重启应用，观察文档被跳过
./mvnw spring-boot:run
```

### 3. 文档变更测试
```bash
# 修改某个markdown文件内容
echo "新增内容" >> src/main/resources/docs/json_markdown_output/test.md

# 重启应用，观察只有变更的文件被重新处理
./mvnw spring-boot:run
```

### 4. 新文档测试
```bash
# 添加新的markdown文件
cp existing.md src/main/resources/docs/json_markdown_output/new_doc.md

# 重启应用，观察只有新文件被处理
./mvnw spring-boot:run
```

## 预期效果

- **首次启动**: 处理所有文档，耗时较长
- **正常重启**: 跳过所有已处理文档，启动快速
- **文档变更**: 只重新处理变更的文档
- **新增文档**: 只处理新增的文档
- **大幅减少**: 重复向量化的时间和资源消耗

## 性能优化

- 避免重复计算向量嵌入
- 减少数据库重复插入
- 启动时间显著缩短
- 资源使用更加高效