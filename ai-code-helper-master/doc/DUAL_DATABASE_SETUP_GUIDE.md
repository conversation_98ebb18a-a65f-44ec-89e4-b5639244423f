# 双数据库架构配置指南

## 🎯 架构概述

本项目采用**双数据库架构**，实现了MySQL和PostgreSQL的完美结合：

| 数据库 | 用途 | 技术栈 | 数据类型 |
|--------|------|--------|----------|
| **MySQL** | 聊天记忆存储 | JPA + Hibernate | 关系型数据 |
| **PostgreSQL** | 向量数据库 | pgvector + 原生SQL | 向量嵌入 |

### 🏗️ 架构优势

1. **专业化分工**：每个数据库专注于自己擅长的领域
2. **性能优化**：向量搜索使用pgvector，关系数据使用MySQL
3. **扩展性强**：可独立扩展和优化
4. **数据安全**：双重备份，降低数据丢失风险

## 📁 配置文件结构

```
src/main/resources/
├── application.yml                    # 主配置文件
├── application-ai.yml                 # AI相关配置
├── application-mysql.yml              # MySQL单独配置(旧)
└── application-postgresql.yml         # 双数据库配置(新)

src/main/java/com/hujun/aicodehelper/config/
├── MultiDataSourceConfig.java         # 多数据源配置
└── RepositoryConfig.java             # Repository配置
```

## 🚀 快速启动

### 1. 激活双数据库配置

确保 `application.yml` 中激活了正确的配置：

```yaml
spring:
  profiles:
    active: ai,postgresql  # 激活AI和PostgreSQL配置
```

### 2. 启动相关服务

#### MySQL 服务
```bash
# 确保MySQL服务正在运行
mysql -h ************ -u litemall -p
```

#### PostgreSQL 服务 (postgresml容器)
```bash
# 启动PostgresML容器
docker run \
    -it \
    -v postgresml_data:/var/lib/postgresql \
    -p 5433:5432 \
    -p 8000:8000 \
    ghcr.io/postgresml/postgresml:2.9.3

# 验证连接
psql -h localhost -p 5433 -U postgresml -d postgresml
```

### 3. 初始化PostgreSQL向量数据库

```bash
# 执行初始化脚本
psql -h localhost -p 5433 -U postgresml -d postgresml \
     -f src/main/resources/db/init_postgresml.sql
```

### 4. 启动应用

```bash
./mvnw spring-boot:run
```

## 🔧 详细配置说明

### 多数据源配置 (`MultiDataSourceConfig.java`)

```java
@Configuration
@EnableTransactionManagement
public class MultiDataSourceConfig {
    
    // MySQL数据源 - 主数据源，用于聊天记忆
    @Bean
    @Primary
    public DataSource mysqlDataSource() { ... }
    
    // PostgreSQL数据源 - 用于向量存储
    @Bean("postgresqlDataSource")
    public DataSource postgresqlDataSource() { ... }
}
```

### 连接池配置

#### MySQL连接池
```yaml
spring:
  datasource:
    mysql:
      hikari:
        pool-name: MySQLPool
        maximum-pool-size: 10
        minimum-idle: 2
        connection-timeout: 30000
```

#### PostgreSQL连接池
```yaml
spring:
  datasource:
    postgresql:
      hikari:
        pool-name: PostgreSQLPool
        maximum-pool-size: 15
        minimum-idle: 3
        connection-timeout: 30000
```

## 📊 数据库表结构

### MySQL 表 (聊天记忆)

```sql
-- 聊天消息表
CREATE TABLE chat_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    memory_id VARCHAR(255) NOT NULL,
    message_order INT NOT NULL,
    message_type VARCHAR(50) NOT NULL,
    message_content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_memory_id (memory_id),
    INDEX idx_message_order (message_order)
);
```

### PostgreSQL 表 (向量存储)

```sql
-- 向量嵌入表
CREATE TABLE embeddings (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB,
    embedding vector(768),  -- Google AI text-embedding-004
    file_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 向量索引
CREATE INDEX embeddings_embedding_idx 
ON embeddings USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);
```

## 🔍 使用示例

### 聊天记忆存储 (MySQL)

```java
@Service
public class ChatService {
    
    @Autowired
    private MySqlChatMemoryStore chatMemoryStore;
    
    public void saveChat(String userId, List<ChatMessage> messages) {
        chatMemoryStore.updateMessages(userId, messages);
    }
    
    public List<ChatMessage> getHistory(String userId) {
        return chatMemoryStore.getMessages(userId);
    }
}
```

### 向量搜索 (PostgreSQL)

```java
@Service
public class RagService {
    
    @Autowired
    @Qualifier("postgresqlDataSource")
    private DataSource postgresqlDataSource;
    
    public List<EmbeddingMatch<TextSegment>> search(String query) {
        // 使用PostgresVectorStore进行向量搜索
        return embeddingStore.findRelevant(embedding, maxResults);
    }
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. 数据源配置冲突
**错误**: `Multiple qualifying beans of type 'DataSource'`

**解决方案**:
确保使用了 `@Qualifier` 注解指定数据源：
```java
@Autowired
@Qualifier("postgresqlDataSource")
private DataSource postgresqlDataSource;
```

#### 2. PostgreSQL连接失败
**错误**: `Connection refused`

**解决方案**:
```bash
# 检查容器状态
docker ps | grep postgresml

# 重启容器
docker start <container_id>
```

#### 3. MySQL聊天记忆表不存在
**错误**: `Table 'chat_messages' doesn't exist`

**解决方案**:
确保JPA自动建表已启用：
```yaml
spring:
  jpa:
    hibernate:
      ddl-auto: update
```

#### 4. 向量维度不匹配
**错误**: `dimension mismatch`

**解决方案**:
检查配置文件中的向量维度设置：
```yaml
embedding:
  google-ai:
    dimension: 768  # Google AI text-embedding-004
  local:
    dimension: 1024  # BAAI/bge-m3
```

## 📈 性能优化建议

### 1. MySQL优化
```yaml
spring:
  datasource:
    mysql:
      hikari:
        maximum-pool-size: 20  # 根据并发量调整
        leak-detection-threshold: 60000
```

### 2. PostgreSQL优化
```sql
-- 调整向量索引参数
CREATE INDEX embeddings_hnsw_idx 
ON embeddings USING hnsw (embedding vector_cosine_ops);

-- 定期分析表
ANALYZE embeddings;
```

### 3. 应用层优化
- 使用连接池监控
- 配置合适的事务隔离级别
- 实现读写分离（如需要）

## 🔒 安全配置

### 1. 数据库连接安全
```yaml
spring:
  datasource:
    mysql:
      # 生产环境建议使用SSL
      url: *************************************
    postgresql:
      # 配置SSL连接
      url: **********************************************
```

### 2. 敏感信息管理
```yaml
# 使用环境变量
spring:
  datasource:
    mysql:
      username: ${MYSQL_USERNAME:litemall}
      password: ${MYSQL_PASSWORD:}
    postgresql:
      username: ${POSTGRES_USERNAME:postgresml}
      password: ${POSTGRES_PASSWORD:}
```

## 🚀 部署注意事项

### 开发环境
- 使用 `ddl-auto: update`
- 启用SQL日志调试
- 使用较小的连接池

### 生产环境
- 使用 `ddl-auto: validate`
- 关闭SQL日志
- 配置合适的连接池大小
- 实施数据库备份策略
- 配置监控和告警

## 📝 维护清单

- [ ] 定期备份MySQL聊天记忆数据
- [ ] 定期备份PostgreSQL向量数据
- [ ] 监控数据库连接池状态
- [ ] 检查向量索引性能
- [ ] 更新数据库驱动版本
- [ ] 审查数据库安全配置

完成配置后，你将拥有一个强大的双数据库AI代码助手系统！🎉