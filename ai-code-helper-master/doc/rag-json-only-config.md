# RAG配置 - 仅JSON数据加载

## 概述

已成功修改 `RagConfig.java`，移除了Markdown文件加载功能，专注于从 `data` 目录加载JSON数据到RAG系统中。

## 主要修改

### 1. 简化文档加载流程

```java
// 加载JSON文档
log.info("📄 正在从JSON文件加载文档...");
List<Document> allDocuments = loadJsonDocuments();
log.info("✅ 发现 {} 篇JSON文档", allDocuments.size());
```

### 2. 移除的功能

- ✅ 移除了 `loadAllDocuments()` 方法
- ✅ 移除了 `loadMarkdownDocuments()` 方法  
- ✅ 移除了Markdown文件扫描和处理逻辑
- ✅ 简化了文档加载流程

### 3. 保留的核心功能

- ✅ JSON数据加载和解析
- ✅ 基于math-schema.json的数据结构处理
- ✅ 增量加载（避免重复处理）
- ✅ PostgreSQL向量存储集成
- ✅ 详细的日志记录和错误处理

## JSON数据处理特性

### 支持的数据结构

基于 `math-schema.json` 规范，支持以下字段：

**必需字段：**
- `id`: 唯一标识符
- `type`: 内容类型（concept, example, problem, solution, definition）
- `content`: 题干或概念正文
- `topic`: 主题（如 Algebra, Geometry）
- `grade`: 年级/水平（如 P5, Sec2）

**可选字段：**
- `answer`: 答案或解答摘要
- `latex`: LaTeX公式
- `sympy_expr`: SymPy表达式
- `graph`: 图形文件路径或SVG
- `difficulty`: 难度等级（基础, 中等, 挑战）
- `source`: 来源信息
- `images`: 图片数组
- `steps`: 解题步骤数组
- `metadata`: 额外元数据

### 数据转换示例

JSON数据会被转换为结构化的文档内容：

```
ID: ratio_p6_concept_1
类型: concept
主题: Ratio
年级: P6
难度: 基础
来源: geniebook

内容: A ratio tells us the relationship between two or more quantities. It may not represent the actual number.

图片信息:
- 图片标题: Equivalent Ratio
- 图片描述: Equivalent ratios are shown in a table...
```

### 元数据丰富化

每个文档都包含详细的元数据：

```java
metadataMap.put("file_name", "final-test.json");
metadataMap.put("file_type", "JSON");
metadataMap.put("source_type", "math_data");
metadataMap.put("math_id", "ratio_p6_concept_1");
metadataMap.put("math_type", "concept");
metadataMap.put("math_topic", "Ratio");
metadataMap.put("math_grade", "P6");
metadataMap.put("math_difficulty", "基础");
```

## 增量加载机制

系统会自动检测和处理：

1. **首次运行**: 加载所有JSON文件中的数据
2. **后续启动**: 只处理新增或修改的文件
3. **内容变化检测**: 使用SHA-256哈希值检测文件内容变化
4. **重复数据避免**: 已处理的数据不会重复加载

## 启动日志示例

```
=== 开始初始化 RAG 知识库 ===
📊 数据库中现有向量数量: 0
📄 正在从JSON文件加载文档...
📁 找到 2 个JSON文件
✅ 成功加载JSON: final-test.json (包含 1 个项目)
✅ 成功加载JSON: volume-cube-detailed-guide.json (包含 1 个项目)
📊 JSON文件扫描完成 - 总文件: 2, 成功加载: 2, 总项目: 2
✅ 发现 2 篇JSON文档
📚 总文档数: 2, 需要处理: 2, 跳过: 0
🚀 开始向量化处理 2 篇文档，使用当前配置的嵌入模型
✅ 向量化完成！耗时: 1234 毫秒
🎉 RAG 知识库初始化完成！
```

## 使用效果

用户现在可以通过聊天界面查询：

- 数学概念和定义
- 具体的数学问题和解答
- 按年级和主题筛选的内容
- 包含图片和步骤的详细解释

系统会基于向量相似度返回最相关的数学教育内容，支持中英文查询。

## 配置要求

确保 `data` 目录存在并包含符合 `math-schema.json` 规范的JSON文件。系统会自动扫描该目录下的所有 `.json` 文件。
