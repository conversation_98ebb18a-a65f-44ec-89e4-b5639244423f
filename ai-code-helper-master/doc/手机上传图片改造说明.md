# 桌面扫码 · 手机拍照上传 — 改造说明与实现手册

本文档描述本次“桌面扫码，移动端操作（Desktop-to-Mobile Handoff）”能力在当前代码库（前端：Vue 3 + Vite；后端：Spring Boot）中的完整实现，包括目标、架构、接口、前端页面、配置、测试步骤与注意事项。

## 1. 改造目标

- 支持桌面端展示二维码，手机端扫码进入上传页，拍照/相册选择后上传题目图片。
- 实时反馈桌面端状态（等待/已扫码/上传中/已上传/失效）。
- 上传完成后，桌面端自动拿到图片 URL 并闭环（在聊天输入预览区显示、可直接发送）。
- 采用 SSE 实时推送，提供轮询降级；Token 一次性使用、3 分钟 TTL。

## 2. 总体设计

- 标识设计：
  - `sessionId`（桌面端持有）：用于 SSE/轮询监听状态。
  - `token`（仅嵌入二维码）：一次性使用，上传校验并使用后作废。
- 交互流程：
  1. 桌面端在聊天窗口点击“手机拍照上传” → 调用后端创建上传会话 → 展示二维码（内含 `token`）。
  2. 手机扫码打开 H5 上传页 `/m/upload?token=...` → 调用 `mark-scanned` 标记已扫码。
  3. 手机选择/拍照后（前端压缩）通过 `POST /upload?token=...` 上传图片。
  4. 后端校验/保存图片，推送 `uploaded` 事件到桌面端 → 桌面端自动显示图片并可发送。
- 文件存储：先落本地 `data/uploads/yyyy/MM/dd/uuid.ext`，通过后端 URL 返回访问地址。

## 3. 后端实现（Spring Boot）

### 3.1 新增代码

- 模型与服务
  - `mobileupload/UploadStatus.java`：状态枚举（`WAITING/SCANNED/UPLOADING/UPLOADED/EXPIRED/CANCELLED/USED`）。
  - `mobileupload/UploadEvent.java`：SSE 事件载体（`type/imageUrl/timestamp`）。
  - `mobileupload/UploadSession.java`：会话模型（`sessionId/token/memoryId/status/…`）。
  - `mobileupload/UploadSessionService.java`：内存存储会话、SSE sink、文件保存、状态推进与过期清理（TTL 3 分钟，10MB 限制，图片类型校验）。
  - `mobileupload/SchedulingConfig.java`：定时清理过期会话（每 60 秒）。
- 控制器
  - `mobileupload/MobileUploadController.java`：对外接口与文件读取。

### 3.2 接口定义（均在 `server.servlet.context-path: /api` 下）

- 创建会话
  - `POST /api/mobile-upload/session/start`
  - 入参：`{ memoryId: string }`
  - 出参：`{ sessionId, token, expiresAt }`

- 会话事件流（SSE）
  - `GET /api/mobile-upload/session/stream?sessionId=...`
  - 事件类型：`waiting`（默认）/`scanned`/`uploading`/`uploaded{imageUrl}`/`expired`

- 轮询状态（降级）
  - `GET /api/mobile-upload/session/status?sessionId=...`
  - 返回：`{ sessionId, status, imageUrl, expiresAt }`

- 标记已扫码
  - `POST /api/mobile-upload/mark-scanned?token=...`

- 上传图片
  - `POST /api/mobile-upload/upload?token=...`（multipart，字段名：`file`）
  - 成功返回：`{ imageUrl }`；失败场景返回 4xx/5xx（过期、已用、类型/大小非法等）。

- 文件访问
  - `GET /api/mobile-upload/file/{yyyy}/{mm}/{dd}/{filename}`（带缓存头与正确 MIME）。

### 3.3 约束与安全

- Token：一次性使用、3 分钟 TTL，使用后即作废。
- 大小/类型校验：≤ 10MB，`image/jpeg|png|gif|webp`。
- 过期清理：定时任务将 `WAITING/SCANNED/UPLOADING` 等非完成态过期会话标记为 `EXPIRED` 并推送事件。

## 4. 前端实现（Vue 3 + Vite）

### 4.1 新增文件

- API 辅助：`ai-k12-frontend/src/api/mobileUploadApi.js`
  - `startMobileUploadSession(memoryId)`、`openUploadSSE(sessionId, ...)`、`getSessionStatus(sessionId)`
  - `buildMobileUploadUrl(token)`（支持 `VITE_MOBILE_BASE_URL` 覆盖）

- H5 上传页：`ai-k12-frontend/src/components/mobile/MobileUpload.vue`
  - 路由：`/m/upload`
  - 逻辑：读取 URL `token` → `mark-scanned` → 用户拍照/选择 → 压缩（最长边 1600px，质量 0.8）→ `POST upload` → 成功提示并 3s 可关闭。

- 桌面二维码弹窗：`ai-k12-frontend/src/components/mobile/DesktopQrModal.vue`
  - 打开时：创建会话、生成二维码、开启 SSE、倒计时（基于 `expiresAt`）。
  - 状态文案：`等待扫描/已扫描/上传中/上传完成/已失效`。
  - 事件：上传完成时 `emit('uploaded', { imageUrl })` → 由聊天窗口消费。

- 路由注册：`ai-k12-frontend/src/router/index.js`
  - 新增：`{ path: '/m/upload', name: 'MobileUpload', component: MobileUpload }`

- 聊天集成：`ai-k12-frontend/src/components/ChatWindow.vue`
  - 新增“手机拍照上传”按钮，调起 `DesktopQrModal`。
  - `@uploaded` 回调将 `imageUrl` 写入 `selectedImage` 并关闭弹窗。
  - 支持“仅图片也能发送”：若无文本则默认使用提示语“请解析我上传的图片”。

- 配置：`ai-k12-frontend/src/config/env.js`
  - 新增 `getMobileBaseUrl()`；`appConfig.api.baseUrl` 已沿用。

### 4.2 二维码渲染

- 目前使用公共二维码服务：`https://api.qrserver.com/v1/create-qr-code/?data=...` 生成图片。
- 可替换为本地包 `qrcode` 或 `qrcode.vue`，以避免外网依赖。

## 5. 配置项

- `.env`（可选）
  - `VITE_API_URL=http://localhost:8081/api`
  - `VITE_MOBILE_BASE_URL=http://<LAN_IP>:3000`（手机端可访问的前端地址；若同域可不配）

## 6. 本地测试步骤

1) 启动后端（默认 `:8081`，`/api` 作为 context-path）。

2) 启动前端：
```bash
cd ai-k12-frontend
npm run dev
```

3) 桌面端在聊天页点击“手机拍照上传” → 弹出二维码与 3 分钟倒计时。

4) 用手机扫码（确保手机能访问二维码中的 H5 地址；如为 `localhost`，需改为 `VITE_MOBILE_BASE_URL=电脑局域网 IP`）。

5) 手机拍照/相册选择 → 压缩 → 上传。桌面端状态从“已扫描/上传中”变为“上传完成”，弹窗关闭，聊天输入区显示图片预览，点击“发送”即可提交。

6) 验证异常场景：二维码失效、重复上传、超大小/非法类型等，提示正常。

## 7. 重要注意事项

- HTTPS：移动浏览器/微信内置浏览器启用摄像头通常需要 HTTPS（开发期可用本地或局域网 IP）。
- 跨域：建议同域部署；如需跨域，请按需配置 CORS，至少允许 `POST /mobile-upload/*`。
- 安全：Token 短时且一次性；服务端严格校验图片类型与大小；建议增设频控与风控策略。
- 存储：当前为本地目录，生产建议迁移到对象存储与签名 URL。

## 8. 目录与变更清单

后端新增：

```
src/main/java/com/hujun/aicodehelper/mobileupload/UploadStatus.java
src/main/java/com/hujun/aicodehelper/mobileupload/UploadEvent.java
src/main/java/com/hujun/aicodehelper/mobileupload/UploadSession.java
src/main/java/com/hujun/aicodehelper/mobileupload/UploadSessionService.java
src/main/java/com/hujun/aicodehelper/mobileupload/MobileUploadController.java
src/main/java/com/hujun/aicodehelper/mobileupload/SchedulingConfig.java
```

前端新增/修改：

```
ai-k12-frontend/src/api/mobileUploadApi.js
ai-k12-frontend/src/components/mobile/MobileUpload.vue
ai-k12-frontend/src/components/mobile/DesktopQrModal.vue
ai-k12-frontend/src/router/index.js           # 新增 /m/upload 路由
ai-k12-frontend/src/components/ChatWindow.vue # 接入二维码弹窗与 image-only 发送逻辑
ai-k12-frontend/src/config/env.js             # 新增 getMobileBaseUrl()
```

## 9. 后续可选优化

- 将二维码生成切换为内置库，避免外部依赖。
- 手机端完成上传后自动触发桌面端发送，减少一步操作（当前为写入预览后手动发送）。
- 引入 Redis 存储上传会话与事件流，支持多实例水平扩展。
- 对象存储与签名 URL，添加图片病毒扫描与内容合规检测。
- 微信内 H5 适配（JS-SDK 授权与 UX 优化）。

---

如需我将二维码生成切换为本地库或在上传完成后自动发送消息，请告知，我可以继续提交增量改造。

