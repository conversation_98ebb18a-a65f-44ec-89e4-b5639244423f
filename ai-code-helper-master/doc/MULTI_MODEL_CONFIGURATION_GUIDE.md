# 多模型配置化选择功能使用指南

## 概述

本系统实现了智能化的AI模型选择功能，能够根据不同的使用场景自动选择最合适的Gemini模型：

- **LITE模型** (`gemini-2.0-flash-thinking-exp`): 用于日常轻量级聊天
- **RAG模型** (`gemini-2.5-flash-lite-preview-06-17`): 用于知识库检索和RAG场景
- **DICTIONARY模型** (`gemini-2.5-flash`): 用于剑桥词典查询
- **PRO模型** (`gemini-2.5-pro`): 用于复杂任务和高级需求

## 配置文件说明

### application-postgresql.yml 配置

```yaml
langchain4j:
  google:
    ai:
      gemini:
        # 默认聊天模型 (保持兼容性)
        chat-model:
          api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
          model-name: gemini-2.5-flash-lite-preview-06-17
        
        # 多模型配置 - 根据使用场景选择
        models:
          # 轻量级聊天模型 (平时聊天)
          lite:
            api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
            model-name: gemini-2.0-flash-thinking-exp
            description: "轻量级模型，用于日常聊天"
            temperature: 0.7
            max-tokens: 4096
          
          # RAG专用模型
          rag:
            api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
            model-name: gemini-2.5-flash-lite-preview-06-17
            description: "RAG检索增强生成专用模型"
            temperature: 0.3
            max-tokens: 8192
          
          # 词典工具专用模型
          dictionary:
            api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
            model-name: gemini-2.5-flash
            description: "剑桥词典查询专用模型"
            temperature: 0.1
            max-tokens: 2048
          
          # 专业模型 (高级需求)
          pro:
            api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
            model-name: gemini-2.5-pro
            description: "专业模型，用于复杂任务和高级需求"
            temperature: 0.2
            max-tokens: 16384
```

## 核心组件

### 1. ModelSelectionStrategy
智能模型选择策略，负责分析用户输入并选择最合适的模型。

#### 选择规则：
- **词典查询检测**: 包含 "define", "meaning", "词典", "什么意思" 等关键词
- **RAG请求检测**: 包含 "搜索", "查找", "知识库", "学习资料" 等关键词  
- **复杂任务检测**: 消息长度>200字符，或包含 "分析", "详细", "设计" 等关键词
- **默认选择**: 其他情况使用轻量级模型

### 2. MultiGeminiModelConfig
多模型配置管理器，负责根据配置创建不同的Gemini模型实例。

### 3. DynamicAiServiceManager
动态AI服务管理器，负责根据场景选择和缓存AI服务实例。

### 4. SmartAiCodeHelperService
智能AI助手服务，集成了模型选择逻辑和优化策略。

## 使用方式

### 1. 自动模型选择

系统会根据用户输入自动选择合适的模型：

```java
// 日常聊天 - 自动选择LITE模型
String response = aiService.chat("你好，今天天气怎么样？");

// 知识库查询 - 自动选择RAG模型
String response = aiService.chatWithRag("搜索关于Spring Boot的学习资料");

// 词典查询 - 自动选择DICTIONARY模型
String response = aiService.chat("what does 'serendipity' mean?");

// 复杂任务 - 自动选择PRO模型
String response = aiService.chat("请详细分析微服务架构的优缺点，并设计一个完整的实施方案");
```

### 2. 显式模型选择

用户可以通过特定命令强制使用某个模型：

```java
// 用户消息: "使用pro模型分析这个问题"
// 系统会强制使用PRO模型

// 用户消息: "切换到轻量模型"  
// 系统会强制使用LITE模型
```

## REST API 管理接口

### 查看模型信息
```http
GET /api/models/info
```

### 获取模型统计
```http
GET /api/models/stats
```

### 刷新模型服务
```http
POST /api/models/refresh
```

### 测试特定模型
```http
POST /api/models/test/{modelType}?testMessage=Hello
```

### 获取模型建议
```http
POST /api/models/suggest
Content-Type: application/json

{
  "message": "用户输入的消息"
}
```

## 模型特性说明

### LITE模型 (gemini-2.0-flash-thinking-exp)
- **适用场景**: 日常聊天、简单问答
- **特点**: 响应快速、资源消耗低
- **配置**: temperature=0.7, max-tokens=4096

### RAG模型 (gemini-2.5-flash-lite-preview-06-17)
- **适用场景**: 知识库检索、学习资料查询
- **特点**: 集成内容检索器，适合知识问答
- **配置**: temperature=0.3, max-tokens=8192

### DICTIONARY模型 (gemini-2.5-flash)
- **适用场景**: 英语词典查询、单词解释
- **特点**: 集成剑桥词典工具，专业词汇解释
- **配置**: temperature=0.1, max-tokens=2048

### PRO模型 (gemini-2.5-pro)
- **适用场景**: 复杂分析、报告生成、设计任务
- **特点**: 功能最全，包含RAG和词典工具
- **配置**: temperature=0.2, max-tokens=16384

## 监控和调试

### 日志监控
系统会记录模型选择过程：
```
INFO: 收到聊天请求: 搜索Spring Boot教程
DEBUG: 检测到RAG请求，使用RAG模型  
INFO: 为会话 123 选择模型: RAG检索增强生成专用模型
```

### 性能监控
- 模型初始化时间
- 响应时间统计
- 模型可用性状态
- 缓存命中率

## 故障处理

### 模型降级机制
如果指定模型不可用，系统会自动降级到LITE模型。

### 服务恢复
通过 `/api/models/refresh` 接口可以重新初始化所有模型服务。

### 配置热更新
修改配置文件后，调用刷新接口即可应用新配置，无需重启应用。

## 扩展指南

### 添加新模型类型
1. 在 `ModelType` 枚举中添加新类型
2. 在配置文件中添加对应配置
3. 在 `ModelSelectionStrategy` 中添加选择逻辑
4. 在 `DynamicAiServiceManager` 中配置服务功能

### 自定义选择策略
继承或修改 `ModelSelectionStrategy` 类，实现自定义的模型选择逻辑。

## 注意事项

1. **API密钥安全**: 生产环境中应使用环境变量管理API密钥
2. **模型配额**: 注意各个模型的使用配额限制
3. **网络延迟**: 不同模型的响应时间可能不同
4. **功能兼容**: 确保所有模型都支持所需的功能特性