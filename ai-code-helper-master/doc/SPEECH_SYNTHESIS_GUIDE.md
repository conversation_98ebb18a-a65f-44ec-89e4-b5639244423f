# 语音合成功能使用指南

## 概述

本项目新增了语音合成功能，可以将AI生成的文章内容转换为语音文件。该功能基于Azure认知服务的语音合成API，支持多种语音选择和自定义配置。

## 主要功能

- 🎤 **文章语音合成**: 将AI生成的文章内容转换为高质量语音文件
- 🌍 **多语音支持**: 支持中文和英文多种语音选择
- 📁 **自动文件管理**: 自动创建输出目录，生成带时间戳的文件名
- ⚙️ **配置灵活**: 支持自定义语音、输出目录等配置

## API 接口

### 1. 基础语音合成

**接口**: `POST /api/ai/speech/synthesize`

**功能**: 将AI生成的文章内容转换为语音文件（使用默认中文女声）

**请求体**:
```json
{
  "text": "这是AI生成的文章内容...",
  "title": "文章标题"
}
```

**响应**:
```json
{
  "success": true,
  "message": "语音合成成功",
  "filePath": "./data/speech-output/文章标题_20240101_120000.wav",
  "fileName": "文章标题_20240101_120000.wav",
  "fileSize": 1024000
}
```

### 2. 自定义语音合成

**接口**: `POST /api/ai/speech/synthesize-with-voice`

**功能**: 使用指定的语音进行语音合成

**请求体**:
```json
{
  "text": "这是AI生成的文章内容...",
  "title": "文章标题",
  "voiceName": "zh-CN-YunxiNeural"
}
```

### 3. 获取支持的语音列表

**接口**: `GET /api/ai/speech/voices`

**功能**: 获取系统支持的所有语音选项

**响应**:
```json
{
  "success": true,
  "defaultVoice": "zh-CN-XiaoxiaoNeural",
  "voices": {
    "zh-CN-XiaoxiaoNeural": "中文女声（晓晓）",
    "zh-CN-YunxiNeural": "中文男声（云希）",
    "zh-CN-YunyangNeural": "中文男声（云扬）",
    "en-US-AriaNeural": "英文女声（Aria）",
    "en-US-JennyNeural": "英文女声（Jenny）",
    "en-US-GuyNeural": "英文男声（Guy）"
  }
}
```

## 配置说明

### Azure语音服务配置

在 `application-postgresql.yml` 中配置Azure语音服务：

```yaml
# Azure语音服务配置
azure:
  speech:
    # Azure语音服务密钥
    key: "your-azure-speech-key"
    # Azure语音服务区域
    region: "eastus"
    # 默认语音
    voice: "zh-CN-XiaoxiaoNeural"

# 语音合成输出配置
speech:
  output:
    # 语音文件输出目录
    directory: "./data/speech-output"
```

### 支持的语音列表

| 语音名称 | 语言 | 性别 | 描述 |
|---------|------|------|------|
| zh-CN-XiaoxiaoNeural | 中文 | 女 | 晓晓（温柔女声） |
| zh-CN-YunxiNeural | 中文 | 男 | 云希（成熟男声） |
| zh-CN-YunyangNeural | 中文 | 男 | 云扬（青年男声） |
| en-US-AriaNeural | 英文 | 女 | Aria（清晰女声） |
| en-US-JennyNeural | 英文 | 女 | Jenny（友好女声） |
| en-US-GuyNeural | 英文 | 男 | Guy（专业男声） |

## 使用场景

### 1. RAG问答后的语音播报

当用户通过RAG功能获得AI生成的回答后，可以请求将答案转换为语音：

```bash
# 1. 先通过RAG获取答案
curl -X POST http://localhost:8081/api/ai/rag/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "请介绍Java学习路线"
  }'

# 2. 将回答转换为语音
curl -X POST http://localhost:8081/api/ai/speech/synthesize \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Java学习路线包括...",
    "title": "Java学习路线介绍"
  }'
```

### 2. 文章内容语音化

对于AI生成的长篇文章，可以转换为语音文件供用户收听：

```javascript
// 前端JavaScript示例
async function synthesizeArticle(articleText, title) {
  const response = await fetch('/api/ai/speech/synthesize', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      text: articleText,
      title: title
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('语音文件生成成功:', result.fileName);
    // 可以提供下载链接或直接播放
  }
}
```

## 技术实现

### 核心服务类

**SpeechSynthesisService**: 负责语音合成的核心业务逻辑
- `synthesizeArticle()`: 基础文章语音合成
- `synthesizeArticleWithVoice()`: 自定义语音的文章合成
- `synthesizeSpeech()`: 底层语音合成方法

### 文件命名规则

生成的语音文件按以下规则命名：
- 格式：`{标题}_{时间戳}.wav`
- 时间戳格式：`yyyyMMdd_HHmmss`
- 特殊字符会被替换为下划线
- 示例：`Java学习指南_20240101_143022.wav`

### 错误处理

系统提供完善的错误处理机制：
- 输入验证（文本内容不能为空）
- Azure服务连接错误处理
- 文件系统操作异常处理
- 资源自动释放（使用try-with-resources）

## 注意事项

1. **API密钥安全**: 请确保Azure语音服务密钥的安全，建议使用环境变量
2. **文件存储**: 语音文件会占用磁盘空间，建议定期清理旧文件
3. **网络依赖**: 功能依赖Azure服务，需要稳定的网络连接
4. **文本长度**: Azure语音服务对单次合成的文本长度有限制
5. **费用考虑**: Azure语音服务按使用量计费，请合理控制使用频率

## 故障排除

### 常见问题

1. **语音合成失败**
   - 检查Azure密钥和区域配置是否正确
   - 确认网络连接正常
   - 验证文本内容不为空

2. **文件生成失败**
   - 检查输出目录权限
   - 确认磁盘空间充足
   - 验证文件路径有效性

3. **语音质量问题**
   - 尝试不同的语音选项
   - 检查文本内容的格式和标点
   - 调整语音合成参数

### 日志查看

语音合成过程会产生详细日志，可通过以下方式查看：

```bash
# 查看应用日志
tail -f logs/application.log | grep "语音合成"
```

## 扩展功能

未来可考虑的扩展功能：
- 语音参数调节（语速、音调等）
- 批量文章语音合成
- 语音文件格式选择（MP3、OGG等）
- 语音文件在线播放接口
- 语音合成进度跟踪