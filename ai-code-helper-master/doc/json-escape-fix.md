# JSON转义问题修复

## 问题描述

在启动应用时遇到PostgreSQL JSON语法错误：

```
ERROR: invalid input syntax for type json
详细：Token "id" is invalid.
在位置：JSON data, line 1: ...,"math_difficulty":"基础","original_json":"{"id...
```

## 根本原因

1. **JSON转义问题**: `PostgresVectorStore.formatMetadataAsJson()` 方法没有正确转义JSON字符串中的特殊字符
2. **原始JSON存储**: 在元数据中存储 `original_json` 字段，包含未转义的JSON字符串，导致嵌套JSON格式错误

## 修复方案

### 1. 增强JSON转义功能

在 `PostgresVectorStore.java` 中添加了完整的JSON字符串转义：

```java
/**
 * 转义JSON字符串中的特殊字符
 */
private String escapeJsonString(String str) {
    if (str == null) return "";
    
    return str.replace("\\", "\\\\")  // 反斜杠
              .replace("\"", "\\\"")  // 双引号
              .replace("\b", "\\b")   // 退格
              .replace("\f", "\\f")   // 换页
              .replace("\n", "\\n")   // 换行
              .replace("\r", "\\r")   // 回车
              .replace("\t", "\\t");  // 制表符
}
```

### 2. 改进元数据格式化

更新了 `formatMetadataAsJson()` 方法：

```java
private String formatMetadataAsJson(java.util.Map<String, Object> metadata) {
    if (metadata.isEmpty()) return "{}";
    
    StringBuilder json = new StringBuilder("{");
    boolean first = true;
    for (java.util.Map.Entry<String, Object> entry : metadata.entrySet()) {
        if (!first) json.append(",");
        json.append("\"").append(escapeJsonString(entry.getKey())).append("\":");
        if (entry.getValue() instanceof String) {
            json.append("\"").append(escapeJsonString((String) entry.getValue())).append("\"");
        } else if (entry.getValue() == null) {
            json.append("null");
        } else {
            json.append(entry.getValue());
        }
        first = false;
    }
    json.append("}");
    return json.toString();
}
```

### 3. 移除原始JSON存储

在 `RagConfig.java` 中移除了 `original_json` 字段的存储：

```java
// 移除前
metadataMap.put("original_json", itemNode.toString());

// 修复后
// 不再添加原始JSON数据，避免存储空间浪费和JSON转义问题
```

## 修复效果

### 转义前的问题JSON：
```json
{"file_name":"test.json","original_json":"{"id":"test","content":"This is a "test""}"}
```

### 转义后的正确JSON：
```json
{"file_name":"test.json","math_id":"test","math_type":"concept","math_topic":"Algebra"}
```

## 支持的转义字符

- `\` → `\\` (反斜杠)
- `"` → `\"` (双引号)
- `\b` → `\\b` (退格)
- `\f` → `\\f` (换页)
- `\n` → `\\n` (换行)
- `\r` → `\\r` (回车)
- `\t` → `\\t` (制表符)
- `null` 值正确处理为 `null`

## 测试验证

创建了 `PostgresVectorStoreJsonTest.java` 测试类，验证：

1. ✅ 基本字符串转义
2. ✅ 特殊字符转义（引号、换行符等）
3. ✅ 复杂JSON字符串转义
4. ✅ null值处理
5. ✅ 完整元数据格式化

## 性能优化

移除 `original_json` 字段还带来了额外好处：

- **存储空间减少**: 不再存储重复的原始JSON数据
- **查询性能提升**: 元数据字段更小，索引效率更高
- **内存使用优化**: 减少了不必要的字符串存储

## 向后兼容性

此修复完全向后兼容，不会影响现有数据的读取和查询功能。
