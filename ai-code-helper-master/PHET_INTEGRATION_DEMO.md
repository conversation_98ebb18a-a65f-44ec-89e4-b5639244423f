# PhET工具集成演示文档

## 🎯 项目概述

本项目成功实现了PhET交互式模拟工具与K12数学AI助手的完整集成，形成了从用户请求到模拟展示的完整闭环流程。

## 🏗️ 架构设计

```
用户请求 → AI模型选择 → PhET工具调用 → 前端iframe展示
    ↓           ↓            ↓              ↓
"学习分数"  →  MATH模型   →  硬编码工具   →  左侧面板
```

## 🔧 核心组件

### 1. 后端PhET工具 (`PhETTool.java`)

**位置**: `/ai-code-helper-master/src/main/java/com/hujun/aicodehelper/ai/tools/PhETTool.java`

**功能**:
- 硬编码5个精选K12模拟
- 智能关键词匹配（中英文支持）
- 返回完整iframe HTML代码

**核心方法**:
```java
@Tool("根据关键词查找相关的PhET交互式模拟并提供HTML嵌入代码")
public String findPhetSimulationByKeyword(String keyword)
```

**内置模拟**:
1. **Fraction Matcher** - 分数学习
2. **Circuit Construction Kit** - 电路学习  
3. **Forces and Motion** - 力学学习
4. **Graphing Lines** - 函数图像
5. **Projectile Motion** - 抛体运动

### 2. AI服务集成 (`DynamicAiServiceManager.java`)

**集成位置**:
- MATH模型：数学计算工具 + PhET工具
- PRO模型：全部工具（词典 + 数学 + PhET + RAG）

**关键代码**:
```java
// MATH模型配置
List<Object> mathTools = new ArrayList<>();
mathTools.addAll(MathToolsConfig.getAllMathTools(...));
mathTools.add(phetTool);  // 添加PhET工具
```

### 3. 模型选择优化 (`ModelSelectionStrategy.java`)

**优化内容**:
- 数学请求优先级提升到最高
- 新增学习相关关键词匹配
- 支持 "fraction learning", "学习分数" 等模式

**关键模式**:
```java
"fraction.*learning|learning.*fraction|math.*learning|学习.*分数"
```

### 4. 前端iframe展示 (`DualScreenStudy.vue`)

**新增功能**:
- 左侧面板iframe展示区域
- 自动从AI响应中提取iframe
- 加载状态和交互控制
- 响应式设计支持

**关键方法**:
```javascript
extractAndShowIframe(content) {
  const iframeRegex = /<iframe[^>]*src=["']([^"']+)["'][^>]*>/gi
  // 提取并显示iframe
}
```

## 🔄 完整流程演示

### 步骤1: 用户发起请求
```
用户输入: "我要学习一下分数"
```

### 步骤2: AI模型选择
```
ModelSelectionStrategy 检测到数学学习请求
选择: MATH模型 (包含PhET工具)
```

### 步骤3: PhET工具调用
```
AI调用: findPhetSimulationByKeyword("分数")
匹配到: Fraction Matcher
返回: <iframe src="https://phet.colorado.edu/sims/html/fraction-matcher/...">
```

### 步骤4: 前端展示
```
DualScreenStudy.vue 检测到iframe内容
提取src URL和标题信息
在左侧面板显示交互式模拟
```

## 🧪 测试方法

### 方法1: 直接API测试
```bash
curl -X GET "http://localhost:8081/api/ai/chat?memoryId=123&message=我要学习分数"
```

### 方法2: 前端界面测试
1. 访问 `http://localhost:5173/dual-screen`
2. 输入 "我要学习分数"
3. 观察左侧面板是否显示PhET模拟

### 方法3: 演示页面测试
1. 打开 `/test-iframe-integration.html`
2. 点击测试按钮观察效果

## 📊 测试结果

### ✅ 成功案例

**中文请求**: "我要学习分数"
- ✅ 正确选择MATH模型
- ✅ 成功调用PhET工具
- ✅ 返回Fraction Matcher模拟
- ✅ 前端正确显示iframe

**英文请求**: "fraction learning"
- ✅ 正确识别数学学习请求
- ✅ 优先级高于词典查询
- ✅ 返回相应的PhET模拟

### 🔧 关键修复

1. **模型选择优先级**: 数学请求优先于词典请求
2. **关键词匹配**: 增加学习相关模式
3. **前端提取**: 正则表达式匹配iframe标签
4. **响应式设计**: 移动端适配

## 🚀 部署状态

### 后端服务
- ✅ Spring Boot应用运行在 `localhost:8081`
- ✅ PhET工具已注册到AI服务
- ✅ 模型选择逻辑已优化

### 前端应用
- ✅ Vue3应用运行在 `localhost:5173`
- ✅ DualScreenStudy组件已更新
- ✅ iframe展示功能已实现

## 📝 使用说明

### 开发者
1. 后端启动: `mvn spring-boot:run -Dspring-boot.run.profiles=postgresql`
2. 前端启动: `cd ai-k12-frontend && npm run dev`
3. 访问: `http://localhost:5173/dual-screen`

### 用户
1. 在聊天框输入数学学习请求
2. 等待AI响应和工具调用
3. 在左侧面板查看交互式模拟
4. 直接与PhET模拟进行交互学习

## 🎯 项目价值

1. **教育价值**: 提供高质量的K12数学交互式学习体验
2. **技术价值**: 展示AI工具与教育资源的深度集成
3. **用户价值**: 一站式的智能学习助手解决方案
4. **扩展价值**: 可轻松添加更多PhET模拟和学科内容

## 🔮 未来扩展

1. **更多模拟**: 扩展到物理、化学、生物等学科
2. **动态获取**: 对接PhET官方API获取最新模拟
3. **个性化推荐**: 根据学习历史推荐合适的模拟
4. **学习跟踪**: 记录用户在模拟中的学习进度

---

**项目状态**: ✅ 完全实现并测试通过  
**最后更新**: 2025-09-05  
**开发者**: AI CodeHelper Team
