# BigModel (智谱 GLM) AI 提供者配置
# 使用方式: -Dspring-boot.run.profiles=postgresql,bigmodel
# 注意：ai.catalog 路由和 chat.image 配置已统一在 application.yml，通过环境变量控制

langchain4j:
  bigmodel:
    openai:
      # OpenAI 兼容 API 基础地址
      base-url: ${BIGMODEL_BASE_URL:https://open.bigmodel.cn/api/paas/v4}

      # 多模型配置 - 根据使用场景选择
      models:
        # 轻量级文本模型
        lite:
          api-key: ${BIGMODEL_API_KEY:a08cf598f1d6486f8e19f6783966ebcd.eubFlWtgOU1X8nrz}
          model-name: ${AI_TEXT_MODEL:${BIGMODEL_LITE_MODEL:glm-4.5-airx}}
          description: "轻量级文本模型，用于日常聊天"
          base-url: ${BIGMODEL_BASE_URL:https://open.bigmodel.cn/api/paas/v4}

        # RAG专用模型
        rag:
          api-key: ${BIGMODEL_API_KEY:a08cf598f1d6486f8e19f6783966ebcd.eubFlWtgOU1X8nrz}
          model-name: ${AI_RAG_MODEL:${BIGMODEL_RAG_MODEL:glm-4.5-airx}}
          description: "RAG检索增强生成专用模型"
          base-url: ${BIGMODEL_BASE_URL:https://open.bigmodel.cn/api/paas/v4}

        # 词典工具专用模型
        dictionary:
          api-key: ${BIGMODEL_API_KEY:a08cf598f1d6486f8e19f6783966ebcd.eubFlWtgOU1X8nrz}
          model-name: ${AI_DICTIONARY_MODEL:${BIGMODEL_DICTIONARY_MODEL:glm-4.5-airx}}
          description: "词典查询专用模型"
          base-url: ${BIGMODEL_BASE_URL:https://open.bigmodel.cn/api/paas/v4}

        # 视觉语言模型
        multimodal:
          api-key: ${BIGMODEL_API_KEY:a08cf598f1d6486f8e19f6783966ebcd.eubFlWtgOU1X8nrz}
          model-name: ${AI_MULTIMODAL_MODEL:${BIGMODEL_VL_MODEL:glm-4.5v}}
          description: "视觉语言模型，用于图片识别"
          base-url: ${BIGMODEL_BASE_URL:https://open.bigmodel.cn/api/paas/v4}

        # 专业模型
        pro:
          api-key: ${BIGMODEL_API_KEY:a08cf598f1d6486f8e19f6783966ebcd.eubFlWtgOU1X8nrz}
          model-name: ${AI_PRO_MODEL:${BIGMODEL_PRO_MODEL:glm-4.5-pro}}
          description: "专业模型，用于复杂任务"
          base-url: ${BIGMODEL_BASE_URL:https://open.bigmodel.cn/api/paas/v4}

# 注意：server 端口和上下文路径配置已移动到 application.yml 基础配置中
