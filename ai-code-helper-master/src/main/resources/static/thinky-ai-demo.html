<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ThinkyAI - 引导式学习助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .chat-area {
            display: flex;
            height: 600px;
        }

        .chat-messages {
            flex: 2;
            padding: 30px;
            overflow-y: auto;
            border-right: 1px solid #eee;
        }

        .status-panel {
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 15px;
            max-width: 80%;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.ai {
            background: #f1f3f4;
            color: #333;
        }

        .message.structured {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
        }

        .structured-content {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 1px solid #ddd;
        }

        .step {
            margin-bottom: 15px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 3px solid #007bff;
        }

        .step-question {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 8px;
        }

        .step-thinking {
            color: #666;
            font-style: italic;
            margin-bottom: 5px;
        }

        .input-area {
            padding: 30px;
            border-top: 1px solid #eee;
            background: #f8f9fa;
        }

        .input-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .input-group {
            flex: 1;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .input-group input,
        .input-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }

        .message-input {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        #messageInput {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 25px;
            resize: none;
            min-height: 50px;
            font-size: 16px;
        }

        .send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .status-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .stat-value {
            color: #333;
            font-weight: 500;
        }

        .learning-phase {
            text-align: center;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            padding: 15px 20px;
            background: #f1f3f4;
            border-radius: 15px;
            margin-bottom: 20px;
            max-width: 120px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots div {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots div:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-dots div:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typing {

            0%,
            80%,
            100% {
                transform: scale(0);
            }

            40% {
                transform: scale(1);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🎓 ThinkyAI</h1>
            <p>引导式学习助手 - 不给答案，给方法</p>
        </div>

        <div class="chat-area">
            <div class="chat-messages" id="chatMessages">
                <div class="message ai">
                    <div>👋 你好！我是ThinkyAI，一个专注于引导式学习的AI助手。</div>
                    <div>我不会直接给你答案，而是会通过引导性问题帮助你自己找到解决方案。</div>
                    <div>你有什么数学或物理问题想要探讨吗？</div>
                </div>
            </div>

            <div class="status-panel">
                <div class="status-card">
                    <h3>📊 学习状态</h3>
                    <div class="stat-item">
                        <span class="stat-label">对话轮次</span>
                        <span class="stat-value" id="roundCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">完成题目</span>
                        <span class="stat-value" id="completedProblems">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">学习时长</span>
                        <span class="stat-value" id="studyTime">0 分钟</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">当前主题</span>
                        <span class="stat-value" id="currentTopic">未开始</span>
                    </div>
                </div>

                <div class="status-card">
                    <h3>🌟 学习阶段</h3>
                    <div class="learning-phase" id="learningPhase">准备探索</div>
                </div>

                <div class="status-card">
                    <h3>🎯 会话信息</h3>
                    <div class="stat-item">
                        <span class="stat-label">会话ID</span>
                        <span class="stat-value" id="sessionId">未连接</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">响应类型</span>
                        <span class="stat-value" id="responseType">-</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="input-area">
            <div class="input-row">
                <div class="input-group">
                    <label for="subjectInput">学科</label>
                    <select id="subjectInput">
                        <option value="">请选择学科</option>
                        <option value="数学">数学</option>
                        <option value="物理">物理</option>
                    </select>
                </div>
                <div class="input-group">
                    <label for="problemInput">题目描述（可选）</label>
                    <input type="text" id="problemInput" placeholder="简单描述你要解决的问题">
                </div>
            </div>

            <div class="message-input">
                <textarea id="messageInput" placeholder="在这里输入你的问题或想法..." rows="2"></textarea>
                <button class="send-btn" id="sendBtn">发送</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let sessionId = null;
        let messageCount = 0;

        // DOM 元素
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const subjectInput = document.getElementById('subjectInput');
        const problemInput = document.getElementById('problemInput');

        // 状态元素
        const roundCountEl = document.getElementById('roundCount');
        const completedProblemsEl = document.getElementById('completedProblems');
        const studyTimeEl = document.getElementById('studyTime');
        const currentTopicEl = document.getElementById('currentTopic');
        const learningPhaseEl = document.getElementById('learningPhase');
        const sessionIdEl = document.getElementById('sessionId');
        const responseTypeEl = document.getElementById('responseType');

        // 初始化
        function init() {
            sessionId = generateSessionId();
            sessionIdEl.textContent = sessionId.substring(0, 8) + '...';

            sendBtn.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // 生成会话ID
        function generateSessionId() {
            return 'thinky-' + Math.random().toString(36).substr(2, 9) + '-' + Date.now().toString(36);
        }

        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            const subject = subjectInput.value;
            const problem = problemInput.value.trim();

            // 显示用户消息
            addMessage('user', message);
            messageInput.value = '';
            messageCount++;

            // 显示加载状态
            showTypingIndicator();
            sendBtn.disabled = true;

            try {
                const response = await fetch('/api/ai/thinky/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        message: message,
                        subject: subject || null,
                        problem: problem || null,
                        language: 'zh'
                    })
                });

                const data = await response.json();

                hideTypingIndicator();

                if (data.error) {
                    addMessage('ai', '抱歉，处理您的请求时出现了错误：' + data.error);
                    return;
                }

                // 显示流式内容
                if (data.streamContent) {
                    addMessage('ai', data.streamContent);
                }

                // 显示结构化内容
                if (data.structuredData) {
                    addStructuredMessage(data.structuredData, data.structuredType);
                }

                // 更新学习状态
                if (data.learningStatus) {
                    updateLearningStatus(data.learningStatus);
                }

                // 更新响应类型
                responseTypeEl.textContent = data.type || '未知';

            } catch (error) {
                hideTypingIndicator();
                addMessage('ai', '抱歉，网络连接出现问题，请稍后再试。');
                console.error('Error:', error);
            } finally {
                sendBtn.disabled = false;
            }
        }

        // 添加消息
        function addMessage(type, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = content.replace(/\n/g, '<br>');

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 添加结构化消息
        function addStructuredMessage(data, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message structured';

            let content = `<strong>💡 ${getStructuredTitle(type)}</strong>`;

            if (type === 'SOLUTION' && data.steps) {
                content += '<div class="structured-content">';
                data.steps.forEach((step, index) => {
                    content += `
                        <div class="step">
                            <div class="step-question">步骤 ${step.stepNumber || index + 1}: ${step.guidingQuestion || '思考问题'}</div>
                            ${step.studentThinking ? `<div class="step-thinking">💭 ${step.studentThinking}</div>` : ''}
                            ${step.explanation ? `<div>${step.explanation}</div>` : ''}
                            ${step.keyFormula ? `<div><strong>关键概念：</strong>${step.keyFormula}</div>` : ''}
                        </div>
                    `;
                });
                content += '</div>';
            } else if (type === 'DIAGNOSIS' && data.gaps) {
                content += '<div class="structured-content">';
                content += '<h4>🔍 知识盲区分析：</h4>';
                data.gaps.forEach(gap => {
                    content += `<div class="step">
                        <strong>${gap.concept}</strong><br>
                        ${gap.gapDescription}<br>
                        <em>严重程度：${gap.severityLevel}/5</em>
                    </div>`;
                });
                content += '</div>';
            } else if (type === 'PROGRESS' && data.milestone) {
                content += '<div class="structured-content">';
                content += `
                    <h4>🎉 ${data.milestone}</h4>
                    <p><strong>完成题目：</strong>${data.completedProblems || 0} 道</p>
                    <p><strong>学习建议：</strong>${data.recommendations || '继续保持！'}</p>
                `;
                content += '</div>';
            }

            messageDiv.innerHTML = content;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 获取结构化内容标题
        function getStructuredTitle(type) {
            switch (type) {
                case 'SOLUTION': return '解题步骤指导';
                case 'DIAGNOSIS': return '学习诊断报告';
                case 'PROGRESS': return '学习进度报告';
                default: return '结构化内容';
            }
        }

        // 显示输入指示器
        function showTypingIndicator() {
            const indicator = document.createElement('div');
            indicator.className = 'typing-indicator';
            indicator.id = 'typingIndicator';
            indicator.innerHTML = `
                <div class="typing-dots">
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                <span style="margin-left: 10px; color: #666;">ThinkyAI 正在思考...</span>
            `;
            indicator.style.display = 'flex';
            chatMessages.appendChild(indicator);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 隐藏输入指示器
        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            if (indicator) {
                indicator.remove();
            }
        }

        // 更新学习状态
        function updateLearningStatus(status) {
            if (status.roundCount !== undefined) {
                roundCountEl.textContent = status.roundCount;
            }
            if (status.completedProblems !== undefined) {
                completedProblemsEl.textContent = status.completedProblems;
            }
            if (status.studyTimeMinutes !== undefined) {
                studyTimeEl.textContent = status.studyTimeMinutes + ' 分钟';
            }
            if (status.currentTopic) {
                currentTopicEl.textContent = status.currentTopic;
            }
            if (status.learningPhase) {
                learningPhaseEl.textContent = status.learningPhase;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>

</html>