<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学知识RAG系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            width: 100%;
            max-width: 1400px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            display: flex;
            overflow: hidden;
        }

        /* 左侧知识面板 */
        .knowledge-panel {
            width: 40%;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .panel-header h2 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .panel-header p {
            font-size: 13px;
            opacity: 0.9;
        }

        .knowledge-tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #e9ecef;
        }

        .tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            font-size: 14px;
            color: #6c757d;
            border: none;
            background: none;
            transition: all 0.3s;
        }

        .tab.active {
            color: #667eea;
            border-bottom: 2px solid #667eea;
        }

        .tab:hover {
            background: #f8f9fa;
        }

        .knowledge-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .knowledge-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
            transition: all 0.3s;
        }

        .knowledge-item:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .knowledge-item h3 {
            font-size: 15px;
            color: #333;
            margin-bottom: 10px;
        }

        .knowledge-item .formula {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: "Courier New", monospace;
            margin: 10px 0;
            color: #495057;
        }

        .knowledge-item .example {
            font-size: 13px;
            color: #6c757d;
            line-height: 1.6;
        }

        /* 右侧聊天窗口 */
        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-header h2 {
            font-size: 18px;
            color: #333;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #6c757d;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-dot.warning {
            background: #ffc107;
        }

        .status-dot.error {
            background: #dc3545;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #fafbfc;
        }

        .message {
            display: flex;
            margin-bottom: 20px;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 10px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .message.assistant .message-avatar {
            background: #f1f3f5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .message-content {
            max-width: 70%;
        }

        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.5;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 11px;
            color: #adb5bd;
            margin-top: 5px;
            text-align: right;
        }

        .message.assistant .message-time {
            text-align: left;
        }

        /* 数学内容展示 */
        .math-content {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            font-family: "Courier New", monospace;
        }

        .math-content .question {
            color: #495057;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .math-content .solution {
            color: #28a745;
            margin-top: 10px;
        }

        .math-content .step {
            margin: 5px 0;
            padding-left: 20px;
            color: #6c757d;
        }

        /* 输入区域 */
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            padding: 12px 50px 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            resize: none;
            outline: none;
            transition: all 0.3s;
            font-family: inherit;
            line-height: 1.5;
            max-height: 120px;
        }

        .message-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-actions {
            position: absolute;
            right: 8px;
            bottom: 8px;
            display: flex;
            gap: 5px;
        }

        .input-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #adb5bd;
            transition: all 0.3s;
            border-radius: 50%;
        }

        .input-btn:hover {
            background: #f1f3f5;
            color: #667eea;
        }

        .send-btn {
            padding: 10px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .send-btn:active {
            transform: translateY(0);
        }

        /* 隐藏的文件输入 */
        .file-input {
            display: none;
        }

        /* 录音指示器 */
        .recording-indicator {
            display: none;
            align-items: center;
            padding: 8px 16px;
            background: #dc3545;
            color: white;
            border-radius: 20px;
            font-size: 13px;
            margin-bottom: 10px;
            animation: recording 1.5s infinite;
        }

        @keyframes recording {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.6;
            }

            100% {
                opacity: 1;
            }
        }

        .recording-indicator.active {
            display: flex;
        }

        .recording-dot {
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 1s infinite;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f3f5;
        }

        ::-webkit-scrollbar-thumb {
            background: #adb5bd;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #868e96;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 左侧知识面板 -->
        <div class="knowledge-panel">
            <div class="panel-header">
                <h2>📚 知识库 RAG</h2>
                <p>系统已加载数学知识库</p>
            </div>
            <div class="knowledge-tabs">
                <button class="tab active" onclick="switchTab('formulas', this)">公式定理</button>
                <button class="tab" onclick="switchTab('examples', this)">典型例题</button>
                <button class="tab" onclick="switchTab('methods', this)">解题方法</button>
            </div>
            <div class="knowledge-content" id="knowledge-content">
                <!-- 公式定理内容 -->
                <div id="formulas-content">
                    <div class="knowledge-item">
                        <h3>二次函数标准式</h3>
                        <div class="formula">y = ax² + bx + c</div>
                        <div class="example">
                            其中 a ≠ 0，a 决定开口方向<br>
                            顶点坐标：(-b/2a, (4ac-b²)/4a)
                        </div>
                    </div>
                    <div class="knowledge-item">
                        <h3>代入三点建立方程组</h3>
                        <div class="formula">
                            a + b + c = 2 ... (1)<br>
                            4a + 2b + c = 3 ... (2)<br>
                            9a + 3b + c = 6 ... (3)
                        </div>
                        <div class="example">
                            首先我们把三个点代入，得到三元一次方程组
                        </div>
                    </div>
                    <div class="knowledge-item">
                        <h3>联立求解（消元/矩阵法）</h3>
                        <div class="formula">
                            (2) - (1): 3a + b = 1 ... (4)<br>
                            (3) - (2): 5a + b = 3 ... (5)
                        </div>
                        <div class="example">
                            然后我们用消元法消去 b，得到关于 a 和 c 的两个方程
                        </div>
                    </div>
                    <div class="knowledge-item">
                        <h3>带回验证并给出结论</h3>
                        <div class="formula">
                            代入 A: y = x² - 2x + 3 = 1 - 2 + 3 = 2 ✓<br>
                            代入 B: y = 4 - 4 + 3 = 3 ✓<br>
                            代入 C: y = 9 - 6 + 3 = 6 ✓
                        </div>
                        <div class="example">
                            最后带回原式检验，确保所有点都满足方程
                        </div>
                    </div>
                </div>
                <!-- 其他标签内容（默认隐藏） -->
                <div id="examples-content" style="display: none;">
                    <div class="knowledge-item">
                        <h3>例题1：三点确定抛物线</h3>
                        <div class="example">
                            已知抛物线经过点 A(1,2)、B(2,3)、C(3,6)，求此抛物线的解析式。
                        </div>
                    </div>
                    <div class="knowledge-item">
                        <h3>例题2：顶点式转换</h3>
                        <div class="example">
                            将 y = x² - 2x + 3 转换为顶点式，并求出顶点坐标。
                        </div>
                    </div>
                </div>
                <div id="methods-content" style="display: none;">
                    <div class="knowledge-item">
                        <h3>待定系数法</h3>
                        <div class="example">
                            根据题目条件设定含有待定系数的解析式，然后根据条件列出方程或方程组，求出待定系数。
                        </div>
                    </div>
                    <div class="knowledge-item">
                        <h3>配方法</h3>
                        <div class="example">
                            将二次函数一般式转化为顶点式的重要方法，便于找出函数的最值和对称轴。
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧聊天窗口 -->
        <div class="chat-panel">
            <div class="chat-header">
                <h2>💬 智能数学助手</h2>
                <div class="status-indicator">
                    <span class="status-dot"></span>
                    <span>在线</span>
                </div>
            </div>

            <div class="chat-messages" id="chat-messages">
                <!-- 助手消息 -->
                <div class="message assistant">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div class="message-bubble">
                            你好！我是智能数学助手，我可以帮你解决各种数学问题。现在让我们一起来解决这道题吧！
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>

                <!-- 用户消息 -->
                <div class="message user">
                    <div class="message-avatar">用户</div>
                    <div class="message-content">
                        <div class="message-bubble">
                            已知抛物线 y = ax²+bx+c 过点 (1,2)、(2,3)、(3,6)，求 a、b、c。
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>

                <!-- 助手回复 -->
                <div class="message assistant">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div class="message-bubble">
                            好的，我来帮你解决这道题。我们需要用待定系数法求解。
                        </div>
                        <div class="math-content">
                            <div class="question">Q：已知抛物线 y = ax²+bx+c 过点 (1,2)、(2,3)、(3,6)，求 a、b、c。</div>
                            <div class="solution">
                                解答步骤：<br>
                                <div class="step">1) 代入三点建立方程组</div>
                                <div class="step">
                                    a + b + c = 2 ... (1)<br>
                                    4a + 2b + c = 3 ... (2)<br>
                                    9a + 3b + c = 6 ... (3)
                                </div>
                                <div class="step">2) 联立求解（消元/矩阵法）</div>
                                <div class="step">
                                    (2) - (1): 3a + b = 1 ... (4)<br>
                                    (3) - (2): 5a + b = 3 ... (5)<br>
                                    (5) - (4): 2a = 2，所以 a = 1<br>
                                    代入 (4): b = 1 - 3 = -2<br>
                                    代入 (1): 1 - 2 + c = 2，所以 c = 3
                                </div>
                                <div class="step">3) 带回验证并给出结论</div>
                                <div class="step">
                                    代入 A: y = 1 - 2 + 3 = 2 ✓<br>
                                    代入 B: y = 4 - 4 + 3 = 3 ✓<br>
                                    代入 C: y = 9 - 6 + 3 = 6 ✓
                                </div>
                                <div class="step" style="color: #28a745; font-weight: 600;">
                                    答案：a = 1, b = -2, c = 3
                                </div>
                            </div>
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>
            </div>

            <div class="chat-input">
                <div class="recording-indicator" id="recording-indicator">
                    <span class="recording-dot"></span>
                    <span>正在录音...</span>
                </div>
                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea class="message-input" id="message-input" placeholder="输入数学问题..." rows="1"></textarea>
                        <div class="input-actions">
                            <button class="input-btn" onclick="toggleVoiceRecording()" title="语音输入">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z" />
                                    <path
                                        d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" />
                                </svg>
                            </button>
                            <button class="input-btn" onclick="document.getElementById('file-input').click()"
                                title="上传图片">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <button class="send-btn" onclick="sendMessage()">发送</button>
                </div>
                <input type="file" id="file-input" class="file-input" accept="image/*"
                    onchange="handleFileSelect(event)">
            </div>
        </div>
    </div>

    <script>
        // 自动调整输入框高度
        const messageInput = document.getElementById('message-input');
        messageInput.addEventListener('input', function () {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // 更新状态指示器
        function updateStatusIndicator(status) {
            const statusDot = document.querySelector('.status-dot');
            const statusText = document.querySelector('.status-indicator span:last-child');

            statusDot.classList.remove('warning', 'error');

            switch (status) {
                case 'online':
                    statusDot.style.background = '#28a745';
                    statusText.textContent = '在线';
                    break;
                case 'thinking':
                    statusDot.style.background = '#ffc107';
                    statusDot.classList.add('warning');
                    statusText.textContent = '思考中';
                    break;
                case 'error':
                    statusDot.style.background = '#dc3545';
                    statusDot.classList.add('error');
                    statusText.textContent = '错误';
                    break;
            }
        }

        // 切换知识库标签
        function switchTab(tabName, element) {
            // 移除所有active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            element.classList.add('active');

            // 隐藏所有内容
            document.getElementById('formulas-content').style.display = 'none';
            document.getElementById('examples-content').style.display = 'none';
            document.getElementById('methods-content').style.display = 'none';

            // 显示选中的内容
            document.getElementById(tabName + '-content').style.display = 'block';
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();

            if (message) {
                addMessage(message, 'user');
                input.value = '';
                input.style.height = 'auto';

                // 显示思考中状态并更新状态指示器
                addMessage('🤔 正在检索知识库并思考中...', 'assistant');
                updateStatusIndicator('thinking');

                try {
                    // 调用RAG流式聊天API
                    const params = new URLSearchParams({
                        memoryId: Date.now().toString(),
                        message: message,
                        grade: 'Grade 6',
                        subject: 'Math'
                    });

                    const response = await fetch(`/ai/rag/chat-stream?${params}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    // 处理SSE响应
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let accumulatedContent = '';
                    let lastMessageDiv = null;

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.substring(6).trim();
                                if (data && data !== '[DONE]') {
                                    accumulatedContent += data;

                                    // 更新最后一条消息
                                    if (!lastMessageDiv) {
                                        // 移除"正在思考中..."消息
                                        const messages = document.querySelectorAll('.message.assistant');
                                        const lastAssistantMessage = messages[messages.length - 1];
                                        if (lastAssistantMessage) {
                                            lastAssistantMessage.remove();
                                        }

                                        // 添加新消息
                                        addMessage('', 'assistant');
                                        lastMessageDiv = document.querySelector('.message.assistant:last-child .message-bubble');
                                    }

                                    if (lastMessageDiv) {
                                        lastMessageDiv.textContent = accumulatedContent;
                                        document.getElementById('chat-messages').scrollTop = document.getElementById('chat-messages').scrollHeight;
                                    }
                                }
                            }
                        }
                    }

                } catch (error) {
                    console.error('RAG聊天请求失败:', error);
                    updateStatusIndicator('error');

                    // 移除"正在思考中..."消息
                    const messages = document.querySelectorAll('.message.assistant');
                    const lastAssistantMessage = messages[messages.length - 1];
                    if (lastAssistantMessage) {
                        lastAssistantMessage.remove();
                    }

                    if (error.message.includes('404')) {
                        addMessage('⚠️ 知识库服务暂时不可用，使用普通聊天模式为您解答。', 'assistant');
                    } else {
                        addMessage('❌ 聊天服务暂时不可用，请稍后再试。', 'assistant');
                    }
                } finally {
                    // 恢复正常状态
                    updateStatusIndicator('online');
                }
            }
        }

        // 添加消息到聊天窗口
        function addMessage(text, sender) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const avatarDiv = document.createElement('div');
            avatarDiv.className = 'message-avatar';
            avatarDiv.textContent = sender === 'user' ? '用户' : '🤖';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';
            bubbleDiv.textContent = text;

            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = '刚刚';

            contentDiv.appendChild(bubbleDiv);
            contentDiv.appendChild(timeDiv);
            messageDiv.appendChild(avatarDiv);
            messageDiv.appendChild(contentDiv);

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 语音录音切换
        let isRecording = false;
        function toggleVoiceRecording() {
            const indicator = document.getElementById('recording-indicator');
            isRecording = !isRecording;

            if (isRecording) {
                indicator.classList.add('active');
                // 这里可以添加实际的录音逻辑
                console.log('开始录音...');
            } else {
                indicator.classList.remove('active');
                console.log('停止录音');
                // 模拟语音识别结果
                setTimeout(() => {
                    document.getElementById('message-input').value = "请帮我解这道二次函数题";
                }, 500);
            }
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                console.log('选择了文件:', file.name);
                // 这里可以添加图片预览和上传逻辑
                addMessage(`[已上传图片: ${file.name}]`, 'user');
            }
        }

        // 回车发送消息
        messageInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>

</html>