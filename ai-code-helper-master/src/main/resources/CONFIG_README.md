# 配置文件说明

## 文件结构

- `application.yml` - 基础通用配置（服务器、日志、RAG、语音等）
- `application-postgresql.yml` - PostgreSQL数据库配置
- `application-gemini.yml` - Gemini AI提供者配置（不再覆盖全局路由，仅提供 Gemini 内部槽位与模型配置）  
- `application-modelscope.yml` - ModelScope(Qwen)提供者配置（不再覆盖全局路由）
- `application-bigmodel.yml` - BigModel(GLM)提供者配置（不再覆盖全局路由与 chat.image）

## 使用方式

### 1. 使用Gemini（默认）
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=postgresql,gemini
```

### 2. 使用ModelScope
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=postgresql,modelscope
```

### 3. 使用BigModel
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=postgresql,bigmodel
```

## 环境变量配置

### 数据库配置
- `POSTGRES_URL` - PostgreSQL数据库URL
- `POSTGRES_USERNAME` - 数据库用户名
- `POSTGRES_PASSWORD` - 数据库密码

### AI提供者配置
- `GEMINI_API_KEY` - Gemini API密钥
- `MODELSCOPE_API_KEY` - ModelScope API密钥  
- `BIGMODEL_API_KEY` - BigModel API密钥

### 服务器配置
- `SERVER_PORT` - 服务器端口（默认8081）
- `SERVER_CONTEXT_PATH` - 上下文路径（默认/api）

## 配置优化说明

1. **消除重复配置** - server、logging等基础配置统一到application.yml
2. **环境变量支持** - 所有敏感信息和可变配置支持环境变量
3. **模块化设计** - 按功能和提供者分离配置文件
4. **路由与槽位集中**
   - `ai.catalog.*`：用例 → 提供者 路由，统一在 `application.yml`；可用 `AI_CATALOG_TEXT/MULTIMODAL/RAG/DICTIONARY/PRO` 控制。
   - `ai.catalog.slots.*`：用例 → 模型类型 槽位映射（LITE/RAG/DICTIONARY/PRO/MATH），统一在 `application.yml`；可用 `AI_SLOT_TEXT/MULTIMODAL/RAG/DICTIONARY/PRO` 控制。
   - 各 provider 的 `application-*.yml` 不再定义这些键，仅保留各自 API/BaseURL/模型默认值。
5. **图片配置集中** - `ai.chat.image.*` 统一在 `application.yml`，通过 `AI_CHAT_IMAGE_*` 控制（如 `AI_CHAT_IMAGE_PUBLIC_BASE_URL`）。
