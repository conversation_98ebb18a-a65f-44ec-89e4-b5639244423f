-- 示例数据插入脚本
-- 基于 math-schema.json 和实际数据文件的结构

-- 插入比例概念数据（来自 final-test.json）
SELECT add_knowledge(
    'ratio_p6_concept_1',                     -- id
    'concept',                                -- type
    'A ratio tells us the relationship between two or more quantities. It may not represent the actual number.', -- content
    'Ratio',                                  -- topic
    'P6',                                     -- grade
    '[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]'::vector(16), -- 示例embedding (实际应该是1536维)
    NULL,                                     -- answer
    NULL,                                     -- latex
    NULL,                                     -- sympy_expr
    NULL,                                     -- graph
    '基础',                                   -- difficulty
    'geniebook',                              -- source
    '[
        {
            "caption": "Equivalent Ratio",
            "description": "Equivalent ratios are shown in a table. The ratios 24:16, 12:8, and 3:2 are equivalent ratios. The ratio 3:2 is in its simplest form.",
            "local_path": null,
            "type": "diagram",
            "url": "https://geniebook.com/cms/storage/app/media/primary-math/ratio-img1.webp"
        },
        {
            "caption": "ratio of the number of cups to the number of saucers",
            "description": "A picture showing 3 cups and 7 saucers.",
            "local_path": null,
            "type": "diagram",
            "url": "https://geniebook.com/cms/storage/app/media/primary-math/ratio-img2.webp"
        }
    ]'::jsonb,                                -- images (部分示例)
    '[]'::jsonb,                              -- steps (空数组)
    '{
        "chapter": "Ratio",
        "page": null,
        "tags": ["ratio", "relationship", "quantities"],
        "related_images": []
    }'::jsonb                                 -- metadata
);

-- 插入立方体体积概念数据（来自 volume-cube-detailed-guide.json）
SELECT add_knowledge(
    'volume_p6_concept_1',                    -- id
    'concept',                                -- type
    'The volume of a cube is a fundamental concept in geometry. It is one of the easier three-dimensional shapes to calculate since its length, width, and height are all equal.', -- content
    'Geometry',                               -- topic
    'P6',                                     -- grade
    '[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]'::vector(16), -- 示例embedding (实际应该是1536维)
    NULL,                                     -- answer
    NULL,                                     -- latex
    NULL,                                     -- sympy_expr
    NULL,                                     -- graph
    '基础',                                   -- difficulty
    'geniebook',                              -- source
    '[
        {
            "caption": "Volume of a cube: A detailed guide",
            "description": "A diagram showing a cube with its sides labeled \"a\".",
            "local_path": null,
            "type": "diagram",
            "url": "https://geniebook.com/cms/storage/app/media/pri.-maths-blog/wepik-export-20230822025642qvag.webp"
        }
    ]'::jsonb,                                -- images
    '[]'::jsonb,                              -- steps (空数组)
    '{
        "chapter": "Volume",
        "page": null,
        "tags": ["volume", "cube", "geometry"],
        "related_images": []
    }'::jsonb                                 -- metadata
);

-- 验证插入的数据
SELECT 
    id, 
    type, 
    LEFT(content, 50) || '...' as content_preview,
    topic,
    grade,
    difficulty,
    source,
    jsonb_array_length(images) as image_count,
    jsonb_array_length(steps) as step_count,
    metadata->'tags' as tags
FROM math_rag_knowledge 
ORDER BY created_at DESC;
