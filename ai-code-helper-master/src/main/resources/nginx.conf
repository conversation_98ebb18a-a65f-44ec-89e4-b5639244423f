# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;

    # Map for WebSocket upgrade header (avoids unknown $connection_upgrade)
    map $http_upgrade $connection_upgrade {
        default upgrade;
        ''      close;
    }

    server {
      listen 80;
      listen [::]:80;

      server_name thinkyai.net www.thinkyai.net;

       # Certbot 已经将重定向规则放在 443 块中了，
       # 但保留这个 80 端口块用于未来的证书续订和明确的跳转
      location /.well-known/acme-challenge/ {
        root /var/www/thinkyai;
       }

      location / {
        return 301 https://$host$request_uri;
       }
     }
    
     # 统一处理所有 HTTPS 请求
     server {
         listen 443 ssl;
             listen [::]:443 ssl;
         # Enable HTTP/2 explicitly (newer nginx removes http2 flag from listen)
         http2 on;

                 server_name thinkyai.net www.thinkyai.net;

                     # 网站根目录
        root /var/www/thinkyai;
        index index.html;
        # --- SSL 证书配置 (由 Certbot 自动添加和管理) ---
        ssl_certificate /etc/letsencrypt/live/www.thinkyai.net/fullchain.pem; # managed by Certbot
        ssl_certificate_key /etc/letsencrypt/live/www.thinkyai.net/privkey.pem; # managed by Certbot
        include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
        ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
                                                         
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                access_log off;
         }

        # 直接由 Nginx 映射图片静态目录（绕过后端，提高可用性与带宽）
        # /api/mobile-upload/file/YYYY/MM/DD/filename -> /home/<USER>/data/uploads/YYYY/MM/DD/filename
        location ^~ /api/mobile-upload/file/ {
                alias /home/<USER>/data/uploads/;
                # 长缓存，文件名带 UUID，基本不可变
                expires 1y;
                add_header Cache-Control "public, immutable";
                access_log off;
        }

        # API 代理（转发到 Spring Boot，已在 application.yml 配置 context-path=/api）
        # 使用“无路径”的 proxy_pass，原样转发 /api/xxx 到 8081 的 /api/xxx
        location /api/ {
                proxy_pass http://127.0.0.1:8081;
                proxy_http_version 1.1;

                # 透传头信息
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # SSE / 流式
                proxy_buffering off;
                proxy_cache off;
                add_header X-Accel-Buffering no;
                proxy_read_timeout 600s;

                # WebSocket（可保留，SSE 不必须）
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection $connection_upgrade;
        }
        # 前端单页应用路由
        location / {
                try_files $uri $uri/ /index.html;
        }

        # 合理的上传大小限制（后端限制为 10MB，这里放宽）
        client_max_body_size 20m;
        }
}
