# PostgreSQL 数据库配置
spring:
  # 数据源配置
  datasource:
    postgresql:
      url: ${POSTGRES_URL:**********************************************}
      username: ${POSTGRES_USERNAME:myappuser}
      password: ${POSTGRES_PASSWORD:mypassword}
      driver-class-name: org.postgresql.Driver
      hikari:
        pool-name: PostgreSQLPool
        maximum-pool-size: ${POSTGRES_POOL_MAX_SIZE:15}
        minimum-idle: ${POSTGRES_POOL_MIN_IDLE:3}
        connection-timeout: ${POSTGRES_CONNECTION_TIMEOUT:30000}
        idle-timeout: ${POSTGRES_IDLE_TIMEOUT:600000}
        max-lifetime: ${POSTGRES_MAX_LIFETIME:1800000}

  # JPA 配置
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: ${HIBERNATE_DDL_AUTO:update}
    show-sql: ${HIBERNATE_SHOW_SQL:false}
    properties:
      hibernate:
        format_sql: ${HIBERNATE_FORMAT_SQL:true}
        dialect: org.hibernate.dialect.PostgreSQLDialect

# 向量数据库配置
embedding:
  # 数据库类型: postgresql (使用pgvector)
  store-type: postgresql
  # 模型类型配置
  model-type: ${EMBEDDING_MODEL_TYPE:gemini}
  
  # 本地模型配置 (BAAI/bge-m3)
  local:
    base-url: ${EMBEDDING_LOCAL_BASE_URL:http://************:9000}
    model-name: ${EMBEDDING_LOCAL_MODEL_NAME:BAAI/bge-base-en-v1.5}
    dimension: ${EMBEDDING_LOCAL_DIMENSION:768}
  
  # Ollama模型配置
  ollama:
    base-url: ${EMBEDDING_OLLAMA_BASE_URL:http://localhost:11434}
    model-name: ${EMBEDDING_OLLAMA_MODEL_NAME:dengcao/Qwen3-Embedding-0.6B:Q8_0}
    dimension: ${EMBEDDING_OLLAMA_DIMENSION:1024}
  
  # Gemini嵌入模型配置
  gemini:
    api-key: ${GEMINI_API_KEY:AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8}
    model-name: ${EMBEDDING_GEMINI_MODEL_NAME:gemini-embedding-001}
    dimension: ${EMBEDDING_GEMINI_DIMENSION:1536}
  
  # ModelScope(Qwen) 嵌入模型配置
  modelscope:
    api-key: ${MODELSCOPE_API_KEY:4b358b91-dd36-42c2-8017-8acee9afa005}
    model-name: ${EMBEDDING_MODELSCOPE_MODEL_NAME:Qwen/Qwen3-Embedding-8B}
    base-url: ${MODELSCOPE_BASE_URL:https://api-inference.modelscope.cn/v1}
    dimension: ${EMBEDDING_MODELSCOPE_DIMENSION:1536}