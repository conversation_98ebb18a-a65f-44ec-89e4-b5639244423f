# Gemini AI 提供者配置
# 使用方式: -Dspring-boot.run.profiles=postgresql,gemini
# 注意：ai.catalog（用例路由）已统一在 application.yml，通过环境变量控制

# Gemini 内部模型槽位映射已统一迁移到全局 application.yml 的 `ai.catalog.slots.*`

# LangChain4j 配置
langchain4j:
  google:
    ai:
      gemini:
        # 默认聊天模型 (保持兼容性)
        chat-model:
          api-key: ${GEMINI_API_KEY:AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8}
          model-name: ${GEMINI_CHAT_MODEL:gemini-2.5-flash-lite-preview-06-17}
        
        # 多模型配置 - 根据使用场景选择
        models:
          # 轻量级聊天模型 (平时聊天)
          lite:
            api-key: ${GEMINI_API_KEY:AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8}
            model-name: ${AI_TEXT_MODEL:${GEMINI_LITE_MODEL:gemini-2.5-flash-lite}}
            description: "轻量级模型，用于日常聊天"
            temperature: ${GEMINI_LITE_TEMPERATURE:0.7}
            max-tokens: ${GEMINI_LITE_MAX_TOKENS:4096}
          
          # RAG专用模型
          rag:
            api-key: ${GEMINI_API_KEY:AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8}
            model-name: ${AI_RAG_MODEL:${GEMINI_RAG_MODEL:gemini-2.5-flash-lite}}
            description: "RAG检索增强生成专用模型"
            temperature: ${GEMINI_RAG_TEMPERATURE:0.3}
            max-tokens: ${GEMINI_RAG_MAX_TOKENS:8192}
          
          # 词典工具专用模型
          dictionary:
            api-key: ${GEMINI_API_KEY:AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8}
            model-name: ${AI_DICTIONARY_MODEL:${GEMINI_DICTIONARY_MODEL:gemini-2.5-flash}}
            description: "剑桥词典查询专用模型"
            temperature: ${GEMINI_DICTIONARY_TEMPERATURE:0.1}
            max-tokens: ${GEMINI_DICTIONARY_MAX_TOKENS:2048}
          
          # 专业模型 (高级需求)
          pro:
            api-key: ${GEMINI_API_KEY:AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8}
            model-name: ${AI_PRO_MODEL:${GEMINI_PRO_MODEL:gemini-2.5-pro}}
            description: "专业模型，用于复杂任务和高级需求"
            temperature: ${GEMINI_PRO_TEMPERATURE:0.2}
            max-tokens: ${GEMINI_PRO_MAX_TOKENS:16384}
  
  # Ollama本地模型配置（如需要）
  ollama:
    chat-model:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      model-name: ${OLLAMA_MODEL_NAME:gemma3n:e4b}
      temperature: ${OLLAMA_TEMPERATURE:0.7}
      max-tokens: ${OLLAMA_MAX_TOKENS:16384}
