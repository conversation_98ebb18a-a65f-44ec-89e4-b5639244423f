# ModelScope (Qwen) AI 提供者配置
# 使用方式: -Dspring-boot.run.profiles=postgresql,modelscope
# 注意：ai.catalog 路由已统一在 application.yml，通过环境变量控制

langchain4j:
  modelscope:
    openai:
      # OpenAI 兼容 API 的基础地址（ModelScope）
      base-url: ${MODELSCOPE_BASE_URL:https://api-inference.modelscope.cn/v1}

      # 多模型配置 - 根据使用场景选择
      models:
        # 轻量级文本模型
        lite:
          api-key: ${MODELSCOPE_API_KEY:4b358b91-dd36-42c2-8017-8acee9afa005}
          model-name: ${AI_TEXT_MODEL:${MODELSCOPE_LITE_MODEL:Qwen/Qwen3-235B-A22B-Thinking-2507}}
          description: "轻量级文本模型，用于日常聊天"
          base-url: ${MODELSCOPE_BASE_URL:https://api-inference.modelscope.cn/v1}

        # RAG专用模型
        rag:
          api-key: ${MODELSCOPE_API_KEY:4b358b91-dd36-42c2-8017-8acee9afa005}
          model-name: ${AI_RAG_MODEL:${MODELSCOPE_RAG_MODEL:Qwen/Qwen3-235B-A22B-Thinking-2507}}
          description: "RAG检索增强生成专用模型"
          base-url: ${MODELSCOPE_BASE_URL:https://api-inference.modelscope.cn/v1}

        # 词典工具专用模型
        dictionary:
          api-key: ${MODELSCOPE_API_KEY:4b358b91-dd36-42c2-8017-8acee9afa005}
          model-name: ${AI_DICTIONARY_MODEL:${MODELSCOPE_DICTIONARY_MODEL:Qwen/Qwen3-235B-A22B-Thinking-2507}}
          description: "词典查询专用模型"
          base-url: ${MODELSCOPE_BASE_URL:https://api-inference.modelscope.cn/v1}

        # 视觉语言模型
        multimodal:
          api-key: ${MODELSCOPE_API_KEY:4b358b91-dd36-42c2-8017-8acee9afa005}
          model-name: ${AI_MULTIMODAL_MODEL:${MODELSCOPE_VL_MODEL:Qwen/Qwen2.5-VL-72B-Instruct}}
          description: "视觉语言模型，用于图片识别"
          base-url: ${MODELSCOPE_BASE_URL:https://api-inference.modelscope.cn/v1}

        # 专业模型
        pro:
          api-key: ${MODELSCOPE_API_KEY:4b358b91-dd36-42c2-8017-8acee9afa005}
          model-name: ${AI_PRO_MODEL:${MODELSCOPE_PRO_MODEL:Qwen/Qwen3-235B-A22B-Thinking-2507}}
          description: "专业模型，用于复杂任务"
          base-url: ${MODELSCOPE_BASE_URL:https://api-inference.modelscope.cn/v1}

        # 嵌入模型
        embedding:
          api-key: ${MODELSCOPE_API_KEY:4b358b91-dd36-42c2-8017-8acee9afa005}
          model-name: ${AI_EMBEDDING_MODEL:${MODELSCOPE_EMBEDDING_MODEL:Qwen/text-embedding-3-large}}
          description: "嵌入模型，用于向量化"
          base-url: ${MODELSCOPE_BASE_URL:https://api-inference.modelscope.cn/v1}

# 注意：server 端口和上下文路径配置已移动到 application.yml 基础配置中
