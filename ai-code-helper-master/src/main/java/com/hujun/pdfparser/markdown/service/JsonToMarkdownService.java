package com.hujun.pdfparser.markdown.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 使用Ollama模型将JSON文件转换为结构化markdown的服务
 */
@Slf4j
@Service("jsonToMarkdownService")
public class JsonToMarkdownService {

    @Value("${langchain4j.ollama.chat-model.base-url}")
    private String ollamaBaseUrl;

    @Value("${langchain4j.ollama.chat-model.model-name}")
    private String modelName;

    @Value("${langchain4j.ollama.chat-model.temperature}")
    private Double temperature;

    @Value("${langchain4j.ollama.chat-model.max-tokens}")
    private Integer maxTokens;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 检查Ollama服务是否可用
     */
    private void checkOllamaService() {
        try {
            String healthUrl = ollamaBaseUrl + "/api/tags";
            ResponseEntity<String> response = restTemplate.getForEntity(healthUrl, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Ollama服务连接正常: {}", ollamaBaseUrl);
            }
        } catch (Exception e) {
            log.warn("Ollama服务连接检查失败: {}", e.getMessage());
            throw new RuntimeException("无法连接到Ollama服务，请确保服务已启动: " + ollamaBaseUrl);
        }
    }

    /**
     * 处理指定目录下的所有JSON文件，生成markdown文件
     */
    public void processAllJsonFiles(String inputDir, String outputDir) {
        try {
            checkOllamaService();
            
            Path inputPath = Paths.get(inputDir);
            Path outputPath = Paths.get(outputDir);
            
            // 确保输出目录存在
            if (!Files.exists(outputPath)) {
                Files.createDirectories(outputPath);
            }

            // 获取所有JSON文件，包括子目录
            List<Path> jsonFiles = new ArrayList<>();
            Files.walk(inputPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().toLowerCase().endsWith(".json"))
                    .forEach(jsonFiles::add);

            log.info("找到 {} 个JSON文件需要处理", jsonFiles.size());

            // 按目录分组处理
            Map<String, List<Path>> filesByDirectory = groupFilesByDirectory(jsonFiles, inputPath);
            
            for (Map.Entry<String, List<Path>> entry : filesByDirectory.entrySet()) {
                String directoryName = entry.getKey();
                List<Path> directoryFiles = entry.getValue();
                
                log.info("开始处理目录: {} ({} 个文件)", directoryName, directoryFiles.size());
                processDirectoryFiles(directoryName, directoryFiles, outputPath);
            }

            log.info("所有JSON文件处理完成！");

        } catch (IOException e) {
            log.error("处理文件时发生错误", e);
            throw new RuntimeException("文件处理失败", e);
        }
    }

    /**
     * 按目录分组文件
     */
    private Map<String, List<Path>> groupFilesByDirectory(List<Path> jsonFiles, Path basePath) {
        Map<String, List<Path>> filesByDirectory = new HashMap<>();
        
        for (Path jsonFile : jsonFiles) {
            Path relativePath = basePath.relativize(jsonFile);
            String directoryName = relativePath.getParent() != null ? 
                    relativePath.getParent().toString() : "root";
            
            filesByDirectory.computeIfAbsent(directoryName, k -> new ArrayList<>()).add(jsonFile);
        }
        
        return filesByDirectory;
    }

    /**
     * 处理单个目录下的所有JSON文件
     */
    private void processDirectoryFiles(String directoryName, List<Path> jsonFiles, Path outputDir) {
        try {
            StringBuilder markdownBuilder = new StringBuilder();
            
            // 添加目录文档头部
            markdownBuilder.append(generateDirectoryHeader(directoryName));
            
            // 处理目录中的每个JSON文件
            for (Path jsonFile : jsonFiles) {
                log.info("处理文件: {}", jsonFile.getFileName());
                String jsonContent = processSingleJsonFile(jsonFile);
                markdownBuilder.append("\n\n").append(jsonContent);
            }
            
            // 保存整个目录的markdown文件
            String outputFileName = directoryName.replaceAll("[/\\\\]", "_") + "_combined.md";
            Path outputFile = outputDir.resolve(outputFileName);
            Files.writeString(outputFile, markdownBuilder.toString());
            
            log.info("目录 {} 处理完成，输出文件: {}", directoryName, outputFileName);
            
        } catch (IOException e) {
            log.error("处理目录 {} 时发生错误", directoryName, e);
        }
    }

    /**
     * 处理单个JSON文件
     */
    private String processSingleJsonFile(Path jsonFile) {
        try {
            // 读取JSON文件内容
            String jsonContent = Files.readString(jsonFile);
            
            // 解析JSON获取结构化数据
            JsonNode jsonNode = objectMapper.readTree(jsonContent);
            
            // 生成结构化的JSON内容描述
            String structuredContent = generateStructuredContent(jsonNode, jsonFile.getFileName().toString());
            
            // 使用AI模型优化内容
            String optimizedContent = processWithAI(structuredContent, jsonFile.getFileName().toString());
            
            return optimizedContent;
            
        } catch (IOException e) {
            log.error("处理JSON文件 {} 时发生错误", jsonFile.getFileName(), e);
            return "## Processing Error\n\nFile: " + jsonFile.getFileName() + "\nError: " + e.getMessage();
        }
    }

    /**
     * 从JSON节点生成结构化内容
     */
    private String generateStructuredContent(JsonNode jsonNode, String fileName) {
        StringBuilder content = new StringBuilder();
        
        content.append("## ").append(fileName.replace(".json", "")).append("\n\n");
        
        // 提取基本信息
        if (jsonNode.has("unitInfo")) {
            content.append("**Unit Information:** ").append(jsonNode.get("unitInfo").asText()).append("\n\n");
        }
        
        if (jsonNode.has("activityTitle")) {
            content.append("**Activity Title:** ").append(jsonNode.get("activityTitle").asText()).append("\n\n");
        }
        
        if (jsonNode.has("instructions")) {
            content.append("**Instructions:** ").append(jsonNode.get("instructions").asText()).append("\n\n");
        }
        
        // 处理视频信息
        if (jsonNode.has("videoInfo")) {
            JsonNode videoInfo = jsonNode.get("videoInfo");
            content.append("### Video Information\n\n");
            
            if (videoInfo.has("videoUrl")) {
                content.append("**Video URL:** ").append(videoInfo.get("videoUrl").asText()).append("\n\n");
            }
            
            if (videoInfo.has("transcript")) {
                content.append("### Video Transcript\n\n");
                JsonNode transcript = videoInfo.get("transcript");
                if (transcript.isArray()) {
                    for (JsonNode transcriptItem : transcript) {
                        if (transcriptItem.has("beginTimestamp") && transcriptItem.has("text")) {
                            content.append("- **").append(transcriptItem.get("beginTimestamp").asText())
                                   .append("s:** ").append(transcriptItem.get("text").asText()).append("\n");
                        }
                    }
                    content.append("\n");
                }
            }
        }
        
        // 处理阅读文章
        if (jsonNode.has("readingPassage") && !jsonNode.get("readingPassage").isNull()) {
            content.append("### Reading Passage\n\n");
            content.append(jsonNode.get("readingPassage").asText()).append("\n\n");
        }
        
        // 处理问题
        if (jsonNode.has("questions")) {
            JsonNode questions = jsonNode.get("questions");
            if (questions.isArray() && questions.size() > 0) {
                content.append("### Questions\n\n");
                int questionNumber = 1;
                for (JsonNode question : questions) {
                    content.append(questionNumber++).append(". ");
                    if (question.has("questionText")) {
                        content.append(question.get("questionText").asText()).append("\n");
                    }
                    
                    if (question.has("options")) {
                        JsonNode options = question.get("options");
                        if (options.isArray()) {
                            for (JsonNode option : options) {
                                content.append("   - ").append(option.asText()).append("\n");
                            }
                        }
                    }
                    content.append("\n");
                }
            }
        }
        
        // 处理其他字段
        content.append("### Raw JSON Data\n\n");
        content.append("```json\n");
        try {
            content.append(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonNode));
        } catch (Exception e) {
            content.append("Error formatting JSON: ").append(e.getMessage());
        }
        content.append("\n```\n\n");
        
        return content.toString();
    }

    /**
     * 使用AI模型优化内容
     */
    private String processWithAI(String content, String fileName) {
        String prompt = buildPrompt(content, fileName);
        
        try {
            String response = callOllamaAPI(prompt);
            return cleanMarkdownResponse(response);
        } catch (Exception e) {
            log.error("AI处理文件 {} 时发生错误", fileName, e);
            return content; // 如果AI处理失败，返回原始结构化内容
        }
    }

    /**
     * 调用Ollama API
     */
    private String callOllamaAPI(String prompt) {
        try {
            String url = ollamaBaseUrl + "/api/generate";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", modelName);
            requestBody.put("prompt", prompt);
            requestBody.put("stream", false);
            requestBody.put("options", Map.of(
                "temperature", temperature,
                "num_predict", maxTokens
            ));
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.POST, request, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode jsonResponse = objectMapper.readTree(response.getBody());
                return jsonResponse.get("response").asText();
            } else {
                throw new RuntimeException("Ollama API调用失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("调用Ollama API时发生错误", e);
            throw new RuntimeException("Ollama API调用失败: " + e.getMessage());
        }
    }

    /**
     * 构建AI处理的提示词
     */
    private String buildPrompt(String content, String fileName) {
        return String.format("""
            Please optimize and enhance the following educational content extracted from a JSON file into well-structured Markdown format. Requirements:
            
            1. Organize the content into logical sections with appropriate headings
            2. Improve readability and educational value
            3. Format educational elements clearly:
               - Instructions and guidelines
               - Video transcripts with timestamps
               - Questions and answers
               - Reading passages
            4. Use appropriate Markdown formatting:
               - **Bold** for important terms and labels
               - Code blocks for technical content
               - Lists for questions and options
               - Tables when appropriate
            5. Maintain the educational integrity of the original content
            6. Add brief introductions to sections when helpful
            7. Remove redundant raw JSON data unless specifically valuable
            
            ## Source File: %s
            
            ## Content to optimize:
            
            %s
            
            Please return the enhanced Markdown content:
            """, fileName, content);
    }

    /**
     * 清理AI返回的markdown内容
     */
    private String cleanMarkdownResponse(String response) {
        // 移除可能的代码块标记
        response = response.replaceAll("```markdown\\s*", "");
        response = response.replaceAll("```\\s*$", "");
        
        // 清理多余的空行
        response = response.replaceAll("\n{3,}", "\n\n");
        
        return response.trim();
    }

    /**
     * 生成目录markdown文档头部
     */
    private String generateDirectoryHeader(String directoryName) {
        return String.format("""
            # %s - Educational Content Collection
            
            > This document is automatically generated by AI from JSON educational content files
            > 
            > Generated Time: %s
            > Source Directory: %s
            > Processing Model: %s
            
            ---
            """, 
            directoryName, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            directoryName,
            modelName
        );
    }
}