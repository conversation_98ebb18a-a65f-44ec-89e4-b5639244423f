package com.hujun.pdfparser.markdown.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 使用Ollama模型将txt文件转换为结构化markdown的服务
 */
@Slf4j
@Service("txtToMarkdownService")
public class TxtToMarkdownService {

    @Value("${langchain4j.ollama.chat-model.base-url}")
    private String ollamaBaseUrl;

    @Value("${langchain4j.ollama.chat-model.model-name}")
    private String modelName;

    @Value("${langchain4j.ollama.chat-model.temperature}")
    private Double temperature;

    @Value("${langchain4j.ollama.chat-model.max-tokens}")
    private Integer maxTokens;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 检查Ollama服务是否可用
     */
    private void checkOllamaService() {
        try {
            String healthUrl = ollamaBaseUrl + "/api/tags";
            ResponseEntity<String> response = restTemplate.getForEntity(healthUrl, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Ollama服务连接正常: {}", ollamaBaseUrl);
            }
        } catch (Exception e) {
            log.warn("Ollama服务连接检查失败: {}", e.getMessage());
            throw new RuntimeException("无法连接到Ollama服务，请确保服务已启动: " + ollamaBaseUrl);
        }
    }

    /**
     * 处理指定目录下的所有txt文件，生成markdown文件
     */
    public void processAllTxtFiles(String inputDir, String outputDir) {
        try {
            checkOllamaService();
            
            Path inputPath = Paths.get(inputDir);
            Path outputPath = Paths.get(outputDir);
            
            // 确保输出目录存在
            if (!Files.exists(outputPath)) {
                Files.createDirectories(outputPath);
            }

            // 获取所有txt文件
            List<Path> txtFiles = new ArrayList<>();
            Files.walk(inputPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().toLowerCase().endsWith(".txt"))
                    .forEach(txtFiles::add);

            log.info("找到 {} 个txt文件需要处理", txtFiles.size());

            for (Path txtFile : txtFiles) {
                processSingleFile(txtFile, outputPath);
            }

            log.info("所有文件处理完成！");

        } catch (IOException e) {
            log.error("处理文件时发生错误", e);
            throw new RuntimeException("文件处理失败", e);
        }
    }

    /**
     * 处理单个txt文件
     */
    private void processSingleFile(Path txtFile, Path outputDir) {
        try {
            log.info("开始处理文件: {}", txtFile.getFileName());
            
            // 读取文件内容
            String content = Files.readString(txtFile);
            
            // 分块处理大文件
            List<String> chunks = splitContent(content, 3000); // 每块3000字符
            StringBuilder markdownBuilder = new StringBuilder();
            
            // 添加文档头部
            String fileName = txtFile.getFileName().toString().replace(".txt", "");
            markdownBuilder.append(generateMarkdownHeader(fileName));
            
            // 处理每个块
            for (int i = 0; i < chunks.size(); i++) {
                log.info("处理文件 {} 的第 {}/{} 块", fileName, i + 1, chunks.size());
                String chunkMarkdown = processChunk(chunks.get(i), i + 1);
                markdownBuilder.append("\n\n").append(chunkMarkdown);
            }
            
            // 保存markdown文件
            String outputFileName = fileName + "_structured.md";
            Path outputFile = outputDir.resolve(outputFileName);
            Files.writeString(outputFile, markdownBuilder.toString());
            
            log.info("文件处理完成: {}", outputFileName);
            
        } catch (IOException e) {
            log.error("处理文件 {} 时发生错误", txtFile.getFileName(), e);
        }
    }

    /**
     * 将内容分块，避免超过模型token限制
     */
    private List<String> splitContent(String content, int maxChunkSize) {
        List<String> chunks = new ArrayList<>();
        
        if (content.length() <= maxChunkSize) {
            chunks.add(content);
            return chunks;
        }
        
        // 按段落分割，尽量保持内容完整性
        String[] paragraphs = content.split("\n\n");
        StringBuilder currentChunk = new StringBuilder();
        
        for (String paragraph : paragraphs) {
            if (currentChunk.length() + paragraph.length() + 2 <= maxChunkSize) {
                if (currentChunk.length() > 0) {
                    currentChunk.append("\n\n");
                }
                currentChunk.append(paragraph);
            } else {
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString());
                    currentChunk = new StringBuilder();
                }
                
                // 如果单个段落太长，强制分割
                if (paragraph.length() > maxChunkSize) {
                    for (int i = 0; i < paragraph.length(); i += maxChunkSize) {
                        int end = Math.min(i + maxChunkSize, paragraph.length());
                        chunks.add(paragraph.substring(i, end));
                    }
                } else {
                    currentChunk.append(paragraph);
                }
            }
        }
        
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString());
        }
        
        return chunks;
    }

    /**
     * 使用AI模型处理单个内容块
     */
    private String processChunk(String chunk, int chunkIndex) {
        String prompt = buildPrompt(chunk, chunkIndex);
        
        try {
            String response = callOllamaAPI(prompt);
            return cleanMarkdownResponse(response);
        } catch (Exception e) {
            log.error("AI处理第{}块内容时发生错误", chunkIndex, e);
            return "## Processing Error\n\nOriginal Content:\n```\n" + chunk + "\n```";
        }
    }

    /**
     * 调用Ollama API
     */
    private String callOllamaAPI(String prompt) {
        try {
            String url = ollamaBaseUrl + "/api/generate";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", modelName);
            requestBody.put("prompt", prompt);
            requestBody.put("stream", false);
            requestBody.put("options", Map.of(
                "temperature", temperature,
                "num_predict", maxTokens
            ));
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.POST, request, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode jsonResponse = objectMapper.readTree(response.getBody());
                return jsonResponse.get("response").asText();
            } else {
                throw new RuntimeException("Ollama API调用失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("调用Ollama API时发生错误", e);
            throw new RuntimeException("Ollama API调用失败: " + e.getMessage());
        }
    }

    /**
     * 构建AI处理的提示词
     */
    private String buildPrompt(String content, int chunkIndex) {
        return String.format("""
            Please convert the following English textbook content into structured Markdown format. Requirements:
            
            1. Identify and extract main topics and concepts
            2. Use appropriate heading levels (##, ###, ####)
            3. Organize content into logically clear paragraphs
            4. Identify and format:
               - Important concepts (**bold**)
               - Example content (> quote blocks)
               - Exercises (- list items)
               - Vocabulary explanations (tables or definition lists)
            5. Maintain the educational value and integrity of the original text
            6. Add appropriate sections and subheadings
            
            ## Original Content (Chunk %d):
            
            %s
            
            Please return the formatted Markdown content:
            """, chunkIndex, content);
    }

    /**
     * 清理AI返回的markdown内容
     */
    private String cleanMarkdownResponse(String response) {
        // 移除可能的代码块标记
        response = response.replaceAll("```markdown\\s*", "");
        response = response.replaceAll("```\\s*$", "");
        
        // 清理多余的空行
        response = response.replaceAll("\n{3,}", "\n\n");
        
        return response.trim();
    }

    /**
     * 生成markdown文档头部
     */
    private String generateMarkdownHeader(String fileName) {
        return String.format("""
            # %s - Structured Document
            
            > This document is automatically generated by AI, structured and organized from original txt file content
            > 
            > Generated Time: %s
            > Original File: %s.txt
            > Processing Model: %s
            
            ---
            """, 
            fileName, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            fileName,
            modelName
        );
    }
}