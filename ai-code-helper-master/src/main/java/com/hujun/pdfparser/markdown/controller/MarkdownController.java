package com.hujun.pdfparser.markdown.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.hujun.pdfparser.markdown.service.TxtToMarkdownService;
import com.hujun.pdfparser.markdown.service.JsonToMarkdownService;
import com.hujun.pdfparser.markdown.service.PdfParsingService;

import java.io.File;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * Markdown转换控制器
 */
@Slf4j
@RestController("markdownController")
@RequestMapping("/markdown")
@RequiredArgsConstructor
public class MarkdownController {

    private final TxtToMarkdownService txtToMarkdownService;
    private final JsonToMarkdownService jsonToMarkdownService;
    private final PdfParsingService pdfParsingService;

    /**
     * 将parsed_output目录下的txt文件转换为结构化markdown
     */
    @PostMapping("/convert")
    public ResponseEntity<Map<String, Object>> convertTxtToMarkdown() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始转换txt文件为markdown格式");
            
            // 使用项目中的默认路径
            String inputDir = "src/main/resources/docs/parsed_output";
            String outputDir = "src/main/resources/docs/markdown_output";
            
            // 检查输入目录是否存在
            File inputDirFile = new File(inputDir);
            if (!inputDirFile.exists()) {
                response.put("success", false);
                response.put("message", "输入目录不存在: " + inputDir);
                return ResponseEntity.badRequest().body(response);
            }
            
            // 执行转换
            txtToMarkdownService.processAllTxtFiles(inputDir, outputDir);
            
            response.put("success", true);
            response.put("message", "转换完成");
            response.put("inputDir", Paths.get(inputDir).toAbsolutePath().toString());
            response.put("outputDir", Paths.get(outputDir).toAbsolutePath().toString());
            
            log.info("txt文件转换markdown完成");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("转换过程中发生错误", e);
            response.put("success", false);
            response.put("message", "转换失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 自定义路径转换
     */
    @PostMapping("/convert/custom")
    public ResponseEntity<Map<String, Object>> convertCustomPath(
            @RequestParam String inputDir,
            @RequestParam(required = false) String outputDir) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始自定义路径转换: {} -> {}", inputDir, outputDir);
            
            // 如果没有指定输出目录，使用默认值
            if (outputDir == null || outputDir.trim().isEmpty()) {
                outputDir = "src/main/resources/docs/markdown_output";
            }
            
            // 检查输入目录是否存在
            File inputDirFile = new File(inputDir);
            if (!inputDirFile.exists()) {
                response.put("success", false);
                response.put("message", "输入目录不存在: " + inputDir);
                return ResponseEntity.badRequest().body(response);
            }
            
            // 执行转换
            txtToMarkdownService.processAllTxtFiles(inputDir, outputDir);
            
            response.put("success", true);
            response.put("message", "转换完成");
            response.put("inputDir", Paths.get(inputDir).toAbsolutePath().toString());
            response.put("outputDir", Paths.get(outputDir).toAbsolutePath().toString());
            
            log.info("自定义路径转换完成");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("自定义路径转换过程中发生错误", e);
            response.put("success", false);
            response.put("message", "转换失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 解析指定目录下的PDF文件
     */
    @PostMapping("/parse-pdfs")
    public ResponseEntity<Map<String, Object>> parsePdfs(@RequestParam String directoryPath) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始解析PDF文件，目录: {}", directoryPath);
            
            // 检查输入目录是否存在
            File inputDir = new File(directoryPath);
            if (!inputDir.exists()) {
                response.put("success", false);
                response.put("message", "目录不存在: " + directoryPath);
                return ResponseEntity.badRequest().body(response);
            }
            
            // 执行PDF解析
            Map<String, String> parsedPdfs = pdfParsingService.parsePdfsInDirectory(directoryPath);
            
            response.put("success", true);
            response.put("message", "PDF解析完成");
            response.put("directoryPath", Paths.get(directoryPath).toAbsolutePath().toString());
            response.put("outputDir", Paths.get(directoryPath + "/parsed_output").toAbsolutePath().toString());
            response.put("parsedFileCount", parsedPdfs.size());
            response.put("parsedFiles", parsedPdfs.keySet());
            
            log.info("PDF解析完成，共解析 {} 个文件", parsedPdfs.size());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("PDF解析过程中发生错误", e);
            response.put("success", false);
            response.put("message", "解析失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 解析默认文档目录的PDF文件
     */
    @PostMapping("/parse-pdfs/default")
    public ResponseEntity<Map<String, Object>> parseDefaultPdfs() {
        String defaultDir = "src/main/resources/docs";
        return parsePdfs(defaultDir);
    }

    /**
     * 将指定目录下的JSON文件转换为结构化markdown
     */
    @PostMapping("/convert-json")
    public ResponseEntity<Map<String, Object>> convertJsonToMarkdown() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始转换JSON文件为markdown格式");
            
            // 使用指定的JSON文件路径
            String inputDir = "data/myelt/2025-06-26/output_json";
            String outputDir = "src/main/resources/docs/json_markdown_output";
            
            // 检查输入目录是否存在
            File inputDirFile = new File(inputDir);
            if (!inputDirFile.exists()) {
                response.put("success", false);
                response.put("message", "输入目录不存在: " + inputDir);
                return ResponseEntity.badRequest().body(response);
            }
            
            // 执行转换
            jsonToMarkdownService.processAllJsonFiles(inputDir, outputDir);
            
            response.put("success", true);
            response.put("message", "JSON文件转换完成");
            response.put("inputDir", Paths.get(inputDir).toAbsolutePath().toString());
            response.put("outputDir", Paths.get(outputDir).toAbsolutePath().toString());
            
            log.info("JSON文件转换markdown完成");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("JSON转换过程中发生错误", e);
            response.put("success", false);
            response.put("message", "转换失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 自定义路径JSON转换
     */
    @PostMapping("/convert-json/custom")
    public ResponseEntity<Map<String, Object>> convertJsonCustomPath(
            @RequestParam String inputDir,
            @RequestParam(required = false) String outputDir) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始自定义路径JSON转换: {} -> {}", inputDir, outputDir);
            
            // 如果没有指定输出目录，使用默认值
            if (outputDir == null || outputDir.trim().isEmpty()) {
                outputDir = "src/main/resources/docs/json_markdown_output";
            }
            
            // 检查输入目录是否存在
            File inputDirFile = new File(inputDir);
            if (!inputDirFile.exists()) {
                response.put("success", false);
                response.put("message", "输入目录不存在: " + inputDir);
                return ResponseEntity.badRequest().body(response);
            }
            
            // 执行转换
            jsonToMarkdownService.processAllJsonFiles(inputDir, outputDir);
            
            response.put("success", true);
            response.put("message", "JSON转换完成");
            response.put("inputDir", Paths.get(inputDir).toAbsolutePath().toString());
            response.put("outputDir", Paths.get(outputDir).toAbsolutePath().toString());
            
            log.info("自定义路径JSON转换完成");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("自定义路径JSON转换过程中发生错误", e);
            response.put("success", false);
            response.put("message", "转换失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 检查Ollama服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> checkStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查parsed_output目录
            String inputDir = "src/main/resources/docs/parsed_output";
            File inputDirFile = new File(inputDir);
            
            response.put("inputDirExists", inputDirFile.exists());
            response.put("inputDir", Paths.get(inputDir).toAbsolutePath().toString());
            
            if (inputDirFile.exists()) {
                File[] txtFiles = inputDirFile.listFiles((dir, name) -> name.toLowerCase().endsWith(".txt"));
                response.put("txtFileCount", txtFiles != null ? txtFiles.length : 0);
            } else {
                response.put("txtFileCount", 0);
            }
            
            response.put("message", "状态检查完成");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("状态检查失败", e);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}