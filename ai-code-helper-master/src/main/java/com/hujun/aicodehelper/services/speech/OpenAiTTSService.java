package com.hujun.aicodehelper.services.speech;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.scheduler.Schedulers;

/**
 * OpenAI兼容的TTS服务
 * 支持与OpenAI TTS API兼容的语音合成服务
 */
@Slf4j
@Service
public class OpenAiTTSService {

    @Value("${speech.openai.base-url:http://localhost:5050}")
    private String baseUrl;

    @Value("${speech.openai.api-key:your_api_key_here}")
    private String apiKey;

    @Value("${speech.openai.timeout:30}")
    private int timeoutSeconds;

    @Value("${speech.openai.default-model:tts-1}")
    private String defaultModel;

    @Value("${speech.openai.default-voice:alloy}")
    private String defaultVoice;

    @Value("${speech.openai.default-speed:1.0}")
    private double defaultSpeed;

    @Value("${speech.openai.default-response-format:mp3}")
    private String defaultResponseFormat;

    private final OkHttpClient client;
    private final ObjectMapper objectMapper;

    public OpenAiTTSService() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 使用默认参数进行TTS转换
     */
    public TTSResult synthesize(String input) {
        return synthesize(defaultModel, input, defaultVoice, defaultSpeed, defaultResponseFormat);
    }

    /**
     * 使用指定参数进行TTS转换
     */
    public TTSResult synthesize(String model, String input, String voice) {
        return synthesize(model, input, voice, defaultSpeed, defaultResponseFormat);
    }

    /**
     * 使用完整参数进行TTS转换
     */
    public TTSResult synthesize(String model, String input, String voice, double speed, String responseFormat) {
        if (input == null || input.trim().isEmpty()) {
            log.error("TTS输入文本为空");
            return TTSResult.error("输入文本不能为空");
        }

        try {
            log.info("开始OpenAI TTS转换，文本长度: {} 字符，模型: {}, 语音: {}, 语速: {}, 格式: {}", 
                    input.length(), model, voice, speed, responseFormat);

            // 构建请求体
            TTSRequest request = new TTSRequest();
            request.setModel(model);
            request.setInput(input.trim());
            request.setVoice(voice);
            request.setSpeed(speed);
            request.setResponseFormat(responseFormat);

            String requestJson = objectMapper.writeValueAsString(request);
            log.debug("TTS请求JSON: {}", requestJson);

            RequestBody body = RequestBody.create(requestJson, MediaType.parse("application/json"));
            
            String url = baseUrl;
            if (!url.endsWith("/")) {
                url += "/";
            }
            url += "v1/audio/speech";

            Request httpRequest = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .build();

            try (Response response = client.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "无响应内容";
                    log.error("OpenAI TTS API调用失败，状态码: {}, 响应: {}", 
                            response.code(), errorBody);
                    return TTSResult.error("TTS转换失败，API返回错误: " + response.code() + ", 详情: " + errorBody);
                }

                // 读取音频数据
                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    log.error("响应体为空");
                    return TTSResult.error("响应体为空");
                }

                byte[] audioData = readAudioData(responseBody.byteStream());
                String contentType = response.header("Content-Type", "audio/mpeg");

                log.info("✅ OpenAI TTS转换成功，音频数据大小: {} bytes, 内容类型: {}", 
                        audioData.length, contentType);

                return TTSResult.success(audioData, contentType, responseFormat);
            }

        } catch (Exception e) {
            log.error("OpenAI TTS转换发生错误", e);
            return TTSResult.error("TTS转换失败: " + e.getMessage());
        }
    }

    /**
     * 通过SSE流式合成TTS音频（openai-edge-tts: stream_format = "sse"）
     * 将上游SSE事件透明转发为下游SSE，data通常包含音频分片的Base64字符串或状态JSON。
     */
    public Flux<ServerSentEvent<String>> synthesizeStream(String model,
                                                         String input,
                                                         String voice,
                                                         double speed,
                                                         String responseFormat,
                                                         String streamFormat) {
        return Flux.create((FluxSink<ServerSentEvent<String>> sink) -> {
            if (input == null || input.trim().isEmpty()) {
                sink.error(new IllegalArgumentException("输入文本不能为空"));
                return;
            }

            try {
                log.info("开始OpenAI TTS流式转换，文本长度: {} 字符，模型: {}, 语音: {}, 语速: {}, 格式: {}, 流格式: {}",
                        input.length(), model, voice, speed, responseFormat, streamFormat);

                // 构建请求体（开启流式 + SSE）
                StreamingTTSRequest request = new StreamingTTSRequest();
                request.setModel(model);
                request.setInput(input.trim());
                request.setVoice(voice);
                request.setSpeed(speed);
                request.setResponseFormat(responseFormat);
                request.setStream(true);
                request.setStreamFormat(streamFormat != null ? streamFormat : "sse");

                String requestJson = objectMapper.writeValueAsString(request);
                log.debug("TTS流式请求JSON: {}", requestJson);

                RequestBody body = RequestBody.create(requestJson, MediaType.parse("application/json"));

                String url = baseUrl;
                if (!url.endsWith("/")) {
                    url += "/";
                }
                url += "v1/audio/speech";

                Request httpRequest = new Request.Builder()
                        .url(url)
                        .post(body)
                        .addHeader("Content-Type", "application/json")
                        .addHeader("Accept", "text/event-stream")
                        .addHeader("Cache-Control", "no-cache")
                        .addHeader("Connection", "keep-alive")
                        .addHeader("Authorization", "Bearer " + apiKey)
                        .build();

                Call call = client.newCall(httpRequest);
                AtomicBoolean completed = new AtomicBoolean(false);

                // 在弹性调度器上执行，避免阻塞调用线程
                Schedulers.boundedElastic().schedule(() -> {
                    try (Response response = call.execute()) {
                        if (!response.isSuccessful()) {
                            String errorBody = response.body() != null ? response.body().string() : "无响应内容";
                            String msg = "TTS流式API返回错误: " + response.code() + ", 详情: " + errorBody;
                            log.error(msg);
                            sink.error(new IOException(msg));
                            return;
                        }

                        ResponseBody responseBody = response.body();
                        if (responseBody == null) {
                            sink.error(new IOException("流式响应体为空"));
                            return;
                        }

                        try (BufferedReader reader = new BufferedReader(new InputStreamReader(responseBody.byteStream()))) {
                            String line;
                            String currentEvent = null;
                            StringBuilder dataBuffer = new StringBuilder();

                            while ((line = reader.readLine()) != null) {
                                // SSE心跳或注释
                                if (line.startsWith(":")) {
                                    continue;
                                }

                                if (line.isEmpty()) {
                                    // 事件结束，发送
                                    if (dataBuffer.length() > 0) {
                                        String data = dataBuffer.toString();
                                        // 处理完成信号
                                        if ("[DONE]".equals(data) || "done".equalsIgnoreCase(currentEvent) || "end".equalsIgnoreCase(currentEvent)) {
                                            sink.next(ServerSentEvent.<String>builder().event("done").data("done").build());
                                            completed.set(true);
                                            break;
                                        }
                                        sink.next(ServerSentEvent.<String>builder()
                                                .event(currentEvent != null ? currentEvent : "message")
                                                .data(data)
                                                .build());
                                        dataBuffer.setLength(0);
                                        currentEvent = null;
                                    }
                                    continue;
                                }

                                if (line.startsWith("event:")) {
                                    currentEvent = line.substring(6).trim();
                                } else if (line.startsWith("data:")) {
                                    if (dataBuffer.length() > 0) dataBuffer.append('\n');
                                    dataBuffer.append(line.substring(5).trim());
                                }
                            }
                        }

                        if (!completed.get()) {
                            sink.next(ServerSentEvent.<String>builder().event("done").data("done").build());
                        }
                        sink.complete();
                    } catch (Exception ex) {
                        if (!sink.isCancelled()) {
                            log.error("OpenAI TTS流式转换异常", ex);
                            sink.error(ex);
                        }
                    }
                });

                // 取消时中断请求
                sink.onCancel(() -> {
                    try {
                        call.cancel();
                    } catch (Exception ignore) {
                    }
                });
            } catch (Exception e) {
                sink.error(e);
            }
        }, FluxSink.OverflowStrategy.BUFFER);
    }

    /**
     * 读取音频数据
     */
    private byte[] readAudioData(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[8192];
        int bytesRead;
        
        while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, bytesRead);
        }
        
        return buffer.toByteArray();
    }

    /**
     * 测试API连接
     */
    public boolean testConnection() {
        try {
            TTSResult result = synthesize("tts-1", "Test connection", defaultVoice);
            return result.isSuccess();
        } catch (Exception e) {
            log.error("OpenAI TTS连接测试失败", e);
            return false;
        }
    }

    /**
     * 获取服务状态信息
     */
    public ServiceStatus getServiceStatus() {
        ServiceStatus status = new ServiceStatus();
        status.setServiceName("OpenAI Compatible TTS");
        status.setBaseUrl(baseUrl);
        status.setDefaultModel(defaultModel);
        status.setDefaultVoice(defaultVoice);
        status.setDefaultSpeed(defaultSpeed);
        status.setDefaultFormat(defaultResponseFormat);

        try {
            boolean isConnected = testConnection();
            status.setAvailable(isConnected);
            status.setMessage(isConnected ? "服务正常" : "服务不可用");
        } catch (Exception e) {
            status.setAvailable(false);
            status.setMessage("连接测试失败: " + e.getMessage());
        }

        return status;
    }

    // 请求数据结构
    public static class TTSRequest {
        @JsonProperty("model")
        private String model;

        @JsonProperty("input")
        private String input;

        @JsonProperty("voice")
        private String voice;

        @JsonProperty("speed")
        private double speed;

        @JsonProperty("response_format")
        private String responseFormat;

        // Getters and Setters
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }

        public String getInput() { return input; }
        public void setInput(String input) { this.input = input; }

        public String getVoice() { return voice; }
        public void setVoice(String voice) { this.voice = voice; }

        public double getSpeed() { return speed; }
        public void setSpeed(double speed) { this.speed = speed; }

        public String getResponseFormat() { return responseFormat; }
        public void setResponseFormat(String responseFormat) { this.responseFormat = responseFormat; }
    }

    /**
     * 流式请求数据结构
     */
    public static class StreamingTTSRequest extends TTSRequest {
        @JsonProperty("stream")
        private boolean stream;

        @JsonProperty("stream_format")
        private String streamFormat;

        public boolean isStream() { return stream; }
        public void setStream(boolean stream) { this.stream = stream; }

        public String getStreamFormat() { return streamFormat; }
        public void setStreamFormat(String streamFormat) { this.streamFormat = streamFormat; }
    }

    // 结果封装类
    public static class TTSResult {
        private boolean success;
        private byte[] audioData;
        private String contentType;
        private String format;
        private String errorMessage;

        private TTSResult() {}

        public static TTSResult success(byte[] audioData, String contentType, String format) {
            TTSResult result = new TTSResult();
            result.success = true;
            result.audioData = audioData;
            result.contentType = contentType;
            result.format = format;
            return result;
        }

        public static TTSResult error(String errorMessage) {
            TTSResult result = new TTSResult();
            result.success = false;
            result.errorMessage = errorMessage;
            return result;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public byte[] getAudioData() { return audioData; }
        public String getContentType() { return contentType; }
        public String getFormat() { return format; }
        public String getErrorMessage() { return errorMessage; }
    }

    // 服务状态类
    public static class ServiceStatus {
        private String serviceName;
        private String baseUrl;
        private String defaultModel;
        private String defaultVoice;
        private double defaultSpeed;
        private String defaultFormat;
        private boolean available;
        private String message;

        // Getters and Setters
        public String getServiceName() { return serviceName; }
        public void setServiceName(String serviceName) { this.serviceName = serviceName; }

        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }

        public String getDefaultModel() { return defaultModel; }
        public void setDefaultModel(String defaultModel) { this.defaultModel = defaultModel; }

        public String getDefaultVoice() { return defaultVoice; }
        public void setDefaultVoice(String defaultVoice) { this.defaultVoice = defaultVoice; }

        public double getDefaultSpeed() { return defaultSpeed; }
        public void setDefaultSpeed(double defaultSpeed) { this.defaultSpeed = defaultSpeed; }

        public String getDefaultFormat() { return defaultFormat; }
        public void setDefaultFormat(String defaultFormat) { this.defaultFormat = defaultFormat; }

        public boolean isAvailable() { return available; }
        public void setAvailable(boolean available) { this.available = available; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
