package com.hujun.aicodehelper.services.multimodal;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.hujun.aicodehelper.services.moderation.ContentModerationService;

import dev.langchain4j.data.message.Content;
import dev.langchain4j.data.message.ImageContent;
import dev.langchain4j.data.message.TextContent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 多模态处理服务 - 负责图片、文本等多模态内容的处理
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MultiModalService {

    private final ContentModerationService moderationService;

    @Value("${ai.chat.image.public-base-url:}")
    private String publicBaseUrl;

    /**
     * 验证多模态内容
     */
    public void validateContents(List<Content> contents) {
        if (contents == null || contents.isEmpty()) {
            throw new IllegalArgumentException("内容列表不能为空");
        }

        for (Content content : contents) {
            if (content instanceof TextContent textContent) {
                moderationService.validateInput(textContent.text());
            } else if (content instanceof ImageContent imageContent) {
                validateImageContent(imageContent);
            }
        }

        log.debug("✅ 多模态内容验证通过，内容数量: {}", contents.size());
    }

    /**
     * 提取文本摘要用于模型选择
     */
    public String extractTextSummary(List<Content> contents) {
        StringBuilder summary = new StringBuilder();

        for (Content content : contents) {
            if (content instanceof TextContent textContent) {
                String text = textContent.text();
                if (text != null) {
                    summary.append(text).append(" ");
                }
            } else if (content instanceof ImageContent) {
                summary.append("[图片] ");
            }
        }

        String result = summary.toString().trim();
        log.debug("📝 提取文本摘要: {}", result.substring(0, Math.min(100, result.length())));
        return result;
    }

    /**
     * 构建语言提示
     */
    public String buildLanguageHint(String language) {
        return switch (language.toLowerCase()) {
            case "en" -> "Please respond in English.";
            case "zh" -> "请用中文回复。";
            case "ja" -> "日本語で回答してください。";
            case "ko" -> "한국어로 답변해 주세요.";
            case "fr" -> "Veuillez répondre en français.";
            case "de" -> "Bitte antworten Sie auf Deutsch.";
            case "es" -> "Por favor responde en español.";
            default -> "Please respond in " + language + ".";
        };
    }

    /**
     * 处理图片URL
     */
    public String processImageUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            return imageUrl;
        }

        // 如果是相对路径，转换为绝对路径
        if (!imageUrl.startsWith("http") && !publicBaseUrl.isEmpty()) {
            String processedUrl = publicBaseUrl + (imageUrl.startsWith("/") ? "" : "/") + imageUrl;
            log.debug("🖼️ 转换图片URL: {} -> {}", imageUrl, processedUrl);
            return processedUrl;
        }

        return imageUrl;
    }

    private void validateImageContent(ImageContent imageContent) {
        // 图片内容验证逻辑
        if (imageContent.image() == null) {
            throw new IllegalArgumentException("图片内容不能为空");
        }

        // 可以添加更多图片验证逻辑，如格式检查、大小限制等
        log.debug("✅ 图片内容验证通过");
    }
}
