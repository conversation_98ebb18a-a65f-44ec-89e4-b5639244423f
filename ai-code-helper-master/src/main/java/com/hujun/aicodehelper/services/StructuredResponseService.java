package com.hujun.aicodehelper.services;

import com.hujun.aicodehelper.model.StructuredResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 结构化响应处理服务
 * 负责分离工具调用结果和普通AI回复
 */
@Slf4j
@Service
public class StructuredResponseService {

    // 匹配工具调用结果的正则表达式模式
    // 匹配格式如：📝 **一元方程求解**、📊 计算结果：等
    private static final Pattern TOOL_RESULT_PATTERN = Pattern.compile(
        "^[📝📊🧮✨📐🔍🎯]\\s*\\*\\*([^*]+)\\*\\*", 
        Pattern.MULTILINE
    );
    
    // 匹配数学工具输出的特征模式
    private static final Pattern MATH_TOOL_OUTPUT_PATTERN = Pattern.compile(
        "(原方程：|原表达式：|原不等式：|计算结果：|分解结果：|化简结果：|展开结果：|解：|解集：)",
        Pattern.MULTILINE
    );
    
    // 检测是否为工具调用结果的关键词
    private static final String[] TOOL_RESULT_KEYWORDS = {
        "📝 **一元方程求解**",
        "📝 **方程组求解**", 
        "📝 **因式分解**",
        "📝 **表达式化简**",
        "📝 **表达式展开**",
        "📝 **不等式求解**",
        "📝 **表达式求值**",
        "📝 **导数计算**",
        "📝 **积分计算**",
        "📝 **极限计算**",
        "📊 计算结果：",
        "🧮 数学引擎",
        "✅ 计算完成"
    };

    /**
     * 处理AI流式响应，分离工具调用结果和普通回复
     * 
     * @param originalFlux 原始AI响应流
     * @return 结构化响应流
     */
    public Flux<String> processAiResponse(Flux<String> originalFlux) {
        StringBuilder accumulatedContent = new StringBuilder();
        
        return originalFlux
            .doOnNext(chunk -> {
                log.debug("📨 处理AI响应块: {}", chunk != null ? chunk.substring(0, Math.min(50, chunk.length())) + "..." : "null");
            })
            .concatMap(chunk -> {
                if (chunk == null || chunk.trim().isEmpty()) {
                    return Flux.empty();
                }
                
                accumulatedContent.append(chunk);
                
                // 检查当前块是否包含工具调用结果
                if (isToolResultChunk(chunk)) {
                    log.info("🛠️ 检测到工具调用结果块: {}", chunk.substring(0, Math.min(100, chunk.length())));
                    
                    // 提取工具信息
                    ToolInfo toolInfo = extractToolInfo(chunk);
                    
                    // 返回工具结果响应
                    return Flux.just(
                        StructuredResponse.toolResult(
                            toolInfo.toolName,
                            toolInfo.toolInput,
                            chunk
                        ).toJsonString()
                    );
                } else {
                    // 作为普通回复返回
                    return Flux.just(StructuredResponse.normalReply(chunk).toJsonString());
                }
            })
            .doOnComplete(() -> {
                log.info("✅ AI响应处理完成，累积内容长度: {}", accumulatedContent.length());
            });
    }
    
    
    /**
     * 检查单个chunk是否为工具结果
     */
    private boolean isToolResultChunk(String chunk) {
        if (chunk == null || chunk.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含工具结果特征
        for (String keyword : TOOL_RESULT_KEYWORDS) {
            if (chunk.contains(keyword)) {
                log.debug("🔍 通过关键词检测到工具结果: {}", keyword);
                return true;
            }
        }
        
        // 检查工具结果格式模式
        if (TOOL_RESULT_PATTERN.matcher(chunk).find()) {
            log.debug("🔍 通过格式模式检测到工具结果");
            return true;
        }
        
        // 检查数学工具输出模式
        if (MATH_TOOL_OUTPUT_PATTERN.matcher(chunk).find()) {
            log.debug("🔍 通过数学工具模式检测到工具结果");
            return true;
        }
        
        // 额外检查：如果chunk包含多行且有明显的工具输出结构
        if (chunk.contains("\n") && (
            chunk.contains("原方程：") || 
            chunk.contains("原表达式：") || 
            chunk.contains("原不等式：") ||
            chunk.contains("分解结果：") ||
            chunk.contains("化简结果：") ||
            chunk.contains("展开结果：") ||
            chunk.contains("解：") ||
            chunk.contains("解集：")
        )) {
            log.debug("🔍 通过多行结构检测到工具结果");
            return true;
        }
        
        return false;
    }
    
    /**
     * 从内容中提取工具信息
     */
    private ToolInfo extractToolInfo(String content) {
        String toolName = "数学工具"; // 默认工具名称
        String toolInput = "";
        
        // 尝试匹配工具结果标题
        Matcher titleMatcher = TOOL_RESULT_PATTERN.matcher(content);
        if (titleMatcher.find()) {
            toolName = titleMatcher.group(1).trim();
            log.debug("🏷️ 通过标题提取工具名称: {}", toolName);
        }
        
        // 尝试提取输入信息，优先级从高到低
        if (content.contains("原方程：")) {
            toolName = "一元方程求解";
            toolInput = extractAfterPattern(content, "原方程：");
        } else if (content.contains("原方程组：")) {
            toolName = "方程组求解";
            toolInput = extractAfterPattern(content, "原方程组：");
        } else if (content.contains("原表达式：")) {
            // 根据后续内容判断具体操作类型
            if (content.contains("分解结果：")) {
                toolName = "因式分解";
            } else if (content.contains("化简结果：")) {
                toolName = "表达式化简";
            } else if (content.contains("展开结果：")) {
                toolName = "表达式展开";
            } else {
                toolName = "表达式处理";
            }
            toolInput = extractAfterPattern(content, "原表达式：");
        } else if (content.contains("原不等式：")) {
            toolName = "不等式求解";
            toolInput = extractAfterPattern(content, "原不等式：");
        } else if (content.contains("分解结果：")) {
            toolName = "因式分解";
            // 尝试从前文提取原始表达式
            toolInput = extractBeforePattern(content, "分解结果：");
        } else if (content.contains("化简结果：")) {
            toolName = "表达式化简";
            toolInput = extractBeforePattern(content, "化简结果：");
        } else if (content.contains("展开结果：")) {
            toolName = "表达式展开";
            toolInput = extractBeforePattern(content, "展开结果：");
        }
        
        log.debug("🔧 提取到工具信息 - 名称: {}, 输入: {}", toolName, toolInput);
        return new ToolInfo(toolName, toolInput);
    }
    
    /**
     * 提取模式后的内容
     */
    private String extractAfterPattern(String content, String pattern) {
        int index = content.indexOf(pattern);
        if (index != -1) {
            int start = index + pattern.length();
            int end = content.indexOf('\n', start);
            if (end == -1) end = content.length();
            return content.substring(start, end).trim();
        }
        return "";
    }
    
    /**
     * 提取模式前的内容（用于从结果中反推输入）
     */
    private String extractBeforePattern(String content, String pattern) {
        int index = content.indexOf(pattern);
        if (index != -1) {
            // 向前查找最近的":"后的内容
            String beforePattern = content.substring(0, index);
            String[] lines = beforePattern.split("\n");
            for (int i = lines.length - 1; i >= 0; i--) {
                String line = lines[i].trim();
                if (line.contains("：") && !line.isEmpty()) {
                    int colonIndex = line.indexOf("：");
                    if (colonIndex != -1) {
                        return line.substring(colonIndex + 1).trim();
                    }
                }
            }
        }
        return "";
    }
    
    /**
     * 工具信息内部类
     */
    private static class ToolInfo {
        final String toolName;
        final String toolInput;
        
        ToolInfo(String toolName, String toolInput) {
            this.toolName = toolName;
            this.toolInput = toolInput;
        }
    }
}
