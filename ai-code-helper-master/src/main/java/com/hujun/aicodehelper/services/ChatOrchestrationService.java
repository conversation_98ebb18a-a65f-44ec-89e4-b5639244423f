package com.hujun.aicodehelper.services;

import com.hujun.aicodehelper.ai.AiCodeHelperService;
import com.hujun.aicodehelper.ai.DynamicAiServiceManager;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType;
import com.hujun.aicodehelper.services.conversation.ConversationService;
import com.hujun.aicodehelper.services.moderation.ContentModerationService;
import com.hujun.aicodehelper.services.multimodal.MultiModalService;
import dev.langchain4j.data.message.Content;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 聊天编排服务 - 负责协调各个服务组件完成聊天流程
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChatOrchestrationService {

    private final DynamicAiServiceManager aiServiceManager;
    private final ConversationService conversationService;
    private final ContentModerationService moderationService;
    private final MultiModalService multiModalService;

    /**
     * 处理文本聊天请求
     */
    public Flux<String> processTextChat(int memoryId, String userMessage) {
        log.info("🎯 开始处理文本聊天，会话: {}", memoryId);

        return Flux.fromCallable(() -> {
            // 1. 内容审核
            moderationService.validateInput(userMessage);

            // 2. 获取对话历史
            List<String> history = conversationService.getHistory(memoryId);

            // 3. 智能模型选择
            ModelType modelType = conversationService.selectOptimalModel(userMessage, history, memoryId);

            // 4. 更新对话历史
            conversationService.updateHistory(memoryId, userMessage);

            return modelType;
        })
                .flatMapMany(modelType -> {
                    // 5. 获取AI服务并执行对话
                    AiCodeHelperService aiService = aiServiceManager.getAiServiceByModelType(modelType);
                    return aiService.chatStream(memoryId, userMessage);
                })
                .doOnNext(chunk -> conversationService.trackToolUsage(memoryId, chunk))
                .doOnComplete(() -> log.info("✅ 文本聊天处理完成，会话: {}", memoryId))
                .doOnError(error -> log.error("❌ 文本聊天处理失败，会话: {}, 错误: {}", memoryId, error.getMessage()));
    }

    /**
     * 处理多模态聊天请求
     */
    public Flux<String> processMultiModalChat(int memoryId, List<Content> contents) {
        log.info("🎯 开始处理多模态聊天，会话: {}", memoryId);

        return Flux.fromCallable(() -> {
            // 1. 多模态内容处理和审核
            multiModalService.validateContents(contents);

            // 2. 提取文本摘要用于模型选择
            String textSummary = multiModalService.extractTextSummary(contents);

            // 3. 获取对话历史和选择模型
            List<String> history = conversationService.getHistory(memoryId);
            ModelType modelType = conversationService.selectOptimalModel(textSummary, history, memoryId);

            // 4. 更新对话历史
            conversationService.updateHistory(memoryId, textSummary);

            return modelType;
        })
                .flatMapMany(modelType -> {
                    // 5. 执行多模态对话
                    AiCodeHelperService aiService = aiServiceManager.getAiServiceByModelType(modelType);
                    return aiService.chatStream(memoryId, contents);
                })
                .doOnNext(chunk -> conversationService.trackToolUsage(memoryId, chunk))
                .doOnComplete(() -> log.info("✅ 多模态聊天处理完成，会话: {}", memoryId))
                .doOnError(error -> log.error("❌ 多模态聊天处理失败，会话: {}, 错误: {}", memoryId, error.getMessage()));
    }

    /**
     * 处理带语言参数的聊天请求
     */
    public Flux<String> processChatWithLanguage(int memoryId, String userMessage, String language) {
        log.info("🌍 开始处理带语言参数的聊天，会话: {}, 语言: {}", memoryId, language);

        return Flux.fromCallable(() -> {
            // 1. 内容审核
            moderationService.validateInput(userMessage);

            // 2. 语言特定的模型选择
            List<String> history = conversationService.getHistory(memoryId);
            ModelType modelType = conversationService.selectModelForLanguage(userMessage, history, language);

            // 3. 构建语言提示
            String enhancedMessage = multiModalService.buildLanguageHint(language) + "\n\n" + userMessage;

            // 4. 更新对话历史
            conversationService.updateHistory(memoryId, userMessage);

            return new ChatContext(modelType, enhancedMessage);
        })
                .flatMapMany(context -> {
                    // 5. 执行带语言参数的对话
                    AiCodeHelperService aiService = aiServiceManager.getAiServiceByModelType(context.modelType());
                    return aiService.chatStream(memoryId, context.message());
                })
                .doOnComplete(() -> log.info("✅ 语言聊天处理完成，会话: {}", memoryId));
    }

    private record ChatContext(ModelType modelType, String message) {
    }
}
