package com.hujun.aicodehelper.services.conversation;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Service;

import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话管理服务 - 负责对话历史、模型选择和工具使用跟踪
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ConversationService {

    private final ModelSelectionStrategy modelSelectionStrategy;

    // 会话记忆缓存
    private final Map<Integer, List<String>> conversationHistory = new ConcurrentHashMap<>();

    // 工具使用跟踪
    private final Map<Integer, String> lastToolUsed = new ConcurrentHashMap<>();

    /**
     * 获取对话历史
     */
    public List<String> getHistory(int memoryId) {
        return conversationHistory.computeIfAbsent(memoryId, k -> new java.util.ArrayList<>());
    }

    /**
     * 更新对话历史
     */
    public void updateHistory(int memoryId, String message) {
        List<String> history = getHistory(memoryId);
        history.add(message);

        // 限制历史记录长度，避免内存溢出
        if (history.size() > 50) {
            history.remove(0);
        }

        log.debug("📝 更新会话 {} 历史记录，当前长度: {}", memoryId, history.size());
    }

    /**
     * 智能模型选择
     */
    public ModelType selectOptimalModel(String userMessage, List<String> history, int memoryId) {
        // 1. 检查显式模型请求
        ModelType explicitModel = modelSelectionStrategy.getExplicitModelRequest(userMessage);
        if (explicitModel != null) {
            log.info("🎯 用户显式请求模型: {}", explicitModel.getDescription());
            return explicitModel;
        }

        // 2. 基于工具使用历史优化选择
        String lastTool = lastToolUsed.get(memoryId);
        if (lastTool != null) {
            ModelType toolOptimizedModel = modelSelectionStrategy.selectModelForTool(lastTool);
            log.info("🔧 基于工具使用历史选择模型: {} (上次工具: {})",
                    toolOptimizedModel.getDescription(), lastTool);
            return toolOptimizedModel;
        }

        // 3. 智能策略选择
        ModelType selectedModel = modelSelectionStrategy.selectModel(userMessage, history);
        log.info("🧠 智能策略选择模型: {}", selectedModel.getDescription());
        return selectedModel;
    }

    /**
     * 为特定语言选择模型
     */
    public ModelType selectModelForLanguage(String userMessage, List<String> history, String language) {
        // 多语言场景通常需要更强的模型
        if (!"zh".equals(language) && !"en".equals(language)) {
            log.info("🌍 非中英文语言 {} 使用PRO模型", language);
            return ModelType.PRO;
        }

        return selectOptimalModel(userMessage, history, -1);
    }

    /**
     * 跟踪工具使用情况
     */
    public void trackToolUsage(int memoryId, String responseChunk) {
        if (responseChunk == null)
            return;

        // 检测工具调用模式
        if (responseChunk.contains("📝 **") || responseChunk.contains("📊 计算结果")) {
            String toolName = extractToolName(responseChunk);
            if (toolName != null) {
                lastToolUsed.put(memoryId, toolName);
                log.debug("🔧 检测到工具使用: {} (会话: {})", toolName, memoryId);
            }
        }
    }

    /**
     * 清理会话数据
     */
    public void clearSession(int memoryId) {
        conversationHistory.remove(memoryId);
        lastToolUsed.remove(memoryId);
        log.info("🧹 清理会话 {} 的数据", memoryId);
    }

    private String extractToolName(String responseChunk) {
        if (responseChunk.contains("一元方程求解"))
            return "math_equation";
        if (responseChunk.contains("因式分解"))
            return "math_factorization";
        if (responseChunk.contains("表达式化简"))
            return "math_simplification";
        if (responseChunk.contains("PhET"))
            return "phet_simulation";
        return null;
    }
}
