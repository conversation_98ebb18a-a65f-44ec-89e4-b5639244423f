package com.hujun.aicodehelper.services;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * PDF生成服务
 * 将AI回复内容转换为PDF文档
 */
@Slf4j
@Service
public class PdfGenerationService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 生成PDF文档
     * @param content AI回复内容（支持Markdown格式）
     * @param title 文档标题
     * @return PDF字节数组
     */
    public byte[] generatePdf(String content, String title) throws DocumentException, IOException {
        Document document = new Document(PageSize.A4);
        document.setMargins(50, 50, 50, 50);
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        PdfWriter.getInstance(document, outputStream);
        
        document.open();
        
        try {
            // 设置字体（支持中文）
            BaseFont baseFont = createChineseFont();
            Font titleFont = new Font(baseFont, 18, Font.BOLD);
            Font headerFont = new Font(baseFont, 14, Font.BOLD);
            Font normalFont = new Font(baseFont, 12, Font.NORMAL);
            Font codeFont = new Font(baseFont, 10, Font.NORMAL);
            
            // 添加标题
            if (title != null && !title.trim().isEmpty()) {
                Paragraph titleParagraph = new Paragraph(title, titleFont);
                titleParagraph.setAlignment(Element.ALIGN_CENTER);
                titleParagraph.setSpacingAfter(20);
                document.add(titleParagraph);
            }
            
            // 添加生成时间
            String timestamp = "生成时间: " + LocalDateTime.now().format(DATE_FORMATTER);
            Paragraph timeParagraph = new Paragraph(timestamp, normalFont);
            timeParagraph.setAlignment(Element.ALIGN_RIGHT);
            timeParagraph.setSpacingAfter(20);
            document.add(timeParagraph);
            
            // 添加分隔线
            Paragraph separatorLine = new Paragraph("─".repeat(50), normalFont);
            separatorLine.setAlignment(Element.ALIGN_CENTER);
            separatorLine.setSpacingAfter(10);
            document.add(separatorLine);
            
            // 解析并添加内容
            addFormattedContent(document, content, headerFont, normalFont, codeFont);
            
        } finally {
            document.close();
        }
        
        return outputStream.toByteArray();
    }
    
    /**
     * 创建支持中文的字体
     */
    private BaseFont createChineseFont() throws DocumentException, IOException {
        try {
            // 尝试使用系统字体
            return BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            log.warn("无法加载中文字体，使用默认字体: {}", e.getMessage());
            // 如果中文字体不可用，使用默认字体
            return BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
        }
    }
    
    /**
     * 添加格式化内容到PDF
     */
    private void addFormattedContent(Document document, String content, Font headerFont, Font normalFont, Font codeFont) throws DocumentException {
        String[] lines = content.split("\n");
        boolean inCodeBlock = false;
        StringBuilder codeBlockContent = new StringBuilder();
        
        for (String line : lines) {
            // 处理代码块
            if (line.trim().startsWith("```")) {
                if (inCodeBlock) {
                    // 结束代码块
                    addCodeBlock(document, codeBlockContent.toString(), codeFont);
                    codeBlockContent = new StringBuilder();
                    inCodeBlock = false;
                } else {
                    // 开始代码块
                    inCodeBlock = true;
                }
                continue;
            }
            
            if (inCodeBlock) {
                codeBlockContent.append(line).append("\n");
                continue;
            }
            
            // 处理标题
            if (line.startsWith("# ")) {
                addHeader(document, line.substring(2), headerFont, 16);
            } else if (line.startsWith("## ")) {
                addHeader(document, line.substring(3), headerFont, 14);
            } else if (line.startsWith("### ")) {
                addHeader(document, line.substring(4), headerFont, 12);
            }
            // 处理列表
            else if (line.trim().startsWith("- ") || line.trim().startsWith("* ")) {
                addListItem(document, line.trim().substring(2), normalFont);
            }
            // 处理数字列表
            else if (line.trim().matches("^\\d+\\.\\s+.*")) {
                addNumberedListItem(document, line.trim(), normalFont);
            }
            // 处理行内代码
            else if (line.contains("`")) {
                addFormattedLine(document, line, normalFont, codeFont);
            }
            // 处理空行
            else if (line.trim().isEmpty()) {
                document.add(Chunk.NEWLINE);
            }
            // 普通段落
            else {
                addParagraph(document, line, normalFont);
            }
        }
        
        // 如果最后还有未结束的代码块
        if (inCodeBlock && codeBlockContent.length() > 0) {
            addCodeBlock(document, codeBlockContent.toString(), codeFont);
        }
    }
    
    /**
     * 添加标题
     */
    private void addHeader(Document document, String text, Font font, float size) throws DocumentException {
        Font headerFont = new Font(font.getBaseFont(), size, Font.BOLD);
        Paragraph paragraph = new Paragraph(text, headerFont);
        paragraph.setSpacingBefore(10);
        paragraph.setSpacingAfter(5);
        document.add(paragraph);
    }
    
    /**
     * 添加段落
     */
    private void addParagraph(Document document, String text, Font font) throws DocumentException {
        if (!text.trim().isEmpty()) {
            Paragraph paragraph = new Paragraph(text, font);
            paragraph.setSpacingAfter(5);
            document.add(paragraph);
        }
    }
    
    /**
     * 添加列表项
     */
    private void addListItem(Document document, String text, Font font) throws DocumentException {
        Paragraph paragraph = new Paragraph("• " + text, font);
        paragraph.setIndentationLeft(20);
        paragraph.setSpacingAfter(3);
        document.add(paragraph);
    }
    
    /**
     * 添加数字列表项
     */
    private void addNumberedListItem(Document document, String text, Font font) throws DocumentException {
        Paragraph paragraph = new Paragraph(text, font);
        paragraph.setIndentationLeft(20);
        paragraph.setSpacingAfter(3);
        document.add(paragraph);
    }
    
    /**
     * 添加代码块
     */
    private void addCodeBlock(Document document, String code, Font codeFont) throws DocumentException {
        if (!code.trim().isEmpty()) {
            // 创建带背景色的代码块
            Paragraph codeBlock = new Paragraph(code.trim(), codeFont);
            codeBlock.setIndentationLeft(20);
            codeBlock.setIndentationRight(20);
            codeBlock.setSpacingBefore(10);
            codeBlock.setSpacingAfter(10);
            
            // 添加边框效果（通过缩进模拟）
            document.add(codeBlock);
        }
    }
    
    /**
     * 添加包含行内代码的格式化行
     */
    private void addFormattedLine(Document document, String line, Font normalFont, Font codeFont) throws DocumentException {
        Paragraph paragraph = new Paragraph();
        
        // 使用正则表达式匹配行内代码
        Pattern pattern = Pattern.compile("`([^`]+)`");
        Matcher matcher = pattern.matcher(line);
        
        int lastEnd = 0;
        while (matcher.find()) {
            // 添加代码前的普通文本
            if (matcher.start() > lastEnd) {
                String normalText = line.substring(lastEnd, matcher.start());
                paragraph.add(new Chunk(normalText, normalFont));
            }
            
            // 添加代码部分
            String codeText = matcher.group(1);
            paragraph.add(new Chunk(codeText, codeFont));
            
            lastEnd = matcher.end();
        }
        
        // 添加剩余的普通文本
        if (lastEnd < line.length()) {
            String remainingText = line.substring(lastEnd);
            paragraph.add(new Chunk(remainingText, normalFont));
        }
        
        paragraph.setSpacingAfter(5);
        document.add(paragraph);
    }
}