package com.hujun.aicodehelper.services.moderation;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * 内容审核服务 - 负责输入内容的安全检查
 */
@Service
@Slf4j
public class ContentModerationService {

    @Value("${ai.chat.moderation.enabled:true}")
    private boolean moderationEnabled;

    @Value("${ai.chat.moderation.on-fail:fallback_text}")
    private String moderationOnFail;

    /**
     * 验证输入内容
     */
    public void validateInput(String input) {
        if (!moderationEnabled) {
            return;
        }

        if (input == null || input.trim().isEmpty()) {
            throw new ContentModerationException("输入内容不能为空");
        }

        // 基础安全检查
        if (containsUnsafeContent(input)) {
            handleUnsafeContent(input);
        }

        log.debug("✅ 内容审核通过");
    }

    private boolean containsUnsafeContent(String input) {
        // 简单的关键词过滤，实际项目中应该使用更sophisticated的方法
        String[] unsafeKeywords = { "恶意", "攻击", "病毒" };
        String lowerInput = input.toLowerCase();

        for (String keyword : unsafeKeywords) {
            if (lowerInput.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    private void handleUnsafeContent(String input) {
        log.warn("🚨 检测到不安全内容: {}", input.substring(0, Math.min(50, input.length())));

        if ("error".equals(moderationOnFail)) {
            throw new ContentModerationException("输入内容包含不当信息");
        }

        // fallback_text 模式下记录但不阻止
        log.info("⚠️ 内容审核失败，使用降级策略继续处理");
    }

    public static class ContentModerationException extends RuntimeException {
        public ContentModerationException(String message) {
            super(message);
        }
    }
}
