package com.hujun.aicodehelper.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import dev.langchain4j.model.output.structured.Description;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 结构化输出基础模型
 */
public class StructuredOutput {

    /**
     * 解题步骤结构
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("详细的解题步骤，包含引导思路和关键知识点")
    public static class StepByStepSolution {
        @JsonProperty(required = true)
        @Description("题目识别信息")
        private ProblemIdentification problem;
        
        @JsonProperty(required = true)
        @Description("解题思路引导链，每一步都以问题形式引导学生思考")
        private List<SolutionStep> steps;
        
        @Description("知识点映射和课程标准关联")
        private KnowledgeMapping knowledgePoints;
        
        @Description("常见错误提醒和预防措施")
        private List<CommonMistake> mistakes;
        
        @Description("延伸练习推荐")
        private List<ExtendedExercise> recommendations;
    }

    /**
     * 学习诊断结构
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("学生学习诊断报告，识别知识薄弱点和学习模式")
    public static class LearningDiagnosis {
        @JsonProperty(required = true)
        @Description("学生当前能力评估，包括水平和熟练度")
        private StudentCapability capability;
        
        @Description("知识盲区识别，列出需要补强的概念")
        private List<KnowledgeGap> gaps;
        
        @Description("思维模式分析，了解学生的学习特点")
        private ThinkingPattern pattern;
        
        @Description("个性化学习路径建议")
        private PersonalizedPath learningPath;
    }

    /**
     * 学习进度报告
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("学生学习进度报告，总结成就和下一步建议")
    public static class ProgressReport {
        @Description("当前达到的学习里程碑，例如：完成代数基础")
        private String milestone;
        
        @Description("完成的题目数量")
        private Integer completedProblems;
        
        @Description("已掌握的知识点列表")
        private List<String> masteredConcepts;
        
        @Description("需要继续提升的领域")
        private List<String> improvementAreas;
        
        @Description("下一阶段的学习建议")
        private String recommendations;
    }

    /**
     * 题目识别信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("题目识别和分类信息")
    public static class ProblemIdentification {
        @Description("学科名称，例如：数学、物理")
        private String subject;
        
        @Description("具体主题，例如：二次方程、力学")
        private String topic;
        
        @Description("难度等级：初级、中级、高级")
        private String difficulty;
        
        @Description("题目类型：选择题、填空题、应用题等")
        private String type;
        
        @Description("题目的简要描述")
        private String description;
    }

    /**
     * 解题步骤
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("单个解题步骤，包含引导性问题和思考要点")
    public static class SolutionStep {
        @Description("步骤编号")
        private Integer stepNumber;
        
        @Description("引导性问题，帮助学生自主思考")
        private String guidingQuestion;
        
        @Description("学生在这一步应该思考的内容")
        private String studentThinking;
        
        @Description("关键公式或重要概念")
        private String keyFormula;
        
        @Description("详细的解释说明")
        private String explanation;
        
        @Description("可视化建议，帮助学生理解概念")
        private String visualization;
    }

    /**
     * 知识点映射
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("知识点映射和课程标准关联")
    public static class KnowledgeMapping {
        @Description("主要概念列表")
        private List<String> primaryConcepts;
        
        @Description("前置知识要求")
        private List<String> prerequisites;
        
        @Description("相关知识点")
        private List<String> relatedTopics;
        
        @Description("课程标准映射")
        private String curriculumMapping;
    }

    /**
     * 常见错误
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("常见错误及其预防措施")
    public static class CommonMistake {
        @Description("常见的错误类型")
        private String mistake;
        
        @Description("错误的原因分析")
        private String explanation;
        
        @Description("预防措施和正确做法")
        private String prevention;
    }

    /**
     * 延伸练习
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("延伸练习推荐")
    public static class ExtendedExercise {
        @Description("练习标题")
        private String title;
        
        @Description("练习描述")
        private String description;
        
        @Description("难度等级")
        private String difficulty;
        
        @Description("涉及的技能点")
        private List<String> skills;
    }

    /**
     * 学生能力评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("学生当前能力水平评估")
    public static class StudentCapability {
        @Description("当前水平：入门、基础、中级、高级")
        private String currentLevel;
        
        @Description("熟练度分数，1-100分")
        private Integer proficiencyScore;
        
        @Description("学习强项列表")
        private List<String> strengths;
        
        @Description("需要改进的弱项")
        private List<String> weaknesses;
    }

    /**
     * 知识盲区
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("识别的知识盲区及补救方案")
    public static class KnowledgeGap {
        @Description("涉及的概念名称")
        private String concept;
        
        @Description("知识盲区的详细描述")
        private String gapDescription;
        
        @Description("严重程度，1-5级，5最严重")
        private Integer severityLevel;
        
        @Description("造成这个盲区的根本原因")
        private String rootCause;
        
        @Description("建议的补救步骤")
        private List<String> remedialSteps;
    }

    /**
     * 思维模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("学生的思维模式分析")
    public static class ThinkingPattern {
        @Description("思维模式类型：逻辑型、直觉型、视觉型等")
        private String patternType;
        
        @Description("思维模式的详细描述")
        private String description;
        
        @Description("该思维模式的主要特征")
        private List<String> characteristics;
        
        @Description("针对此思维模式的引导建议")
        private String guidance;
    }

    /**
     * 个性化学习路径
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Description("为学生定制的个性化学习路径")
    public static class PersonalizedPath {
        @Description("推荐的下一步学习主题")
        private List<String> nextTopics;
        
        @Description("适合的练习类型")
        private List<String> practiceTypes;
        
        @Description("推荐的学习策略")
        private String learningStrategy;
        
        @Description("预估学习时间，单位：分钟")
        private Integer estimatedTime;
    }
}
