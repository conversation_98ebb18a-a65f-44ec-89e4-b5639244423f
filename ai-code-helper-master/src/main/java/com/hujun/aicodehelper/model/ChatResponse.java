package com.hujun.aicodehelper.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 聊天响应模型 - 支持流式和结构化混合输出
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChatResponse {
    
    /**
     * 响应类型
     */
    public enum ResponseType {
        STREAM_ONLY,        // 仅流式响应
        STRUCTURED_ONLY,    // 仅结构化响应
        MIXED               // 混合响应
    }
    
    // 基础信息
    private String sessionId;
    private ResponseType type;
    private Long timestamp;
    
    // 流式内容
    private String streamContent;
    
    // 结构化内容
    private Object structuredData;
    private String structuredType;  // SOLUTION, DIAGNOSIS, PROGRESS
    
    // 学习状态信息
    private LearningStatus learningStatus;
    
    // 错误信息
    private String error;
    
    /**
     * 学习状态信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class LearningStatus {
        private Integer roundCount;         // 对话轮次
        private Integer completedProblems;  // 完成题目数
        private Integer consecutiveMistakes; // 连续错误次数
        private String currentTopic;        // 当前主题
        private Integer studyTimeMinutes;   // 学习时长
        private String learningPhase;       // 学习阶段
    }
    
    /**
     * 创建仅流式响应
     */
    public static ChatResponse streamOnly(String sessionId, String content) {
        return ChatResponse.builder()
            .sessionId(sessionId)
            .type(ResponseType.STREAM_ONLY)
            .streamContent(content)
            .timestamp(System.currentTimeMillis())
            .build();
    }
    
    /**
     * 创建仅结构化响应
     */
    public static ChatResponse structuredOnly(String sessionId, Object structuredData, String structuredType) {
        return ChatResponse.builder()
            .sessionId(sessionId)
            .type(ResponseType.STRUCTURED_ONLY)
            .structuredData(structuredData)
            .structuredType(structuredType)
            .timestamp(System.currentTimeMillis())
            .build();
    }
    
    /**
     * 创建混合响应
     */
    public static ChatResponse mixed(String sessionId, String streamContent, 
                                   Object structuredData, String structuredType) {
        return ChatResponse.builder()
            .sessionId(sessionId)
            .type(ResponseType.MIXED)
            .streamContent(streamContent)
            .structuredData(structuredData)
            .structuredType(structuredType)
            .timestamp(System.currentTimeMillis())
            .build();
    }
    
    /**
     * 创建错误响应
     */
    public static ChatResponse error(String sessionId, String error) {
        return ChatResponse.builder()
            .sessionId(sessionId)
            .error(error)
            .timestamp(System.currentTimeMillis())
            .build();
    }
    
    /**
     * 设置学习状态
     */
    public ChatResponse withLearningStatus(LearningStatus status) {
        this.learningStatus = status;
        return this;
    }
    
    /**
     * 检查是否包含结构化数据
     */
    public boolean hasStructuredData() {
        return structuredData != null;
    }
    
    /**
     * 检查是否包含流式内容
     */
    public boolean hasStreamContent() {
        return streamContent != null && !streamContent.trim().isEmpty();
    }
}
