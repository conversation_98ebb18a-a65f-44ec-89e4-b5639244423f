package com.hujun.aicodehelper.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 结构化响应模型 - 用于区分工具调用结果和普通回复
 */
public class StructuredResponse {
    
    /**
     * 响应类型枚举
     */
    public enum ResponseType {
        TOOL_RESULT("tool_result"),
        NORMAL_REPLY("normal_reply");
        
        private final String value;
        
        ResponseType(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
    }
    
    private final ResponseType type;
    private final String content;
    private final String toolName;
    private final String toolInput;
    
    @JsonCreator
    public StructuredResponse(
            @JsonProperty("type") ResponseType type,
            @JsonProperty("content") String content,
            @JsonProperty("toolName") String toolName,
            @JsonProperty("toolInput") String toolInput) {
        this.type = type;
        this.content = content;
        this.toolName = toolName;
        this.toolInput = toolInput;
    }
    
    /**
     * 创建工具调用结果响应
     */
    public static StructuredResponse toolResult(String toolName, String toolInput, String content) {
        return new StructuredResponse(ResponseType.TOOL_RESULT, content, toolName, toolInput);
    }
    
    /**
     * 创建普通回复响应
     */
    public static StructuredResponse normalReply(String content) {
        return new StructuredResponse(ResponseType.NORMAL_REPLY, content, null, null);
    }
    
    // Getters
    public ResponseType getType() {
        return type;
    }
    
    public String getContent() {
        return content;
    }
    
    public String getToolName() {
        return toolName;
    }
    
    public String getToolInput() {
        return toolInput;
    }
    
    /**
     * 转换为JSON字符串格式
     */
    public String toJsonString() {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"type\":\"").append(type.getValue()).append("\"");
        json.append(",\"content\":").append(escapeJsonString(content));
        if (toolName != null) {
            json.append(",\"toolName\":").append(escapeJsonString(toolName));
        }
        if (toolInput != null) {
            json.append(",\"toolInput\":").append(escapeJsonString(toolInput));
        }
        json.append("}");
        return json.toString();
    }
    
    /**
     * JSON字符串转义
     */
    private String escapeJsonString(String input) {
        if (input == null) {
            return "null";
        }
        
        StringBuilder escaped = new StringBuilder("\"");
        for (char c : input.toCharArray()) {
            switch (c) {
                case '"':
                    escaped.append("\\\"");
                    break;
                case '\\':
                    escaped.append("\\\\");
                    break;
                case '\b':
                    escaped.append("\\b");
                    break;
                case '\f':
                    escaped.append("\\f");
                    break;
                case '\n':
                    escaped.append("\\n");
                    break;
                case '\r':
                    escaped.append("\\r");
                    break;
                case '\t':
                    escaped.append("\\t");
                    break;
                default:
                    if (c < ' ') {
                        escaped.append(String.format("\\u%04x", (int) c));
                    } else {
                        escaped.append(c);
                    }
                    break;
            }
        }
        escaped.append("\"");
        return escaped.toString();
    }
    
    /**
     * 检查是否为结构化响应格式
     */
    public static boolean isStructuredResponse(String content) {
        return content != null && 
               content.trim().startsWith("{") && 
               content.contains("\"type\":") && 
               (content.contains("\"tool_result\"") || content.contains("\"normal_reply\""));
    }
    
    /**
     * 从JSON字符串解析响应类型
     */
    public static ResponseType parseResponseType(String jsonContent) {
        if (jsonContent != null && jsonContent.contains("\"tool_result\"")) {
            return ResponseType.TOOL_RESULT;
        } else if (jsonContent != null && jsonContent.contains("\"normal_reply\"")) {
            return ResponseType.NORMAL_REPLY;
        }
        return ResponseType.NORMAL_REPLY; // 默认为普通回复
    }
}
