package com.hujun.aicodehelper.mobileupload;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

@Slf4j
@Service
public class UploadSessionService {
    private static final Duration TOKEN_TTL = Duration.ofMinutes(3);
    private static final long MAX_SIZE_BYTES = 10L * 1024 * 1024; // 10MB
    private static final DateTimeFormatter YYYY = DateTimeFormatter.ofPattern("yyyy");
    private static final DateTimeFormatter MM = DateTimeFormatter.ofPattern("MM");
    private static final DateTimeFormatter DD = DateTimeFormatter.ofPattern("dd");

    private final Map<String, UploadSession> sessionsById = new ConcurrentHashMap<>();
    private final Map<String, UploadSession> sessionsByToken = new ConcurrentHashMap<>();
    private final Map<String, Sinks.Many<UploadEvent>> sinksBySession = new ConcurrentHashMap<>();

    @org.springframework.beans.factory.annotation.Value("${ai.chat.image.public-base-url:}")
    private String publicBaseUrl;

    public static class CreateSessionResult {
        public final String sessionId;
        public final String token;
        public final Instant expiresAt;
        public CreateSessionResult(String sessionId, String token, Instant expiresAt) {
            this.sessionId = sessionId;
            this.token = token;
            this.expiresAt = expiresAt;
        }
    }

    public CreateSessionResult createSession(String memoryId) {
        String sessionId = UUID.randomUUID().toString();
        String token = UUID.randomUUID().toString().replace("-", "");

        UploadSession session = new UploadSession();
        session.setSessionId(sessionId);
        session.setToken(token);
        session.setMemoryId(memoryId);
        session.setStatus(UploadStatus.WAITING);
        session.setCreatedAt(Instant.now());
        session.setExpiresAt(session.getCreatedAt().plus(TOKEN_TTL));

        sessionsById.put(sessionId, session);
        sessionsByToken.put(token, session);

        // create sink and emit initial event
        Sinks.Many<UploadEvent> sink = Sinks.many().multicast().directAllOrNothing();
        sinksBySession.put(sessionId, sink);
        emit(sessionId, UploadEvent.of(sessionId, "waiting"));

        log.info("Created mobile upload session: sessionId={}, token={} (ttl={}s)", sessionId, token, TOKEN_TTL.toSeconds());
        return new CreateSessionResult(sessionId, token, session.getExpiresAt());
    }

    public Flux<UploadEvent> stream(String sessionId) {
        Sinks.Many<UploadEvent> sink = sinksBySession.get(sessionId);
        if (sink == null) {
            // if no sink, create a placeholder that will complete
            Sinks.Many<UploadEvent> newSink = Sinks.many().multicast().directAllOrNothing();
            sinksBySession.put(sessionId, newSink);
            return newSink.asFlux();
        }
        return sink.asFlux();
    }

    public Optional<UploadSession> getBySessionId(String sessionId) {
        return Optional.ofNullable(sessionsById.get(sessionId));
    }

    public Optional<UploadSession> getByToken(String token) {
        return Optional.ofNullable(sessionsByToken.get(token));
    }

    public void markScanned(String token) {
        UploadSession session = sessionsByToken.get(token);
        if (session == null) return;
        if (isExpired(session)) {
            expire(session);
            return;
        }
        if (session.getStatus() == UploadStatus.WAITING) {
            session.setStatus(UploadStatus.SCANNED);
            session.setScannedAt(Instant.now());
            emit(session.getSessionId(), UploadEvent.of(session.getSessionId(), "scanned"));
        }
    }

    public String handleUpload(String token, MultipartFile file) throws IOException {
        UploadSession session = sessionsByToken.get(token);
        if (session == null) {
            throw new IllegalStateException("Invalid or used token");
        }
        if (isExpired(session)) {
            expire(session);
            throw new IllegalStateException("Token expired");
        }
        if (session.getStatus() == UploadStatus.UPLOADED || session.getStatus() == UploadStatus.USED) {
            throw new IllegalStateException("Token already used");
        }
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("Empty file");
        }
        if (file.getSize() > MAX_SIZE_BYTES) {
            throw new IllegalArgumentException("File too large");
        }

        String contentType = file.getContentType();
        if (!isSupportedImage(contentType, file.getOriginalFilename())) {
            throw new IllegalArgumentException("Unsupported image type");
        }

        session.setStatus(UploadStatus.UPLOADING);
        emit(session.getSessionId(), UploadEvent.of(session.getSessionId(), "uploading"));

        // Save file
        String url = saveFileAndReturnUrl(file);

        session.setImageUrl(url);
        session.setStatus(UploadStatus.UPLOADED);
        session.setUsedAt(Instant.now());
        // mark token used
        sessionsByToken.remove(token);

        emit(session.getSessionId(), UploadEvent.uploaded(session.getSessionId(), url));
        return url;
    }

    public void expireOldSessions() {
        Instant now = Instant.now();
        sessionsById.values().forEach(s -> {
            if (s.getStatus() != UploadStatus.UPLOADED && s.getStatus() != UploadStatus.USED && s.getExpiresAt().isBefore(now)) {
                expire(s);
            }
        });
    }

    private void expire(UploadSession s) {
        s.setStatus(UploadStatus.EXPIRED);
        emit(s.getSessionId(), UploadEvent.of(s.getSessionId(), "expired"));
        // cleanup token mapping
        sessionsByToken.remove(s.getToken());
    }

    private void emit(String sessionId, UploadEvent event) {
        Sinks.Many<UploadEvent> sink = sinksBySession.get(sessionId);
        if (sink != null) {
            sink.tryEmitNext(event);
        }
    }

    private boolean isExpired(UploadSession s) {
        return s.getExpiresAt() != null && s.getExpiresAt().isBefore(Instant.now());
    }

    private boolean isSupportedImage(String contentType, String filename) {
        String ext = StringUtils.getFilenameExtension(Objects.toString(filename, ""));
        if (ext != null) ext = ext.toLowerCase();
        if (contentType == null) contentType = "";
        return contentType.startsWith("image/") && ("jpg".equals(ext) || "jpeg".equals(ext) || "png".equals(ext) || "gif".equals(ext) || "webp".equals(ext));
    }

    private String saveFileAndReturnUrl(MultipartFile file) throws IOException {
        Instant now = Instant.now();
        var zdt = now.atZone(ZoneId.systemDefault());
        String yyyy = zdt.format(YYYY);
        String mm = zdt.format(MM);
        String dd = zdt.format(DD);
        // 使用绝对路径，避免被容器工作目录重定向到 /tmp/tomcat.../work 下
        Path baseDirPath = Paths.get(System.getProperty("user.dir"), "data", "uploads", yyyy, mm, dd).toAbsolutePath();
        Files.createDirectories(baseDirPath);

        String ext = Optional.ofNullable(StringUtils.getFilenameExtension(file.getOriginalFilename())).map(String::toLowerCase).orElse("jpg");
        String fileName = UUID.randomUUID().toString().replace("-", "") + "." + ext;
        Path target = baseDirPath.resolve(fileName);
        file.transferTo(target.toFile());

        // Return a controller URL for serving
        return "/api/mobile-upload/file/" + yyyy + "/" + mm + "/" + dd + "/" + fileName;
    }

    /**
     * 将相对URL转换为可公网访问的完整URL（依赖 ai.chat.image.public-base-url）。
     * 若未配置 public-base-url，则返回 null。
     */
    public String toPublicUrl(String relativeUrl) {
        if (relativeUrl == null || relativeUrl.isBlank()) return null;
        if (relativeUrl.startsWith("http://") || relativeUrl.startsWith("https://")) return relativeUrl;
        if (publicBaseUrl == null || publicBaseUrl.isBlank()) return null;
        String base = publicBaseUrl.endsWith("/") ? publicBaseUrl.substring(0, publicBaseUrl.length() - 1) : publicBaseUrl;
        String path = relativeUrl.startsWith("/") ? relativeUrl : ("/" + relativeUrl);
        return base + path;
    }

    public MediaType resolveMediaType(Path path) {
        try {
            String type = Files.probeContentType(path);
            if (type != null) return MediaType.parseMediaType(type);
        } catch (IOException ignored) {}
        // Fallback by extension
        String p = path.toString().toLowerCase();
        if (p.endsWith(".png")) return MediaType.IMAGE_PNG;
        if (p.endsWith(".jpg") || p.endsWith(".jpeg")) return MediaType.IMAGE_JPEG;
        if (p.endsWith(".gif")) return MediaType.IMAGE_GIF;
        if (p.endsWith(".webp")) return MediaType.parseMediaType("image/webp");
        return MediaType.APPLICATION_OCTET_STREAM;
    }
}
