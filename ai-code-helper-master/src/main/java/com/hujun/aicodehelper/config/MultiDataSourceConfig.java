package com.hujun.aicodehelper.config;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import jakarta.persistence.EntityManagerFactory;
import java.util.Map;

/**
 * 单数据源配置类
 * 配置PostgreSQL数据源，用于聊天记忆和向量数据库
 */
@Slf4j
@Configuration
@EnableTransactionManagement
public class MultiDataSourceConfig {

    // ========== PostgreSQL 数据源配置 (统一数据源) ==========

    /**
     * PostgreSQL 数据源属性配置
     */
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.postgresql")
    public DataSourceProperties postgresqlDataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * PostgreSQL 数据源 (统一数据源)
     */
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.postgresql.hikari")
    public DataSource postgresqlDataSource(@Qualifier("postgresqlDataSourceProperties") DataSourceProperties properties) {
        log.info("🗄️ 初始化 PostgreSQL 数据源 (统一数据源)...");
        HikariDataSource dataSource = properties.initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
        log.info("✅ PostgreSQL 数据源初始化完成: {}", properties.getUrl());
        return dataSource;
    }

    /**
     * PostgreSQL JPA 配置 (统一数据源)
     */
    @Bean
    @Primary
    public LocalContainerEntityManagerFactoryBean postgresqlEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("postgresqlDataSource") DataSource dataSource) {
        
        log.info("🏗️ 配置 PostgreSQL EntityManagerFactory (统一数据源)...");
        return builder
                .dataSource(dataSource)
                .packages("com.hujun.aicodehelper.chat.entity", "com.hujun.aicodehelper.ai.entity")
                .persistenceUnit("postgresql")
                .properties(Map.of(
                    "hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect",
                    "hibernate.hbm2ddl.auto", "update",
                    "hibernate.format_sql", "true"
                ))
                .build();
    }

    /**
     * PostgreSQL 事务管理器 (统一数据源)
     */
    @Bean
    @Primary
    public PlatformTransactionManager postgresqlTransactionManager(
            @Qualifier("postgresqlEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}