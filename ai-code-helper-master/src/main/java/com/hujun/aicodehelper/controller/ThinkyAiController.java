package com.hujun.aicodehelper.controller;

import com.hujun.aicodehelper.ai.SmartAiCodeHelperService;
import com.hujun.aicodehelper.ai.trigger.StructuredOutputTrigger;
import com.hujun.aicodehelper.context.ConversationContext;
import com.hujun.aicodehelper.context.ConversationContextManager;
import com.hujun.aicodehelper.model.ChatResponse;
import com.hujun.aicodehelper.services.StructuredResponseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

/**
 * ThinkyAI 控制器 - 支持引导式学习的结构化输出
 */
@Slf4j
@RestController
@RequestMapping("/ai/thinky")
public class ThinkyAiController {

    @Autowired
    private SmartAiCodeHelperService aiCodeHelperService;

    @Autowired
    private StructuredOutputTrigger structuredOutputTrigger;

    @Autowired
    private ConversationContextManager contextManager;

    @Autowired
    private StructuredResponseService structuredResponseService;

    /**
     * ThinkyAI 聊天接口 - 支持流式响应和结构化输出
     */
    @PostMapping("/chat")
    public ResponseEntity<ChatResponse> chat(@RequestBody ThinkyAiChatRequest request) {
        String sessionId = request.getSessionId() != null ? request.getSessionId() : UUID.randomUUID().toString();
        
        log.info("🎓 收到ThinkyAI聊天请求 [会话:{}]: {}", sessionId, 
                request.getMessage().substring(0, Math.min(50, request.getMessage().length())));

        try {
            // 更新学习状态
            if (request.getSubject() != null || request.getProblem() != null) {
                contextManager.updateLearningState(sessionId, request.getSubject(), request.getProblem());
            }

            // 检查并触发结构化输出
            Optional<Object> structuredOutput = structuredOutputTrigger.checkAndTrigger(
                    request.getMessage(), sessionId);

            // 获取流式响应（模拟，实际中应该是异步的）
            String streamContent = generateStreamResponse(request, sessionId);

            // 构建响应
            ChatResponse response;
            if (structuredOutput.isPresent()) {
                // 混合响应
                String structuredType = determineStructuredType(structuredOutput.get());
                response = ChatResponse.mixed(sessionId, streamContent, structuredOutput.get(), structuredType);
                log.info("💡 生成混合响应 [会话:{}] - 类型: {}", sessionId, structuredType);
            } else {
                // 仅流式响应
                response = ChatResponse.streamOnly(sessionId, streamContent);
                log.debug("💬 生成流式响应 [会话:{}]", sessionId);
            }

            // 添加学习状态信息
            ConversationContext context = contextManager.get(sessionId);
            if (context != null) {
                context.updateStudyTime();
                ChatResponse.LearningStatus status = buildLearningStatus(context);
                response.withLearningStatus(status);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("ThinkyAI聊天处理失败 [会话:{}]: {}", sessionId, e.getMessage(), e);
            return ResponseEntity.ok(ChatResponse.error(sessionId, "处理请求失败：" + e.getMessage()));
        }
    }

    /**
     * ThinkyAI 流式聊天接口 - 支持服务器发送事件
     */
    @GetMapping("/chat/stream")
    public Flux<ServerSentEvent<ChatResponse>> chatStream(
            @RequestParam String sessionId,
            @RequestParam String message,
            @RequestParam(required = false) String subject,
            @RequestParam(required = false) String problem,
            @RequestParam(required = false, defaultValue = "zh") String language) {

        log.info("🎓 收到ThinkyAI流式聊天请求 [会话:{}]: {}", sessionId, 
                message.substring(0, Math.min(50, message.length())));

        return Flux.defer(() -> {
            try {
                // 更新学习状态
                if (subject != null || problem != null) {
                    contextManager.updateLearningState(sessionId, subject, problem);
                }

                // 存储结构化输出的引用
                AtomicReference<Object> structuredOutputRef = new AtomicReference<>();
                AtomicReference<String> structuredTypeRef = new AtomicReference<>();

                // 将字符串转换为int ID用于AI服务
                int memoryId = Math.abs(sessionId.hashCode());

                // 获取流式响应
                Flux<String> streamFlux = structuredResponseService.processAiResponse(
                        aiCodeHelperService.chatStreamWithLanguage(memoryId, message, language))
                        .doOnComplete(() -> {
                            // 流式响应完成后，检查结构化输出
                            try {
                                Optional<Object> structuredOutput = structuredOutputTrigger.checkAndTrigger(message, sessionId);
                                if (structuredOutput.isPresent()) {
                                    structuredOutputRef.set(structuredOutput.get());
                                    structuredTypeRef.set(determineStructuredType(structuredOutput.get()));
                                    log.info("💡 检测到结构化输出 [会话:{}] - 类型: {}", sessionId, structuredTypeRef.get());
                                }
                            } catch (Exception e) {
                                log.warn("结构化输出检查失败 [会话:{}]: {}", sessionId, e.getMessage());
                            }
                        });

                // 转换为ChatResponse格式的流
                Flux<ServerSentEvent<ChatResponse>> responseFlux = streamFlux
                        .map(chunk -> {
                            ChatResponse response = ChatResponse.streamOnly(sessionId, chunk);
                            // 添加学习状态
                            ConversationContext context = contextManager.get(sessionId);
                            if (context != null) {
                                response.withLearningStatus(buildLearningStatus(context));
                            }
                            return ServerSentEvent.<ChatResponse>builder()
                                    .data(response)
                                    .build();
                        });

                // 在流的末尾添加结构化输出（如果有的话）
                return responseFlux.concatWith(Flux.defer(() -> {
                    if (structuredOutputRef.get() != null) {
                        ChatResponse structuredResponse = ChatResponse.structuredOnly(
                                sessionId, structuredOutputRef.get(), structuredTypeRef.get());
                        
                        // 添加学习状态
                        ConversationContext context = contextManager.get(sessionId);
                        if (context != null) {
                            structuredResponse.withLearningStatus(buildLearningStatus(context));
                        }

                        return Flux.just(ServerSentEvent.<ChatResponse>builder()
                                .event("structured")
                                .data(structuredResponse)
                                .build());
                    } else {
                        return Flux.empty();
                    }
                }));

            } catch (Exception e) {
                log.error("ThinkyAI流式聊天处理失败 [会话:{}]: {}", sessionId, e.getMessage(), e);
                ChatResponse errorResponse = ChatResponse.error(sessionId, "处理请求失败：" + e.getMessage());
                return Flux.just(ServerSentEvent.<ChatResponse>builder()
                        .data(errorResponse)
                        .build());
            }
        });
    }

    /**
     * 获取会话学习状态
     */
    @GetMapping("/session/{sessionId}/status")
    public ResponseEntity<ChatResponse.LearningStatus> getSessionStatus(@PathVariable String sessionId) {
        ConversationContext context = contextManager.get(sessionId);
        if (context == null) {
            return ResponseEntity.notFound().build();
        }

        context.updateStudyTime();
        ChatResponse.LearningStatus status = buildLearningStatus(context);
        return ResponseEntity.ok(status);
    }

    /**
     * 获取会话统计信息
     */
    @GetMapping("/session/{sessionId}/stats")
    public ResponseEntity<String> getSessionStats(@PathVariable String sessionId) {
        String stats = contextManager.getSessionStats(sessionId);
        return ResponseEntity.ok(stats);
    }

    /**
     * 重置会话状态
     */
    @PostMapping("/session/{sessionId}/reset")
    public ResponseEntity<Void> resetSession(@PathVariable String sessionId) {
        contextManager.resetSession(sessionId);
        log.info("重置ThinkyAI会话状态: {}", sessionId);
        return ResponseEntity.ok().build();
    }

    /**
     * 测试结构化输出触发（调试用）
     */
    @PostMapping("/session/{sessionId}/test-trigger")
    public ResponseEntity<Object> testStructuredTrigger(
            @PathVariable String sessionId,
            @RequestParam(required = false, defaultValue = "解方程 x² - 5x + 6 = 0，我的答案是 x = 2 和 x = 3") String message) {
        
        log.info("🧪 测试结构化输出触发 [会话:{}]: {}", sessionId, message);
        
        try {
            // 手动触发结构化输出检查
            Optional<Object> structuredOutput = structuredOutputTrigger.checkAndTrigger(message, sessionId);
            
            if (structuredOutput.isPresent()) {
                String structuredType = determineStructuredType(structuredOutput.get());
                log.info("✅ 成功触发结构化输出 [会话:{}] - 类型: {}", sessionId, structuredType);
                
                // 构建响应
                ChatResponse response = ChatResponse.structuredOnly(sessionId, structuredOutput.get(), structuredType);
                
                // 添加学习状态
                ConversationContext context = contextManager.get(sessionId);
                if (context != null) {
                    context.updateStudyTime();
                    ChatResponse.LearningStatus status = buildLearningStatus(context);
                    response.withLearningStatus(status);
                }
                
                return ResponseEntity.ok(response);
            } else {
                log.warn("❌ 未触发结构化输出 [会话:{}]", sessionId);
                return ResponseEntity.ok("未触发结构化输出，检查触发条件");
            }
            
        } catch (Exception e) {
            log.error("测试结构化输出触发失败 [会话:{}]: {}", sessionId, e.getMessage(), e);
            return ResponseEntity.status(500).body("测试失败：" + e.getMessage());
        }
    }

    /**
     * 手动记录学习事件
     */
    @PostMapping("/session/{sessionId}/event")
    public ResponseEntity<Void> recordLearningEvent(
            @PathVariable String sessionId,
            @RequestBody LearningEventRequest request) {
        
        switch (request.getEventType().toLowerCase()) {
            case "mistake":
                contextManager.recordMistake(sessionId, request.getDescription());
                break;
            case "success":
                contextManager.recordSuccess(sessionId);
                break;
            case "mastery":
                contextManager.updateKnowledgeMastery(sessionId, 
                        request.getConcept(), request.getScore());
                break;
            default:
                log.warn("未知的学习事件类型: {}", request.getEventType());
                return ResponseEntity.badRequest().build();
        }

        log.debug("记录学习事件 [会话:{}]: {} - {}", sessionId, 
                request.getEventType(), request.getDescription());
        return ResponseEntity.ok().build();
    }

    // ========== 私有辅助方法 ==========

    /**
     * 生成流式响应（同步版本，用于非流式接口）
     */
    private String generateStreamResponse(ThinkyAiChatRequest request, String sessionId) {
        try {
            int memoryId = Math.abs(sessionId.hashCode());
            
            // 收集流式响应
            StringBuilder content = new StringBuilder();
            aiCodeHelperService.chatStreamWithLanguage(memoryId, request.getMessage(), "zh")
                    .doOnNext(chunk -> content.append(chunk))
                    .blockLast(java.time.Duration.ofSeconds(30)); // 30秒超时
            
            return content.toString();
        } catch (Exception e) {
            log.warn("生成流式响应失败 [会话:{}]: {}", sessionId, e.getMessage());
            return "抱歉，我现在无法回答您的问题。请稍后再试。";
        }
    }

    /**
     * 确定结构化输出的类型
     */
    private String determineStructuredType(Object structuredOutput) {
        if (structuredOutput == null) return "UNKNOWN";
        
        String className = structuredOutput.getClass().getSimpleName();
        if (className.contains("StepByStepSolution")) {
            return "SOLUTION";
        } else if (className.contains("LearningDiagnosis")) {
            return "DIAGNOSIS";
        } else if (className.contains("ProgressReport")) {
            return "PROGRESS";
        }
        return "OTHER";
    }

    /**
     * 构建学习状态对象
     */
    private ChatResponse.LearningStatus buildLearningStatus(ConversationContext context) {
        return ChatResponse.LearningStatus.builder()
                .roundCount(context.getRoundCount())
                .completedProblems(context.getCompletedProblems())
                .consecutiveMistakes(context.getConsecutiveMistakes())
                .currentTopic(context.getCurrentTopic())
                .studyTimeMinutes(context.getStudyTimeMinutes())
                .learningPhase(determineLearningPhase(context))
                .build();
    }

    /**
     * 确定学习阶段
     */
    private String determineLearningPhase(ConversationContext context) {
        if (context.getCompletedProblems() == null || context.getCompletedProblems() == 0) {
            return "探索阶段";
        } else if (context.getCompletedProblems() < 5) {
            return "练习阶段";
        } else if (context.getCompletedProblems() < 15) {
            return "提升阶段";
        } else {
            return "精进阶段";
        }
    }

    // ========== 请求/响应模型 ==========

    public static class ThinkyAiChatRequest {
        private String sessionId;
        private String message;
        private String subject;
        private String problem;
        private String studentId;
        private String language = "zh";

        // Getters and setters
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getSubject() { return subject; }
        public void setSubject(String subject) { this.subject = subject; }
        public String getProblem() { return problem; }
        public void setProblem(String problem) { this.problem = problem; }
        public String getStudentId() { return studentId; }
        public void setStudentId(String studentId) { this.studentId = studentId; }
        public String getLanguage() { return language; }
        public void setLanguage(String language) { this.language = language; }
    }

    public static class LearningEventRequest {
        private String eventType; // mistake, success, mastery
        private String description;
        private String concept;
        private Float score;

        // Getters and setters
        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getConcept() { return concept; }
        public void setConcept(String concept) { this.concept = concept; }
        public Float getScore() { return score; }
        public void setScore(Float score) { this.score = score; }
    }
}
