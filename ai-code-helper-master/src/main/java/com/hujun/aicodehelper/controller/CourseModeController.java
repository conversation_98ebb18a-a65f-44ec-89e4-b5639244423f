package com.hujun.aicodehelper.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;
import java.nio.file.Files;

/**
 * 课程模式控制器
 * 提供数学RAG聊天界面的访问
 */
@Slf4j
@Controller
@RequestMapping("/course")
public class CourseModeController {

    /**
     * 获取数学RAG聊天界面
     */
    @GetMapping("/math-rag-chat")
    public ResponseEntity<String> getMathRagChatInterface() {
        try {
            Resource resource = new ClassPathResource("static/math-rag-chat.html");
            String content = new String(Files.readAllBytes(resource.getFile().toPath()));

            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(content);
        } catch (IOException e) {
            log.error("加载数学RAG聊天界面失败", e);
            return ResponseEntity.internalServerError()
                    .contentType(MediaType.TEXT_HTML)
                    .body("<h1>加载失败</h1><p>无法加载数学RAG聊天界面</p>");
        }
    }

    /**
     * 重定向到数学RAG聊天界面
     */
    @GetMapping("")
    public String redirectToMathRagChat() {
        return "redirect:/course/math-rag-chat";
    }

    /**
     * 重定向到默认的数学RAG聊天界面
     */
    @GetMapping("/")
    public String redirectToDefault() {
        return "redirect:/course/math-rag-chat";
    }
}
