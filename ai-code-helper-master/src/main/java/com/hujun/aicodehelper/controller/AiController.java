package com.hujun.aicodehelper.controller;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.hujun.aicodehelper.ai.AiCodeHelperService;
import com.hujun.aicodehelper.ai.RagService;
import com.hujun.aicodehelper.ai.SmartAiCodeHelperService;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType;
import com.hujun.aicodehelper.services.PdfGenerationService;
import com.hujun.aicodehelper.services.StructuredResponseService;
import com.hujun.aicodehelper.services.speech.OpenAiTTSService;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
@RestController
@RequestMapping("/ai")
public class AiController {

    @Resource
    private AiCodeHelperService aiCodeHelperService;

    @Resource
    private RagService ragService;

    @org.springframework.beans.factory.annotation.Value("${speech.provider:azure}")
    private String speechProvider;

    @org.springframework.beans.factory.annotation.Value("${speech.tts.ollama.base-url:http://localhost:11434}")
    private String ollamaBaseUrl;

    @Resource
    private PdfGenerationService pdfGenerationService;

    @Resource
    private StructuredResponseService structuredResponseService;

    @Resource
    private OpenAiTTSService openAiTTSService;

    @GetMapping("/chat")
    public Flux<ServerSentEvent<String>> chat(
            @RequestParam String memoryId,
            @RequestParam String message,
            @RequestParam(required = false, defaultValue = "en") String language) {
        log.info("🚀 收到普通流式聊天请求 [会话:{}]: {}, 语言: {}", memoryId, message.substring(0, Math.min(50, message.length())),
                language);

        // 将字符串memoryId转换为int，如果转换失败则使用hashCode
        int tempMemoryId;
        try {
            tempMemoryId = Integer.parseInt(memoryId);
        } catch (NumberFormatException e) {
            tempMemoryId = Math.abs(memoryId.hashCode());
            log.warn("无法解析memoryId为int: {}, 使用hashCode: {}", memoryId, tempMemoryId);
        }
        final int memoryIdInt = tempMemoryId;

        return structuredResponseService.processAiResponse(
                ((SmartAiCodeHelperService) aiCodeHelperService).chatStreamWithLanguage(memoryIdInt, message, language))
                .doOnSubscribe(subscription -> {
                    log.info("🔄 开始订阅结构化普通流式响应，会话: {}", memoryId);
                })
                .doOnNext(structuredChunk -> {
                    log.debug("📤 结构化普通流式数据块，会话: {}, 长度: {}", memoryId,
                            structuredChunk != null ? structuredChunk.length() : 0);
                })
                .map(structuredChunk -> ServerSentEvent.<String>builder()
                        .data(structuredChunk)
                        .build())
                .doOnComplete(() -> {
                    log.info("✅ 结构化普通流式聊天完成，会话: {}", memoryIdInt);
                })
                .doOnError(error -> {
                    log.error("❌ 结构化普通流式聊天出错，会话: {}, 错误: {}", memoryIdInt, error.getMessage(), error);
                });
    }

    /**
     * 多模态聊天接口 - 支持文本和图片输入的流式响应
     */
    @PostMapping(value = "/chat/multimodal", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Flux<ServerSentEvent<String>> multimodalChat(
            @RequestParam String memoryId,
            @RequestParam String message,
            @RequestParam(required = false) MultipartFile image,
            @RequestParam(name = "imageUrl", required = false) String imageUrlParam,
            @RequestParam(required = false, defaultValue = "en") String language) {

        log.info("🚀 收到多模态流式聊天请求 [会话:{}]: {}, 图片: {}, 图片URL: {}, 语言: {}",
                memoryId,
                message.substring(0, Math.min(50, message.length())),
                image != null ? image.getOriginalFilename() : "无",
                imageUrlParam,
                language);

        try {
            // 验证输入参数
            if (message == null || message.trim().isEmpty()) {
                log.warn("⚠️ 多模态聊天消息为空");
                return Flux.just(ServerSentEvent.<String>builder()
                        .data("消息内容不能为空")
                        .build());
            }

            // 将字符串memoryId转换为int，如果转换失败则使用hashCode
            int tempId;
            try {
                tempId = Integer.parseInt(memoryId);
            } catch (NumberFormatException e) {
                tempId = Math.abs(memoryId.hashCode());
                log.warn("无法解析memoryId为int: {}, 使用hashCode: {}", memoryId, tempId);
            }
            final int memoryIdInt = tempId;

            // 如果有图片，处理图片
            String imageUrl = null;
            // 优先使用传入的服务器侧图片URL（例如手机扫码上传得到的）
            if (imageUrlParam != null && !imageUrlParam.trim().isEmpty()) {
                imageUrl = imageUrlParam.trim();
            } else if (image != null && !image.isEmpty()) {
                try {
                    imageUrl = processUploadedImage(image);
                    log.info("📷 图片处理完成: {}", imageUrl);
                } catch (Exception e) {
                    log.error("❌ 图片处理失败: {}", e.getMessage(), e);
                    return Flux.just(ServerSentEvent.<String>builder()
                            .data("图片处理失败: " + e.getMessage())
                            .build());
                }
            }

            // 调用AI服务处理多模态输入，使用结构化响应服务
            return structuredResponseService.processAiResponse(
                    ((SmartAiCodeHelperService) aiCodeHelperService).chatStreamWithImageAndLanguage(memoryIdInt,
                            message.trim(), imageUrl, language))
                    .doOnSubscribe(subscription -> {
                        log.info("🔄 开始订阅结构化多模态流式响应，会话: {}", memoryIdInt);
                    })
                    .doOnNext(structuredChunk -> {
                        log.debug("📤 结构化多模态流式数据块，会话: {}, 长度: {}", memoryIdInt,
                                structuredChunk != null ? structuredChunk.length() : 0);
                    })
                    .map(structuredChunk -> ServerSentEvent.<String>builder()
                            .data(structuredChunk)
                            .build())
                    .concatWith(Flux.just(ServerSentEvent.<String>builder()
                            .event("end")
                            .data("[DONE]")
                            .build()))
                    .doOnComplete(() -> {
                        log.info("✅ 多模态流式聊天完成，会话: {}", memoryIdInt);
                    })
                    .doOnError(error -> {
                        log.error("❌ 多模态流式聊天出错，会话: {}, 错误: {}", memoryIdInt, error.getMessage(), error);
                    });

        } catch (Exception e) {
            log.error("多模态聊天参数处理错误: {}", e.getMessage());
            return Flux.just(ServerSentEvent.<String>builder()
                    .data("参数处理错误: " + e.getMessage())
                    .build());
        }
    }

    /**
     * 测试流式响应的简单端点
     */
    @GetMapping("/test-stream")
    public Flux<ServerSentEvent<String>> testStream() {
        log.info("🧪 测试流式响应端点被调用");
        return Flux.just("第一段", "第二段", "第三段", "结束")
                .delayElements(java.time.Duration.ofMillis(500))
                .doOnNext(chunk -> {
                    log.info("🧪 发送测试数据: {}", chunk);
                })
                .map(chunk -> ServerSentEvent.<String>builder()
                        .data(chunk)
                        .build())
                .concatWith(Flux.just(ServerSentEvent.<String>builder()
                        .event("end")
                        .data("[DONE]")
                        .build()))
                .doOnComplete(() -> {
                    log.info("🧪 测试流式响应完成");
                });
    }

    /**
     * RAG增强问答接口 - 同步版本
     * 基于知识库进行问答，返回答案和相关文档来源
     */
    @PostMapping("/rag/chat")
    public Map<String, Object> ragChat(@RequestBody RagChatRequest request) {
        RagService.RagChatResult result = ragService.chatWithRag(request.getMessage());

        Map<String, Object> response = new HashMap<>();
        response.put("answer", result.getAnswer());
        response.put("sources", result.getSources());
        response.put("success", true);

        return response;
    }

    /**
     * RAG增强问答接口 - 流式版本
     * 基于知识库进行问答，实时返回流式响应
     */
    @GetMapping("/rag/chat-stream")
    public Flux<ServerSentEvent<String>> ragChatStream(
            @RequestParam String memoryId,
            @RequestParam String message,
            @RequestParam(required = false) String subject,
            @RequestParam(required = false) String grade,
            @RequestParam(required = false, defaultValue = "en") String language) {
        log.info("🚀 收到RAG流式聊天请求 [会话:{}]: {}, 年级: {}, 科目: {}, 语言: {}",
                memoryId, message.substring(0, Math.min(50, message.length())), grade, subject, language);

        try {
            // 验证输入参数
            if (message == null || message.trim().isEmpty()) {
                log.warn("⚠️ RAG流式聊天消息为空");
                return Flux.just(ServerSentEvent.<String>builder()
                        .data("{\"error\": \"消息内容不能为空\"}")
                        .build());
            }

            // 将字符串memoryId转换为int，如果转换失败则使用默认值
            int tempMemoryId;
            try {
                tempMemoryId = Integer.parseInt(memoryId);
            } catch (NumberFormatException e) {
                // 如果无法解析为int，使用hashCode作为替代
                tempMemoryId = Math.abs(memoryId.hashCode());
                log.warn("无法解析memoryId为int: {}, 使用hashCode: {}", memoryId, tempMemoryId);
            }
            final int memoryIdInt = tempMemoryId;

            log.info("📝 开始处理RAG流式聊天 [会话:{}]", memoryIdInt);

            // 构建包含年级和科目信息的增强消息
            String enhancedMessage = buildEnhancedMessage(message.trim(), grade, subject);
            log.info("📝 增强消息内容: {}", enhancedMessage.substring(0, Math.min(100, enhancedMessage.length())));

            // 先进行RAG检索，获取相关知识
            String ragEnhancedMessage;
            try {
                log.info("🔍 开始RAG知识库检索...");
                List<com.hujun.aicodehelper.ai.RagService.RagSearchResult> ragResults = ragService
                        .searchKnowledge(enhancedMessage, 5, 0.7);

                // 构建包含RAG上下文的增强消息
                ragEnhancedMessage = buildRagEnhancedMessage(enhancedMessage, ragResults);
                log.info("📚 RAG增强后消息长度: {} 字符", ragEnhancedMessage.length());

            } catch (Exception e) {
                log.warn("⚠️ RAG检索失败，使用原始消息进行普通聊天: {}", e.getMessage());
                log.debug("RAG检索详细错误: ", e);
                // RAG失败时，仍然可以使用普通聊天，但会显示提示
                ragEnhancedMessage = "[注意：知识库检索暂时不可用，将使用普通聊天模式]\n\n" + enhancedMessage;
            }

            // 先基于原始用户消息选择最合适的模型（包括数学工具模型）
            ModelType selectedModelType = ((SmartAiCodeHelperService) aiCodeHelperService)
                    .optimizeModelSelection(message.trim(), new ArrayList<>(), memoryIdInt);
            log.info("🎯 RAG接口为消息 [{}] 选择模型: {}",
                    message.substring(0, Math.min(30, message.length())), selectedModelType.getDescription());

            // 获取选择的模型服务
            AiCodeHelperService selectedService = ((SmartAiCodeHelperService) aiCodeHelperService)
                    .getDynamicAiServiceManager().getAiServiceByModelType(selectedModelType);

            // 使用选择的模型处理RAG增强的消息，并通过结构化响应服务处理
            return structuredResponseService.processAiResponse(
                    ((SmartAiCodeHelperService) selectedService).chatStreamWithLanguage(memoryIdInt, ragEnhancedMessage,
                            language))
                    .doOnSubscribe(subscription -> {
                        log.info("🔄 开始订阅结构化流式响应，会话: {}", memoryIdInt);
                    })
                    .doOnNext(structuredChunk -> {
                        log.info("📤 收到结构化数据块，会话: {}, 长度: {}, 内容: [{}]",
                                memoryIdInt,
                                structuredChunk != null ? structuredChunk.length() : 0,
                                structuredChunk != null
                                        ? structuredChunk.substring(0, Math.min(100, structuredChunk.length()))
                                        : "null");
                    })
                    .filter(chunk -> {
                        boolean isValid = chunk != null && !chunk.trim().isEmpty();
                        if (!isValid) {
                            log.warn("⚠️ 过滤空结构化内容块，会话: {}, 原始内容: [{}]", memoryIdInt, chunk);
                        }
                        return isValid;
                    })
                    .map(structuredChunk -> {
                        log.info("🔄 转换结构化数据块为SSE，会话: {}, 内容: [{}]", memoryIdInt,
                                structuredChunk.substring(0, Math.min(100, structuredChunk.length())));
                        return ServerSentEvent.<String>builder()
                                .data(structuredChunk)
                                .build();
                    })
                    .concatWith(Flux.just(ServerSentEvent.<String>builder()
                            .event("end")
                            .data("[DONE]")
                            .build())
                            .doOnNext(endEvent -> {
                                log.info("🏁 发送流结束标识，会话: {}", memoryIdInt);
                            }))
                    .doOnComplete(() -> {
                        log.info("✅ RAG流式聊天完成，会话: {}", memoryIdInt);
                    })
                    .doOnError(error -> {
                        log.error("❌ RAG流式聊天出错，会话: {}, 错误: {}", memoryIdInt, error.getMessage(), error);
                    })
                    .onErrorResume(throwable -> {
                        log.error("🚨 RAG流式聊天发生错误: {}", throwable.getMessage(), throwable);
                        return Flux.just(ServerSentEvent.<String>builder()
                                .data("{\"error\": \"处理请求时发生错误，请稍后重试\"}")
                                .build());
                    });
        } catch (Exception e) {
            log.error("RAG流式聊天参数处理错误: {}", e.getMessage());
            return Flux.just(ServerSentEvent.<String>builder()
                    .data("{\"error\": \"参数处理错误\"}")
                    .build());
        }
    }

    /**
     * 知识库搜索接口
     * 直接搜索知识库中的相关内容，不进行对话生成
     */
    @PostMapping("/rag/search")
    public Map<String, Object> searchKnowledge(@RequestBody KnowledgeSearchRequest request) {
        List<RagService.RagSearchResult> results = ragService.searchKnowledge(
                request.getQuery(),
                request.getMaxResults(),
                request.getMinScore());

        Map<String, Object> response = new HashMap<>();
        response.put("results", results);
        response.put("success", true);
        response.put("query", request.getQuery());
        response.put("total", results.size());

        return response;
    }

    /**
     * 获取知识库统计信息
     */
    @GetMapping("/rag/stats")
    public RagService.KnowledgeStats getKnowledgeStats() {
        return ragService.getKnowledgeStats();
    }

    /**
     * 将AI生成的内容转换为语音
     * 适用于AI通过RAG生成的文章内容
     */
    @PostMapping("/speech/synthesize")
    public Map<String, Object> synthesizeSpeech(@RequestBody SpeechSynthesisRequest request) {
        return null;
    }

    /**
     * 使用OpenAI兼容TTS进行语音合成
     */
    @PostMapping("/openai/tts")
    public ResponseEntity<?> openAiTTSSynthesize(@RequestBody OpenAiTTSRequest request) {
        try {
            log.info("收到OpenAI TTS合成请求，文本长度: {}, 模型: {}, 语音: {}",
                    request.getInput() != null ? request.getInput().length() : 0,
                    request.getModel(), request.getVoice());

            // 验证输入参数
            if (request.getInput() == null || request.getInput().trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "输入文本不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            // 调用OpenAI TTS服务
            OpenAiTTSService.TTSResult result = openAiTTSService.synthesize(
                    request.getModel() != null ? request.getModel() : "tts-1",
                    request.getInput().trim(),
                    request.getVoice() != null ? request.getVoice() : "alloy",
                    request.getSpeed() != null ? request.getSpeed() : 1.0,
                    request.getResponseFormat() != null ? request.getResponseFormat() : "mp3");

            if (result.isSuccess()) {
                // 返回音频文件
                String contentType = result.getContentType() != null ? result.getContentType() : "audio/mpeg";
                log.info("✅ OpenAI TTS合成成功，音频数据大小: {} bytes", result.getAudioData().length);

                return ResponseEntity.ok()
                        .header("Content-Type", contentType)
                        .body(result.getAudioData());
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", result.getErrorMessage());
                log.error("❌ OpenAI TTS合成失败: {}", result.getErrorMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            log.error("OpenAI TTS合成异常: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "TTS转换异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 使用OpenAI兼容TTS进行SSE流式语音合成
     */
    @PostMapping(value = "/openai/tts/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> openAiTTSStream(@RequestBody OpenAiTTSRequest request) {
        String model = request.getModel() != null ? request.getModel() : "tts-1";
        String voice = request.getVoice() != null ? request.getVoice() : "alloy";
        double speed = request.getSpeed() != null ? request.getSpeed() : 1.0;
        String format = request.getResponseFormat() != null ? request.getResponseFormat() : "mp3";
        String streamFormat = request.getStreamFormat() != null ? request.getStreamFormat() : "sse";

        log.info("收到OpenAI TTS流式请求，文本长度: {}, 模型: {}, 语音: {}, 流式格式: {}",
                request.getInput() != null ? request.getInput().length() : 0, model, voice, streamFormat);

        if (request.getInput() == null || request.getInput().trim().isEmpty()) {
            return Flux.just(ServerSentEvent.<String>builder()
                    .event("error")
                    .data("输入文本不能为空")
                    .build());
        }

        return openAiTTSService.synthesizeStream(model, request.getInput().trim(), voice, speed, format, streamFormat)
                .onErrorResume(ex -> Flux.just(ServerSentEvent.<String>builder()
                        .event("error")
                        .data("TTS流式转换异常: " + ex.getMessage())
                        .build()));
    }

    /**
     * 测试OpenAI TTS连接
     */
    @GetMapping("/openai/tts/test")
    public Map<String, Object> testOpenAiTTSConnection() {
        try {
            boolean isConnected = openAiTTSService.testConnection();

            Map<String, Object> response = new HashMap<>();
            response.put("success", isConnected);
            response.put("message", isConnected ? "OpenAI TTS连接正常" : "OpenAI TTS连接失败");
            response.put("timestamp", System.currentTimeMillis());

            return response;

        } catch (Exception e) {
            log.error("OpenAI TTS连接测试异常: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "连接测试异常: " + e.getMessage());
            return response;
        }
    }

    /**
     * 获取OpenAI TTS服务状态
     */
    @GetMapping("/openai/tts/status")
    public Map<String, Object> getOpenAiTTSStatus() {
        try {
            OpenAiTTSService.ServiceStatus status = openAiTTSService.getServiceStatus();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("status", status);

            return response;

        } catch (Exception e) {
            log.error("获取OpenAI TTS状态异常: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取状态失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 支持自定义语音的语音合成接口
     */
    // @PostMapping("/speech/synthesize-with-voice")
    // public Map<String, Object> synthesizeSpeechWithVoice(@RequestBody
    // SpeechSynthesisWithVoiceRequest request) {
    // try {
    // log.info("收到自定义语音合成请求，文本长度: {}, 语音: {}",
    // request.getText() != null ? request.getText().length() : 0,
    // request.getVoiceName());

    // // 使用自定义语音的语音合成方法
    // SpeechSynthesisService.SpeechSynthesisResult result =
    // speechSynthesisService.synthesizeArticleWithVoice(
    // request.getText(),
    // request.getTitle(),
    // request.getVoiceName());

    // Map<String, Object> response = new HashMap<>();
    // response.put("success", result.isSuccess());
    // response.put("message", result.getMessage());

    // if (result.isSuccess()) {
    // response.put("filePath", result.getFilePath());
    // response.put("fileName", result.getFileName());
    // response.put("fileSize", result.getFileSize());
    // }

    // return response;

    // } catch (Exception e) {
    // log.error("自定义语音合成异常: {}", e.getMessage(), e);
    // Map<String, Object> response = new HashMap<>();
    // response.put("success", false);
    // response.put("message", "语音合成异常: " + e.getMessage());
    // return response;
    // }
    // }

    /**
     * 获取支持的语音列表
     */
    @GetMapping("/speech/voices")
    public Map<String, Object> getSupportedVoices() {
        Map<String, Object> response = new HashMap<>();
        Map<String, String> voices = new HashMap<>();

        voices.put("zh-CN-XiaoxiaoNeural", "中文女声（晓晓）");
        voices.put("zh-CN-YunxiNeural", "中文男声（云希）");
        voices.put("zh-CN-YunyangNeural", "中文男声（云扬）");
        voices.put("en-US-AriaNeural", "英文女声（Aria）");
        voices.put("en-US-JennyNeural", "英文女声（Jenny）");
        voices.put("en-US-GuyNeural", "英文男声（Guy）");

        response.put("success", true);
        response.put("voices", voices);
        response.put("defaultVoice", "zh-CN-XiaoxiaoNeural");

        return response;
    }

    /**
     * 下载语音文件
     */
    @GetMapping("/speech/download")
    public ResponseEntity<FileSystemResource> downloadSpeechFile(@RequestParam String fileName) {
        try {
            // 验证文件名安全性
            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                return ResponseEntity.badRequest().build();
            }

            // 构建文件路径
            Path filePath = Paths.get("./data/speech-output", fileName);
            File file = filePath.toFile();

            if (!file.exists() || !file.isFile()) {
                return ResponseEntity.notFound().build();
            }

            FileSystemResource resource = new FileSystemResource(file);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "audio/wav")
                    .contentLength(file.length())
                    .body(resource);

        } catch (Exception e) {
            log.error("下载语音文件异常: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取语音文件列表
     */
    @GetMapping("/speech/files")
    public Map<String, Object> getSpeechFiles() {
        try {
            Path speechDir = Paths.get("./data/speech-output");
            List<Map<String, Object>> files = new ArrayList<>();

            if (Files.exists(speechDir)) {
                Files.list(speechDir)
                        .filter(path -> path.toString().endsWith(".wav"))
                        .forEach(path -> {
                            File file = path.toFile();
                            Map<String, Object> fileInfo = new HashMap<>();
                            fileInfo.put("name", file.getName());
                            fileInfo.put("size", file.length());
                            fileInfo.put("lastModified", file.lastModified());
                            files.add(fileInfo);
                        });
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("files", files);
            response.put("total", files.size());

            return response;

        } catch (IOException e) {
            log.error("获取语音文件列表异常: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取文件列表失败");
            return response;
        }
    }

    /**
     * RAG聊天请求体
     */
    public static class RagChatRequest {
        private String message;
        private String context; // 可选的上下文信息

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getContext() {
            return context;
        }

        public void setContext(String context) {
            this.context = context;
        }
    }

    /**
     * 知识库搜索请求体
     */
    public static class KnowledgeSearchRequest {
        private String query;
        private int maxResults = 5; // 最大返回结果数
        private double minScore = 0.7; // 最小相似度分数
        private String grade; // 年级过滤器
        private String topic; // 主题过滤器

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public int getMaxResults() {
            return maxResults;
        }

        public void setMaxResults(int maxResults) {
            this.maxResults = maxResults;
        }

        public double getMinScore() {
            return minScore;
        }

        public void setMinScore(double minScore) {
            this.minScore = minScore;
        }

        public String getGrade() {
            return grade;
        }

        public void setGrade(String grade) {
            this.grade = grade;
        }

        public String getTopic() {
            return topic;
        }

        public void setTopic(String topic) {
            this.topic = topic;
        }
    }

    /**
     * 语音合成请求体
     */
    public static class SpeechSynthesisRequest {
        private String text;
        private String title;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    }

    /**
     * PDF导出接口
     * 将AI回复内容生成为PDF文档供下载
     */
    @PostMapping("/export/pdf")
    public ResponseEntity<byte[]> exportToPdf(@RequestBody PdfExportRequest request) {
        try {
            log.info("收到PDF导出请求，标题: {}, 内容长度: {}",
                    request.getTitle(),
                    request.getContent() != null ? request.getContent().length() : 0);

            // 验证输入参数
            if (request.getContent() == null || request.getContent().trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            // 生成PDF
            String title = request.getTitle() != null && !request.getTitle().trim().isEmpty()
                    ? request.getTitle()
                    : "AI回复内容";

            byte[] pdfBytes = pdfGenerationService.generatePdf(request.getContent(), title);

            // 构建文件名
            String fileName = title.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_") + ".pdf";

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentType(org.springframework.http.MediaType.APPLICATION_PDF);
            headers.setContentLength(pdfBytes.length);

            log.info("PDF生成成功，文件大小: {} bytes", pdfBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("PDF生成失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * PDF导出请求体
     */
    public static class PdfExportRequest {
        private String content;
        private String title;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    }

    /**
     * 自定义语音合成请求体
     */
    public static class SpeechSynthesisWithVoiceRequest {
        private String text;
        private String title;
        private String voiceName;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getVoiceName() {
            return voiceName;
        }

        public void setVoiceName(String voiceName) {
            this.voiceName = voiceName;
        }
    }

    /**
     * Genny TTS请求体
     */
    public static class GennyTTSRequest {
        private String text;
        private String speakerId;
        private Double speed;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getSpeakerId() {
            return speakerId;
        }

        public void setSpeakerId(String speakerId) {
            this.speakerId = speakerId;
        }

        public Double getSpeed() {
            return speed;
        }

        public void setSpeed(Double speed) {
            this.speed = speed;
        }
    }

    /**
     * OpenAI TTS请求体
     */
    public static class OpenAiTTSRequest {
        private String model;
        private String input;
        private String voice;
        private Double speed;
        private String responseFormat;
        private Boolean stream;
        private String streamFormat; // e.g., "sse"

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public String getInput() {
            return input;
        }

        public void setInput(String input) {
            this.input = input;
        }

        public String getVoice() {
            return voice;
        }

        public void setVoice(String voice) {
            this.voice = voice;
        }

        public Double getSpeed() {
            return speed;
        }

        public void setSpeed(Double speed) {
            this.speed = speed;
        }

        public String getResponseFormat() {
            return responseFormat;
        }

        public void setResponseFormat(String responseFormat) {
            this.responseFormat = responseFormat;
        }

        public Boolean getStream() {
            return stream;
        }

        public void setStream(Boolean stream) {
            this.stream = stream;
        }

        public String getStreamFormat() {
            return streamFormat;
        }

        public void setStreamFormat(String streamFormat) {
            this.streamFormat = streamFormat;
        }
    }

    /**
     * 处理上传的图片文件
     * 参考移动端上传的实现
     */
    private String processUploadedImage(MultipartFile file) throws IOException {
        // 文件大小限制 (10MB)
        final long MAX_SIZE_BYTES = 10L * 1024 * 1024;
        if (file.getSize() > MAX_SIZE_BYTES) {
            throw new IllegalArgumentException("图片文件过大，最大支持10MB");
        }

        // 验证文件类型
        String contentType = file.getContentType();
        String filename = file.getOriginalFilename();
        if (!isSupportedImageType(contentType, filename)) {
            throw new IllegalArgumentException("不支持的图片格式，请上传 JPG、PNG、GIF 或 WebP 格式的图片");
        }

        // 保存文件
        return saveImageFile(file);
    }

    /**
     * 检查是否为支持的图片类型
     */
    private boolean isSupportedImageType(String contentType, String filename) {
        if (contentType == null)
            contentType = "";

        String ext = null;
        if (filename != null) {
            int lastDot = filename.lastIndexOf('.');
            if (lastDot > 0) {
                ext = filename.substring(lastDot + 1).toLowerCase();
            }
        }

        return contentType.startsWith("image/") &&
                (ext != null && ("jpg".equals(ext) || "jpeg".equals(ext) || "png".equals(ext) ||
                        "gif".equals(ext) || "webp".equals(ext)));
    }

    /**
     * 保存图片文件并返回访问URL
     */
    private String saveImageFile(MultipartFile file) throws IOException {
        // 使用时间戳创建目录结构
        java.time.Instant now = java.time.Instant.now();
        java.time.ZonedDateTime zdt = now.atZone(java.time.ZoneId.systemDefault());

        String yyyy = String.format("%04d", zdt.getYear());
        String mm = String.format("%02d", zdt.getMonthValue());
        String dd = String.format("%02d", zdt.getDayOfMonth());

        // 统一使用项目根目录作为基准，避免在容器下相对路径解析到临时工作目录
        Path baseDirPath = Paths.get(System.getProperty("user.dir"), "data", "uploads", yyyy, mm, dd).toAbsolutePath();
        Files.createDirectories(baseDirPath);

        // 生成唯一文件名
        String ext = "jpg"; // 默认扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename != null) {
            int lastDot = originalFilename.lastIndexOf('.');
            if (lastDot > 0) {
                ext = originalFilename.substring(lastDot + 1).toLowerCase();
            }
        }

        String fileName = java.util.UUID.randomUUID().toString().replace("-", "") + "." + ext;
        Path target = baseDirPath.resolve(fileName);

        // 保存文件
        file.transferTo(target.toFile());

        // 记录调试信息：尺寸与大小，便于排查“是否被压缩”
        try {
            long sizeBytes = Files.size(target);
            int w = -1, h = -1;
            try {
                java.awt.image.BufferedImage img = javax.imageio.ImageIO.read(target.toFile());
                if (img != null) {
                    w = img.getWidth();
                    h = img.getHeight();
                }
            } catch (Throwable ignore) {
            }
            log.info("📸 已保存上传图片: path={}, size={} KB, dimension={}x{}", target, sizeBytes / 1024, w, h);
        } catch (Throwable logErr) {
            log.debug("记录图片尺寸失败: {}", logErr.getMessage());
        }

        // 返回可访问的URL
        return "/api/mobile-upload/file/" + yyyy + "/" + mm + "/" + dd + "/" + fileName;
    }

    /**
     * 构建包含年级和科目信息的增强消息
     * 
     * @param originalMessage 原始消息
     * @param grade           年级
     * @param subject         科目
     * @return 增强后的消息
     */
    private String buildEnhancedMessage(String originalMessage, String grade, String subject) {
        if ((grade == null || grade.trim().isEmpty()) && (subject == null || subject.trim().isEmpty())) {
            return originalMessage;
        }

        StringBuilder enhanced = new StringBuilder();

        // 添加年级和科目信息到消息开头
        if (grade != null && !grade.trim().isEmpty() && subject != null && !subject.trim().isEmpty()) {
            enhanced.append("[").append(grade.trim()).append(" ").append(subject.trim()).append("] ");
        } else if (grade != null && !grade.trim().isEmpty()) {
            enhanced.append("[年级: ").append(grade.trim()).append("] ");
        } else if (subject != null && !subject.trim().isEmpty()) {
            enhanced.append("[科目: ").append(subject.trim()).append("] ");
        }

        enhanced.append(originalMessage);

        return enhanced.toString();
    }

    /**
     * 构建包含RAG检索结果的增强消息
     * 
     * @param originalMessage 原始消息
     * @param ragResults      RAG检索结果
     * @return RAG增强后的消息
     */
    private String buildRagEnhancedMessage(String originalMessage,
            List<com.hujun.aicodehelper.ai.RagService.RagSearchResult> ragResults) {
        if (ragResults == null || ragResults.isEmpty()) {
            log.warn("⚠️ 没有找到相关的知识库内容，使用原始消息");
            return originalMessage;
        }

        StringBuilder enhanced = new StringBuilder();

        // 添加RAG检索到的相关知识作为上下文
        enhanced.append("基于以下相关知识回答问题：\n\n");

        for (int i = 0; i < ragResults.size(); i++) {
            com.hujun.aicodehelper.ai.RagService.RagSearchResult result = ragResults.get(i);
            enhanced.append("【相关知识 ").append(i + 1).append("】\n");
            enhanced.append(result.getText()).append("\n\n");

            log.info("📖 使用知识源 {}: {} (相似度: {:.2f})",
                    i + 1, result.getFileName(), result.getScore());
        }

        enhanced.append("现在请根据上述知识回答以下问题：\n");
        enhanced.append(originalMessage);

        return enhanced.toString();
    }

}
