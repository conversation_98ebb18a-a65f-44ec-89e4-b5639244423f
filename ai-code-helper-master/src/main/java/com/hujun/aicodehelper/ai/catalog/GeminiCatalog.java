package com.hujun.aicodehelper.ai.catalog;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.hujun.aicodehelper.ai.model.MultiGeminiModelConfig;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;

/**
 * Provider catalog for Google Gemini family.
 * Maps higher-level UseCase to concrete Gemini ModelType.
 */
@Component
public class GeminiCatalog implements ModelCatalog {

    private final MultiGeminiModelConfig geminiConfig;
    private final Map<UseCase, ModelType> mapping = new EnumMap<>(UseCase.class);

    public GeminiCatalog(
            MultiGeminiModelConfig geminiConfig,
            @Value("${ai.catalog.gemini.text:lite}") String textSlot,
            @Value("${ai.catalog.gemini.rag:rag}") String ragSlot,
            @Value("${ai.catalog.gemini.dictionary:dictionary}") String dictionarySlot,
            @Value("${ai.catalog.gemini.pro:pro}") String proSlot
    ) {
        this.geminiConfig = geminiConfig;
        mapping.put(UseCase.TEXT, ModelType.valueOf(textSlot.toUpperCase()));
        mapping.put(UseCase.MULTIMODAL, ModelType.valueOf(dictionarySlot.toUpperCase()));
        mapping.put(UseCase.RAG, ModelType.valueOf(ragSlot.toUpperCase()));
        mapping.put(UseCase.DICTIONARY, ModelType.valueOf(dictionarySlot.toUpperCase()));
        mapping.put(UseCase.PRO, ModelType.valueOf(proSlot.toUpperCase()));
    }

    @Override
    public Optional<StreamingChatModel> getStreaming(UseCase useCase) {
        ModelType type = mapping.getOrDefault(useCase, ModelType.LITE);
        return Optional.ofNullable(geminiConfig.getStreamingChatModel(type));
    }

    @Override
    public Optional<ChatModel> getChat(UseCase useCase) {
        ModelType type = mapping.getOrDefault(useCase, ModelType.LITE);
        return Optional.ofNullable(geminiConfig.getChatModel(type));
    }

    @Override
    public String getEffectiveModelName(UseCase useCase) {
        ModelType type = mapping.getOrDefault(useCase, ModelType.LITE);
        var cfg = geminiConfig.getModels().get(type.getConfigKey());
        if (cfg != null && cfg.getModelName() != null) {
            return cfg.getModelName();
        }
        var defaultCfg = geminiConfig.getChatModel();
        return (defaultCfg != null && defaultCfg.getModelName() != null)
                ? defaultCfg.getModelName()
                : type.name().toLowerCase();
    }
}
