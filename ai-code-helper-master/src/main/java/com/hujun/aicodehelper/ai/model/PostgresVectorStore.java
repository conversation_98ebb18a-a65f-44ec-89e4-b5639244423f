package com.hujun.aicodehelper.ai.model;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import javax.sql.DataSource;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import lombok.extern.slf4j.Slf4j;

/**
 * PostgreSQL 向量存储实现
 * 使用 pgvector 扩展进行向量相似度搜索
 */
@Slf4j
public class PostgresVectorStore implements EmbeddingStore<TextSegment> {

    private final DataSource dataSource;
    private final EmbeddingModel embeddingModel;
    private final int vectorDimension;

    public PostgresVectorStore(DataSource dataSource, EmbeddingModel embeddingModel) {
        this(dataSource, embeddingModel, 768); // 默认维度为768 (text-embedding-004)
    }

    public PostgresVectorStore(DataSource dataSource, EmbeddingModel embeddingModel, int vectorDimension) {
        this.dataSource = dataSource;
        this.embeddingModel = embeddingModel;
        this.vectorDimension = vectorDimension;
        initializeDatabase();
    }

    /**
     * 初始化数据库，确保表和扩展存在
     */
    private void initializeDatabase() {
        log.info("🗄️ 正在初始化 PostgreSQL 向量数据库...");
        log.info("📐 向量维度: {}", vectorDimension);

        try (Connection conn = dataSource.getConnection()) {
            // 创建 pgvector 扩展
            try (PreparedStatement stmt = conn.prepareStatement("CREATE EXTENSION IF NOT EXISTS vector")) {
                stmt.execute();
                log.info("✅ pgvector 扩展已创建或已存在");
            }

            // 检查表是否存在以及向量维度是否匹配
            boolean needsRecreate = checkAndValidateTable(conn);

            if (needsRecreate) {
                log.warn("🔄 检测到向量维度不匹配，正在重建embeddings表...");
                recreateEmbeddingsTable(conn);
            } else {
                // 创建向量表 - 使用动态向量维度
                String createTableSQL = String.format("""
                        CREATE TABLE IF NOT EXISTS embeddings (
                            id SERIAL PRIMARY KEY,
                            content TEXT NOT NULL,
                            metadata JSONB,
                            embedding vector(%d),
                            file_name VARCHAR(255),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                        """, vectorDimension);

                try (PreparedStatement stmt = conn.prepareStatement(createTableSQL)) {
                    stmt.execute();
                    log.info("✅ embeddings 表已创建或已存在 (向量维度: {})", vectorDimension);
                }
            }

            // 创建索引
            String createIndexSQL = """
                    CREATE INDEX IF NOT EXISTS embeddings_embedding_idx
                    ON embeddings USING ivfflat (embedding vector_cosine_ops)
                    WITH (lists = 100)
                    """;

            try (PreparedStatement stmt = conn.prepareStatement(createIndexSQL)) {
                stmt.execute();
                log.info("✅ 向量索引已创建或已存在");
            }

            // 创建元数据索引
            String createMetadataIndexSQL = "CREATE INDEX IF NOT EXISTS embeddings_metadata_idx ON embeddings USING GIN (metadata)";
            try (PreparedStatement stmt = conn.prepareStatement(createMetadataIndexSQL)) {
                stmt.execute();
                log.info("✅ 元数据索引已创建或已存在");
            }

            // 创建文件名索引
            String createFileNameIndexSQL = "CREATE INDEX IF NOT EXISTS embeddings_file_name_idx ON embeddings (file_name)";
            try (PreparedStatement stmt = conn.prepareStatement(createFileNameIndexSQL)) {
                stmt.execute();
                log.info("✅ 文件名索引已创建或已存在");
            }

        } catch (SQLException e) {
            log.error("❌ 初始化 PostgreSQL 向量数据库失败: {}", e.getMessage());
            throw new RuntimeException("初始化向量数据库失败", e);
        }
    }

    /**
     * 检查表是否存在以及向量维度是否匹配
     */
    private boolean checkAndValidateTable(Connection conn) throws SQLException {
        // 检查表是否存在
        String checkTableSQL = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'embeddings'
                )
                """;

        try (PreparedStatement stmt = conn.prepareStatement(checkTableSQL);
                ResultSet rs = stmt.executeQuery()) {
            if (rs.next() && rs.getBoolean(1)) {
                // 表存在，检查向量维度
                return checkVectorDimension(conn);
            }
        }

        return false; // 表不存在，不需要重建
    }

    /**
     * 检查现有表的向量维度是否与当前配置匹配
     */
    private boolean checkVectorDimension(Connection conn) throws SQLException {
        String checkDimensionSQL = """
                SELECT atttypmod
                FROM pg_attribute
                WHERE attrelid = 'embeddings'::regclass
                AND attname = 'embedding'
                """;

        try (PreparedStatement stmt = conn.prepareStatement(checkDimensionSQL);
                ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                int existingDimension = rs.getInt(1);
                if (existingDimension != vectorDimension) {
                    log.warn("⚠️  向量维度不匹配: 现有={}, 需要={}", existingDimension, vectorDimension);
                    return true; // 需要重建
                }
            }
        }

        return false; // 维度匹配，不需要重建
    }

    /**
     * 重建embeddings表
     */
    private void recreateEmbeddingsTable(Connection conn) throws SQLException {
        log.info("🗑️  删除现有的embeddings表...");

        // 删除现有表和相关对象
        String dropSQL = """
                DROP TABLE IF EXISTS embeddings CASCADE;
                DROP FUNCTION IF EXISTS search_embeddings CASCADE;
                DROP FUNCTION IF EXISTS add_embedding CASCADE;
                """;

        try (PreparedStatement stmt = conn.prepareStatement(dropSQL)) {
            stmt.execute();
            log.info("✅ 旧表已删除");
        }

        // 创建新表
        String createTableSQL = String.format("""
                CREATE TABLE embeddings (
                    id SERIAL PRIMARY KEY,
                    content TEXT NOT NULL,
                    metadata JSONB,
                    embedding vector(%d),
                    file_name VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """, vectorDimension);

        try (PreparedStatement stmt = conn.prepareStatement(createTableSQL)) {
            stmt.execute();
            log.info("✅ embeddings 表已重建 (向量维度: {})", vectorDimension);
        }
    }

    /**
     * 获取向量维度
     */
    public int getVectorDimension() {
        return vectorDimension;
    }

    @Override
    public String add(Embedding embedding) {
        return add(embedding, TextSegment.from(""));
    }

    @Override
    public void add(String id, Embedding embedding) {
        add(embedding, TextSegment.from(""));
    }

    @Override
    public String add(Embedding embedding, TextSegment textSegment) {
        log.debug("📝 正在添加向量到 PostgreSQL 数据库...");

        String insertSQL = """
                INSERT INTO embeddings (content, embedding, metadata, file_name)
                VALUES (?, ?::vector, ?::jsonb, ?)
                RETURNING id
                """;

        try (Connection conn = dataSource.getConnection();
                PreparedStatement stmt = conn.prepareStatement(insertSQL)) {

            stmt.setString(1, textSegment.text());
            stmt.setString(2, vectorToString(embedding.vectorAsList()));

            // 处理元数据
            String metadata = textSegment.metadata().toMap().isEmpty() ? "{}"
                    : formatMetadataAsJson(textSegment.metadata().toMap());
            stmt.setString(3, metadata);

            // 处理文件名
            String fileName = textSegment.metadata().getString("file_name");
            stmt.setString(4, fileName);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                String id = String.valueOf(rs.getInt("id"));
                log.debug("✅ 向量已添加，ID: {}", id);
                return id;
            }

        } catch (SQLException e) {
            log.error("❌ 添加向量失败: {}", e.getMessage());
            throw new RuntimeException("添加向量到数据库失败", e);
        }

        return null;
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings) {
        List<String> ids = new ArrayList<>();
        for (Embedding embedding : embeddings) {
            ids.add(add(embedding));
        }
        return ids;
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings, List<TextSegment> textSegments) {
        if (embeddings.size() != textSegments.size()) {
            throw new IllegalArgumentException("向量和文本片段数量不匹配");
        }

        List<String> ids = new ArrayList<>();
        for (int i = 0; i < embeddings.size(); i++) {
            ids.add(add(embeddings.get(i), textSegments.get(i)));
        }
        return ids;
    }

    @Override
    public EmbeddingSearchResult<TextSegment> search(EmbeddingSearchRequest request) {
        Embedding queryEmbedding = request.queryEmbedding();
        int maxResults = request.maxResults();
        double minScore = request.minScore();

        List<EmbeddingMatch<TextSegment>> matches = findRelevant(queryEmbedding, maxResults, minScore);
        return new EmbeddingSearchResult<>(matches);
    }

    public List<EmbeddingMatch<TextSegment>> findRelevant(Embedding referenceEmbedding, int maxResults,
            double minScore) {
        return findRelevant(referenceEmbedding, maxResults, minScore, null);
    }

    public List<EmbeddingMatch<TextSegment>> findRelevant(Embedding referenceEmbedding, int maxResults,
            double minScore, java.util.Map<String, Object> metadataFilters) {
        log.debug("🔍 正在搜索相关向量，最大结果数: {}, 最小分数: {}, 过滤器: {}", maxResults, minScore, metadataFilters);

        // 构建动态SQL查询
        StringBuilder sqlBuilder = new StringBuilder("""
                SELECT id, content, metadata, file_name,
                       1 - (embedding <=> ?::vector) AS similarity
                FROM embeddings
                WHERE 1 - (embedding <=> ?::vector) > ?
                """);

        // 添加元数据过滤条件
        if (metadataFilters != null && !metadataFilters.isEmpty()) {
            for (String key : metadataFilters.keySet()) {
                sqlBuilder.append(" AND metadata->>'").append(key).append("' = ?");
            }
        }

        sqlBuilder.append("""
                ORDER BY embedding <=> ?::vector
                LIMIT ?
                """);

        List<EmbeddingMatch<TextSegment>> matches = new ArrayList<>();
        String vectorString = vectorToString(referenceEmbedding.vectorAsList());

        try (Connection conn = dataSource.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sqlBuilder.toString())) {

            int paramIndex = 1;
            stmt.setString(paramIndex++, vectorString);
            stmt.setString(paramIndex++, vectorString);
            stmt.setDouble(paramIndex++, minScore);

            // 设置元数据过滤参数
            if (metadataFilters != null && !metadataFilters.isEmpty()) {
                for (Object value : metadataFilters.values()) {
                    stmt.setString(paramIndex++, value.toString());
                }
            }

            stmt.setString(paramIndex++, vectorString);
            stmt.setInt(paramIndex, maxResults);

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                String content = rs.getString("content");
                double similarity = rs.getDouble("similarity");
                String fileName = rs.getString("file_name");

                TextSegment textSegment;
                if (fileName != null) {
                    java.util.Map<String, Object> metadataMap = new java.util.HashMap<>();
                    metadataMap.put("file_name", fileName);
                    dev.langchain4j.data.document.Metadata metadata = dev.langchain4j.data.document.Metadata
                            .from(metadataMap);
                    textSegment = TextSegment.from(content, metadata);
                } else {
                    textSegment = TextSegment.from(content);
                }

                EmbeddingMatch<TextSegment> match = new EmbeddingMatch<>(
                        similarity,
                        rs.getString("id"),
                        referenceEmbedding,
                        textSegment);
                matches.add(match);
            }

            log.debug("✅ 找到 {} 个相关结果", matches.size());

        } catch (SQLException e) {
            log.error("❌ 向量搜索失败: {}", e.getMessage());
            throw new RuntimeException("向量搜索失败", e);
        }

        return matches;
    }

    @Override
    public void removeAll(Collection<String> ids) {
        if (ids.isEmpty())
            return;

        log.debug("🗑️ 正在删除 {} 个向量", ids.size());

        String deleteSQL = "DELETE FROM embeddings WHERE id = ANY(?)";

        try (Connection conn = dataSource.getConnection();
                PreparedStatement stmt = conn.prepareStatement(deleteSQL)) {

            Integer[] idsArray = ids.stream()
                    .map(Integer::parseInt)
                    .toArray(Integer[]::new);

            stmt.setArray(1, conn.createArrayOf("INTEGER", idsArray));
            int deletedCount = stmt.executeUpdate();

            log.debug("✅ 已删除 {} 个向量", deletedCount);

        } catch (SQLException e) {
            log.error("❌ 删除向量失败: {}", e.getMessage());
            throw new RuntimeException("删除向量失败", e);
        }
    }

    @Override
    public void removeAll() {
        log.debug("🗑️ 正在清空向量数据库...");

        try (Connection conn = dataSource.getConnection();
                PreparedStatement stmt = conn.prepareStatement("DELETE FROM embeddings")) {

            int deletedCount = stmt.executeUpdate();
            log.debug("✅ 已清空数据库，删除了 {} 个向量", deletedCount);

        } catch (SQLException e) {
            log.error("❌ 清空数据库失败: {}", e.getMessage());
            throw new RuntimeException("清空数据库失败", e);
        }
    }

    /**
     * 将向量转换为PostgreSQL可识别的字符串格式
     */
    private String vectorToString(List<Float> vector) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < vector.size(); i++) {
            if (i > 0)
                sb.append(",");
            sb.append(vector.get(i));
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 格式化元数据为JSON字符串
     */
    private String formatMetadataAsJson(java.util.Map<String, Object> metadata) {
        if (metadata.isEmpty())
            return "{}";

        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (java.util.Map.Entry<String, Object> entry : metadata.entrySet()) {
            if (!first)
                json.append(",");
            json.append("\"").append(escapeJsonString(entry.getKey())).append("\":");
            if (entry.getValue() instanceof String) {
                json.append("\"").append(escapeJsonString((String) entry.getValue())).append("\"");
            } else if (entry.getValue() == null) {
                json.append("null");
            } else {
                json.append(entry.getValue());
            }
            first = false;
        }
        json.append("}");
        return json.toString();
    }

    /**
     * 转义JSON字符串中的特殊字符
     */
    private String escapeJsonString(String str) {
        if (str == null)
            return "";

        return str.replace("\\", "\\\\") // 反斜杠
                .replace("\"", "\\\"") // 双引号
                .replace("\b", "\\b") // 退格
                .replace("\f", "\\f") // 换页
                .replace("\n", "\\n") // 换行
                .replace("\r", "\\r") // 回车
                .replace("\t", "\\t"); // 制表符
    }

    /**
     * 获取数据库统计信息
     */
    public long getVectorCount() {
        try (Connection conn = dataSource.getConnection();
                PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM embeddings")) {

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getLong(1);
            }

        } catch (SQLException e) {
            log.error("❌ 获取向量数量失败: {}", e.getMessage());
        }
        return 0;
    }

    /**
     * 检查指定文件是否已存在向量数据
     */
    public boolean hasVectorsForFile(String fileName) {
        if (fileName == null)
            return false;

        try (Connection conn = dataSource.getConnection();
                PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM embeddings WHERE file_name = ?")) {

            stmt.setString(1, fileName);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getLong(1) > 0;
            }

        } catch (SQLException e) {
            log.error("❌ 检查文件向量数据失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 检查指定文件的内容哈希是否已存在
     */
    public boolean hasVectorForContentHash(String contentHash) {
        if (contentHash == null)
            return false;

        try (Connection conn = dataSource.getConnection();
                PreparedStatement stmt = conn.prepareStatement(
                        "SELECT COUNT(*) FROM embeddings WHERE metadata->>'content_hash' = ?")) {

            stmt.setString(1, contentHash);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getLong(1) > 0;
            }

        } catch (SQLException e) {
            log.error("❌ 检查内容哈希失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 获取所有已存在的文件名列表
     */
    public List<String> getExistingFileNames() {
        List<String> fileNames = new ArrayList<>();

        try (Connection conn = dataSource.getConnection();
                PreparedStatement stmt = conn.prepareStatement(
                        "SELECT DISTINCT file_name FROM embeddings WHERE file_name IS NOT NULL")) {

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                fileNames.add(rs.getString("file_name"));
            }

        } catch (SQLException e) {
            log.error("❌ 获取现有文件名列表失败: {}", e.getMessage());
        }
        return fileNames;
    }

    /**
     * 删除指定文件的所有向量数据
     */
    public void removeVectorsForFile(String fileName) {
        if (fileName == null)
            return;

        try (Connection conn = dataSource.getConnection();
                PreparedStatement stmt = conn.prepareStatement("DELETE FROM embeddings WHERE file_name = ?")) {

            stmt.setString(1, fileName);
            int deletedCount = stmt.executeUpdate();
            log.info("🗑️ 已删除文件 {} 的 {} 个向量", fileName, deletedCount);

        } catch (SQLException e) {
            log.error("❌ 删除文件向量数据失败: {}", e.getMessage());
            throw new RuntimeException("删除文件向量数据失败", e);
        }
    }
}