package com.hujun.aicodehelper.ai;

import com.hujun.aicodehelper.ai.guardrail.SafeInputGuardrail;
import dev.langchain4j.service.*;
import dev.langchain4j.service.guardrail.InputGuardrails;
import reactor.core.publisher.Flux;

import java.util.List;
import dev.langchain4j.data.message.Content;

//改为手动构建，更灵活
//@AiService
@InputGuardrails({SafeInputGuardrail.class})
public interface AiCodeHelperService {

    @SystemMessage(fromResource = "system-prompt.txt")
    String chat(String userMessage);

    @SystemMessage(fromResource = "system-prompt.txt")
    Report chatForReport(String userMessage);

    // 学习报告
    record Report(String name, List<String> suggestionList) {
    }

    @SystemMessage(fromResource = "system-prompt.txt")
    Result<String> chatWithRag(String userMessage);

    // 流式对话
    @SystemMessage(fromResource = "system-prompt.txt")
    Flux<String> chatStream(@MemoryId int memoryId, @UserMessage String userMessage);

    // 多模态流式对话（内容块形式），用于传递文本+图片
    // 使用专用多模态系统提示，鼓励进行OCR与图像理解
    @SystemMessage(fromResource = "system-prompt-multimodal.txt")
    Flux<String> chatStream(@MemoryId int memoryId, @UserMessage List<Content> contents);

    // 多模态流式对话 - 支持图片输入
    @SystemMessage(fromResource = "system-prompt.txt")
    Flux<String> chatStreamWithImage(@MemoryId int memoryId, @UserMessage String textMessage, @V("imageUrl") String imageUrl);
    
    // 带语言参数的流式对话方法（在实现类中定义具体逻辑）
    default Flux<String> chatStreamWithLanguage(int memoryId, String userMessage, String language) {
        return chatStream(memoryId, userMessage);
    }
    
    // 带语言参数的多模态流式对话方法
    default Flux<String> chatStreamWithImageAndLanguage(int memoryId, String textMessage, String imageUrl, String language) {
        return chatStreamWithImage(memoryId, textMessage, imageUrl);
    }
}
