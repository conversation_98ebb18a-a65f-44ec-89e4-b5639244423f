package com.hujun.aicodehelper.ai.tools.math;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * K12 数学工具配置类
 * 统一管理所有数学计算工具的配置和组合
 */
@Slf4j
@Configuration
public class MathToolsConfig {

    /**
     * 获取所有 P0 级别数学工具
     * 基础代数：方程求解、因式分解、化简等
     */
    @Bean
    public P0BasicMathTool p0BasicMathTool() {
        log.info("🔧 初始化 P0 基础数学工具");
        return new P0BasicMathTool();
    }

    /**
     * 获取所有 P1 级别数学工具  
     * 高等数学：导数、积分、极限等
     */
    @Bean
    public P1AdvancedMathTool p1AdvancedMathTool() {
        log.info("🔧 初始化 P1 高级数学工具");
        return new P1AdvancedMathTool();
    }

    /**
     * 获取所有 P2 级别数学工具
     * 专业数学：解析几何、数论组合等
     */
    @Bean
    public P2SpecializedMathTool p2SpecializedMathTool() {
        log.info("🔧 初始化 P2 专业数学工具");
        return new P2SpecializedMathTool();
    }

    /**
     * 数学工具能力级别枚举
     */
    public enum MathLevel {
        P0("基础代数", "一元方程/方程组、因式分解、化简、展开、分式运算、不等式"),
        P1("高等数学", "导数、积分、极限、数列、线性代数基础"),
        P2("专业数学", "解析几何、数论、组合数学、概率统计");

        private final String name;
        private final String description;

        MathLevel(String name, String description) {
            this.name = name;
            this.description = description;
        }

        public String getName() { return name; }
        public String getDescription() { return description; }
    }

    /**
     * 根据级别获取对应的工具列表
     * 支持组合不同级别的工具
     */
    public static List<Object> getToolsForLevels(P0BasicMathTool p0Tool, P1AdvancedMathTool p1Tool, 
                                                  P2SpecializedMathTool p2Tool, MathLevel... levels) {
        List<MathLevel> levelList = Arrays.asList(levels);
        
        return levelList.stream()
            .flatMap(level -> {
                switch (level) {
                    case P0: return List.of(p0Tool).stream();
                    case P1: return List.of(p1Tool).stream();
                    case P2: return List.of(p2Tool).stream();
                    default: return List.<Object>of().stream();
                }
            })
            .toList();
    }

    /**
     * 获取完整工具集（P0 + P1 + P2）
     * 适用于高级数学教学场景
     */
    public static List<Object> getAllMathTools(P0BasicMathTool p0Tool, P1AdvancedMathTool p1Tool, 
                                               P2SpecializedMathTool p2Tool) {
        return Arrays.asList(p0Tool, p1Tool, p2Tool);
    }

    /**
     * 获取基础工具集（仅P0）
     * 适用于初中数学教学
     */
    public static List<Object> getBasicMathTools(P0BasicMathTool p0Tool) {
        return List.of(p0Tool);
    }

    /**
     * 获取高级工具集（P0 + P1）
     * 适用于高中数学教学
     */
    public static List<Object> getAdvancedMathTools(P0BasicMathTool p0Tool, P1AdvancedMathTool p1Tool) {
        return Arrays.asList(p0Tool, p1Tool);
    }

    /**
     * 数学工具使用统计和监控
     */
    public static void logMathToolUsage(String toolName, String operation, boolean success) {
        if (success) {
            log.info("✅ 数学工具调用成功 - 工具: {}, 操作: {}", toolName, operation);
        } else {
            log.warn("❌ 数学工具调用失败 - 工具: {}, 操作: {}", toolName, operation);
        }
    }

    /**
     * 数学教学提示生成器
     */
    public static String generateTeachingHint(String topic) {
        return switch (topic.toLowerCase()) {
            case "equation" -> "💡 求解方程时，记住要保持等式平衡，两边同时进行相同运算。";
            case "factorization" -> "💡 因式分解时，寻找公因式或特殊公式（如平方差、完全平方）。";
            case "derivative" -> "💡 导数表示函数的变化率，几何意义是切线斜率。";
            case "integral" -> "💡 积分是导数的逆运算，几何意义是面积。";
            case "limit" -> "💡 极限描述函数在某点附近的趋势，不一定等于函数值。";
            case "probability" -> "💡 概率值始终在0到1之间，所有可能事件的概率和为1。";
            default -> "💡 数学是逻辑的艺术，每一步都有其道理。";
        };
    }
}
