package com.hujun.aicodehelper.ai.model;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.openai.OpenAiEmbeddingModel;
import lombok.Data;

@Configuration
@ConfigurationProperties(prefix = "langchain4j.modelscope.openai.embedding-model")
@Data
public class ModelScopeOpenAiEmbeddingModelConfig {

    private String apiKey;
    private String modelName;
    private String baseUrl = "https://api-inference.modelscope.cn/v1";

    @Bean(name = "myModelScopeEmbeddingModel")
    public EmbeddingModel myModelScopeEmbeddingModel() {
        return OpenAiEmbeddingModel.builder()
                .apiKey(apiKey)
                .modelName(modelName)
                .baseUrl(baseUrl)
                .build();
    }
}

