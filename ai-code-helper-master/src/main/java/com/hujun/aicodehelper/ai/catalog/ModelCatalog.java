package com.hujun.aicodehelper.ai.catalog;

import java.util.Optional;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;

public interface ModelCatalog {
    Optional<StreamingChatModel> getStreaming(UseCase useCase);
    Optional<ChatModel> getChat(UseCase useCase);

    /**
     * 返回当前UseCase下实际配置的底层模型名（便于日志与调试）。
     * 默认返回useCase名称。
     */
    default String getEffectiveModelName(UseCase useCase) {
        return useCase.name().toLowerCase();
    }
}
