package com.hujun.aicodehelper.ai.model;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自定义Google AI嵌入模型实现
 * 支持outputDimensionality参数来控制向量输出维度
 */
@Slf4j
public class CustomGoogleAiEmbeddingModel implements EmbeddingModel {

    private final String apiKey;
    private final String modelName;
    private final int outputDimensionality;
    private final RestTemplate restTemplate;
    private final String baseUrl = "https://generativelanguage.googleapis.com/v1beta";
    
    // API限流相关参数 - 更保守的设置
    private static final int MAX_REQUESTS_PER_MINUTE = 60; // 进一步降低到60以留出更多缓冲
    private static final int MAX_REQUESTS_PER_DAY = 900; // 设置为900以留出缓冲
    private static final long MINUTE_IN_MILLIS = 60 * 1000;
    private static final long DAY_IN_MILLIS = 24 * 60 * 60 * 1000;
    
    // 请求计数器
    private volatile int requestsThisMinute = 0;
    private volatile int requestsToday = 0;
    private volatile long lastMinuteReset = System.currentTimeMillis();
    private volatile long lastDayReset = System.currentTimeMillis();


    public CustomGoogleAiEmbeddingModel(String apiKey, String modelName, int outputDimensionality) {
        this.apiKey = apiKey;
        this.modelName = modelName;
        this.outputDimensionality = outputDimensionality;
        this.restTemplate = new RestTemplate();
        
        log.info("🤖 正在初始化自定义Google AI嵌入模型...");
        log.info("📋 模型配置信息:");
        log.info("   - 模型名称: {}", this.modelName);
        log.info("   - 输出维度: {}", this.outputDimensionality);
        log.info("   - API端点: {}", this.baseUrl);
    }

    @Override
    public Response<Embedding> embed(TextSegment textSegment) {
        return embed(textSegment.text());
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response<Embedding> embed(String text) {
        log.debug("🚀 调用Google AI模型进行向量化: '{}'", text.substring(0, Math.min(100, text.length())) + "...");
        
        try {
            // 检查API限流
            checkRateLimit();
            
            long startTime = System.currentTimeMillis();
            
            // 构建请求体，使用正确的Google AI API格式
            Map<String, Object> content = Map.of(
                "parts", List.of(Map.of("text", text))
            );
            Map<String, Object> requestBody = Map.of(
                "requests", List.of(Map.of(
                    "model", "models/" + modelName,
                    "content", content,
                    "outputDimensionality", outputDimensionality,
                    "taskType", "RETRIEVAL_DOCUMENT"
                ))
            );
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("x-goog-api-key", apiKey);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            // 调用Google AI API
            String url = String.format("%s/models/%s:batchEmbedContents", baseUrl, modelName);
            ResponseEntity<Map<String, Object>> response = restTemplate.postForEntity(
                url, request, (Class<Map<String, Object>>) (Class<?>) Map.class
            );
            
            long endTime = System.currentTimeMillis();
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> embeddings = (List<Map<String, Object>>) responseBody.get("embeddings");
                
                if (embeddings != null && !embeddings.isEmpty()) {
                    Map<String, Object> embeddingData = embeddings.get(0);
                    List<Double> values = (List<Double>) embeddingData.get("values");
                    
                    if (values != null) {
                        // 直接转换为float数组
                        float[] vector = new float[values.size()];
                        for (int i = 0; i < values.size(); i++) {
                            vector[i] = values.get(i).floatValue();
                        }
                        
                        log.debug("✅ 向量化完成，维度: {}, 耗时: {}ms", vector.length, endTime - startTime);
                        
                        return Response.from(Embedding.from(vector));
                    }
                }
            }
            
            log.error("❌ Google AI API响应格式不正确: {}", response.getBody());
            throw new RuntimeException("Google AI API响应格式不正确");
            
        } catch (Exception e) {
            log.error("❌ 调用Google AI嵌入模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用Google AI嵌入模型失败", e);
        }
    }

    @Override
    public Response<List<Embedding>> embedAll(List<TextSegment> textSegments) {
        log.debug("🚀 批量调用Google AI模型进行向量化，数量: {}", textSegments.size());
        
        List<String> texts = textSegments.stream()
                .map(TextSegment::text)
                .collect(Collectors.toList());
        
        return embedTexts(texts);
    }

    /**
     * 批量向量化文本列表
     * 支持自动分批处理，每批最多50个请求
     */
    public Response<List<Embedding>> embedTexts(List<String> texts) {
        log.debug("🚀 批量调用Google AI模型进行向量化，数量: {}", texts.size());
        
        final int BATCH_SIZE = 50; // 减小批次大小以降低频率限制风险
        List<Embedding> allEmbeddings = new ArrayList<>();
        
        try {
            long totalStartTime = System.currentTimeMillis();
            
            // 将文本分批处理
            for (int i = 0; i < texts.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, texts.size());
                List<String> batch = texts.subList(i, endIndex);
                
                log.debug("📦 处理第 {}/{} 批，包含 {} 个文本", 
                         (i / BATCH_SIZE) + 1, (texts.size() + BATCH_SIZE - 1) / BATCH_SIZE, batch.size());
                
                List<Embedding> batchEmbeddings = processBatch(batch);
                allEmbeddings.addAll(batchEmbeddings);
                
                // 添加更长的延迟，避免API限流
                if (i + BATCH_SIZE < texts.size()) {
                    Thread.sleep(3000); // 3秒延迟，更保守的间隔
                }
            }
            
            long totalEndTime = System.currentTimeMillis();
            log.debug("✅ 全部批量向量化完成，总数量: {}, 维度: {}, 总耗时: {}ms", 
                     allEmbeddings.size(), 
                     allEmbeddings.isEmpty() ? 0 : allEmbeddings.get(0).vector().length, 
                     totalEndTime - totalStartTime);
            
            return Response.from(allEmbeddings);
            
        } catch (Exception e) {
            log.error("❌ 批量调用Google AI嵌入模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量调用Google AI嵌入模型失败", e);
        }
    }
    
    /**
     * 检查并等待API限流
     */
    private synchronized void checkRateLimit() throws InterruptedException {
        long currentTime = System.currentTimeMillis();
        
        // 重置分钟计数器
        if (currentTime - lastMinuteReset >= MINUTE_IN_MILLIS) {
            requestsThisMinute = 0;
            lastMinuteReset = currentTime;
        }
        
        // 重置日计数器
        if (currentTime - lastDayReset >= DAY_IN_MILLIS) {
            requestsToday = 0;
            lastDayReset = currentTime;
        }
        
        // 检查日限制
        if (requestsToday >= MAX_REQUESTS_PER_DAY) {
            long waitTime = DAY_IN_MILLIS - (currentTime - lastDayReset);
            log.warn("⚠️ 已达到每日API调用限制 ({}/{}), 需要等待 {} 分钟", 
                    requestsToday, MAX_REQUESTS_PER_DAY, waitTime / 60000);
            throw new RuntimeException("已达到Google AI API每日调用限制，请明天再试");
        }
        
        // 检查分钟限制
        if (requestsThisMinute >= MAX_REQUESTS_PER_MINUTE) {
            long waitTime = MINUTE_IN_MILLIS - (currentTime - lastMinuteReset);
            log.info("⏰ 已达到每分钟API调用限制 ({}/{}), 等待 {} 秒后继续", 
                    requestsThisMinute, MAX_REQUESTS_PER_MINUTE, waitTime / 1000);
            Thread.sleep(waitTime + 1000); // 额外等待1秒确保安全
            
            // 重置计数器
            requestsThisMinute = 0;
            lastMinuteReset = System.currentTimeMillis();
        }
        
        // 增加计数
        requestsThisMinute++;
        requestsToday++;
        
        log.debug("📊 API调用计数 - 本分钟: {}/{}, 今日: {}/{}", 
                requestsThisMinute, MAX_REQUESTS_PER_MINUTE, 
                requestsToday, MAX_REQUESTS_PER_DAY);
    }
    
    /**
     * 处理单个批次的向量化请求
     */
    @SuppressWarnings("unchecked")
    private List<Embedding> processBatch(List<String> texts) {
        final int MAX_RETRIES = 3;
        final long DELAY_ON_429 = 60000; // 直接等待60秒
        
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                // 检查API限流
                checkRateLimit();
                
                long startTime = System.currentTimeMillis();
                
                // 构建批量请求体，使用正确的Google AI API格式
                List<Map<String, Object>> requests = texts.stream()
                        .map(text -> Map.of(
                            "model", "models/" + modelName,
                            "content", Map.of("parts", List.of(Map.of("text", text))),
                            "outputDimensionality", outputDimensionality,
                            "taskType", "RETRIEVAL_DOCUMENT"
                        ))
                        .collect(Collectors.toList());
                
                Map<String, Object> requestBody = Map.of("requests", requests);
                
                // 设置请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.set("x-goog-api-key", apiKey);
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
                
                // 调用Google AI API
                String url = String.format("%s/models/%s:batchEmbedContents", baseUrl, modelName);
                ResponseEntity<Map<String, Object>> response = restTemplate.postForEntity(
                    url, request, (Class<Map<String, Object>>) (Class<?>) Map.class
                );
            
                long endTime = System.currentTimeMillis();
                
                if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                    Map<String, Object> responseBody = response.getBody();
                    List<Map<String, Object>> embeddings = (List<Map<String, Object>>) responseBody.get("embeddings");
                    
                    if (embeddings != null) {
                        List<Embedding> embeddingList = embeddings.stream()
                                .map(embeddingData -> {
                                    List<Double> values = (List<Double>) embeddingData.get("values");
                                    if (values != null) {
                                        float[] vector = new float[values.size()];
                                        for (int i = 0; i < values.size(); i++) {
                                            vector[i] = values.get(i).floatValue();
                                        }
                                        return Embedding.from(vector);
                                    }
                                    return null;
                                })
                                .filter(embedding -> embedding != null)
                                .collect(Collectors.toList());
                        
                        log.debug("✅ 批次向量化完成，数量: {}, 维度: {}, 耗时: {}ms", 
                                 embeddingList.size(), 
                                 embeddingList.isEmpty() ? 0 : embeddingList.get(0).vector().length, 
                                 endTime - startTime);
                        
                        return embeddingList;
                    }
                }
                
                log.error("❌ Google AI API批次响应格式不正确: {}", response.getBody());
                throw new RuntimeException("Google AI API批次响应格式不正确");
                
            } catch (org.springframework.web.client.HttpClientErrorException.TooManyRequests e) {
                log.warn("⚠️ API请求频率过高 (429错误), 第 {}/{} 次尝试", attempt, MAX_RETRIES);
                        
                if (attempt == MAX_RETRIES) {
                    log.error("❌ 重试次数已达上限，放弃处理该批次");
                    throw new RuntimeException("Google AI API频率限制，请稍后重试", e);
                }
                
                // 直接等待60秒
                log.info("⏰ 等待60秒后重试...");
                try {
                    Thread.sleep(DELAY_ON_429);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
                
            } catch (Exception e) {
                log.error("❌ 处理批次向量化失败 (第 {}/{} 次尝试): {}", attempt, MAX_RETRIES, e.getMessage());
                if (attempt == MAX_RETRIES) {
                    throw new RuntimeException("处理批次向量化失败", e);
                }
                
                // 其他错误也进行短暂重试
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
            }
        }
        
        // 理论上不会到达这里
        throw new RuntimeException("批次处理失败：超出最大重试次数");
    }

    /**
     * 测试连接
     */
    public boolean testConnection() {
        try {
            embed("test");
            return true;
        } catch (Exception e) {
            log.warn("⚠️ Google AI嵌入模型连接测试失败: {}", e.getMessage());
            return false;
        }
    }
}