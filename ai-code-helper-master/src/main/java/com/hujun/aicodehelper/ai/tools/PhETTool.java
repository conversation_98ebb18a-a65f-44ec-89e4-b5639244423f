package com.hujun.aicodehelper.ai.tools;

import dev.langchain4j.agent.tool.Tool;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * PhET交互式模拟工具 - MVP硬编码版本
 * 
 * 提供K12阶段最常用、效果最好的PhET模拟，支持中英文关键词匹配。
 * 这个版本内置了精心挑选的5个核心模拟，覆盖数学和物理的关键概念。
 * 
 * <AUTHOR> CodeHelper
 */
@Component
public class PhETTool {

    /**
     * 内部数据结构，用于存储预设的模拟信息
     */
    private static class PhETSimulation {
        private final String title;
        private final String url;
        private final List<String> keywords;
        private final String description;

        public PhETSimulation(String title, String url, String description, String... keywords) {
            this.title = title;
            this.url = url;
            this.description = description;
            this.keywords = Arrays.asList(keywords);
        }
    }

    /**
     * 硬编码的模拟列表 - 包含K12阶段最重要的交互式学习内容
     */
    private static final List<PhETSimulation> SIMULATIONS = Arrays.asList(
        new PhETSimulation(
            "Circuit Construction Kit: DC",
            "https://phet.colorado.edu/sims/html/circuit-construction-kit-dc/latest/circuit-construction-kit-dc_all.html",
            "通过拖拽组件构建直流电路，观察电流、电压变化，理解欧姆定律",
            "circuit", "current", "voltage", "resistance", "ohm", "electrical", "电路", "电流", "电压", "电阻", "欧姆定律", "电学"
        ),
        new PhETSimulation(
            "Forces and Motion: Basics",
            "https://phet.colorado.edu/sims/html/forces-and-motion-basics/latest/forces-and-motion-basics_all.html",
            "探索力与运动的关系，体验摩擦力、重力等概念，验证牛顿定律",
            "force", "motion", "friction", "newton", "physics", "push", "pull", "力", "运动", "摩擦力", "牛顿", "物理", "推力", "拉力"
        ),
        new PhETSimulation(
            "Fraction Matcher",
            "https://phet.colorado.edu/sims/html/fraction-matcher/latest/fraction-matcher_all.html",
            "通过可视化方式理解分数概念，进行分数与小数的匹配练习",
            "fraction", "decimal", "math", "numerator", "denominator", "分数", "小数", "数学", "分子", "分母", "匹配"
        ),
        new PhETSimulation(
            "Graphing Lines",
            "https://phet.colorado.edu/sims/html/graphing-lines/latest/graphing-lines_all.html",
            "交互式构建线性函数图像，理解斜率、截距等概念",
            "function", "graph", "plot", "line", "slope", "intercept", "linear", "equation", "函数", "图像", "画图", "直线", "斜率", "截距", "线性", "方程"
        ),
        new PhETSimulation(
            "Projectile Motion",
            "https://phet.colorado.edu/sims/html/projectile-motion/latest/projectile-motion_all.html",
            "模拟抛体运动轨迹，探索角度、初速度对抛物线的影响",
            "projectile", "parabola", "angle", "velocity", "gravity", "trajectory", "physics", "抛射", "抛物线", "角度", "初速度", "重力", "轨迹", "物理"
        )
    );

    /**
     * 根据关键词查找相关的PhET交互式模拟
     * 
     * @param keyword 搜索关键词（支持中英文）
     * @return 包含嵌入HTML代码的响应文本
     */
    @Tool("根据关键词查找相关的PhET交互式模拟并提供HTML嵌入代码。适用于需要可视化或交互式学习的请求。")
    public String findPhetSimulationByKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return "请提供一个关键词来搜索PhET模拟。例如：'电路'、'力'、'分数'、'函数'或'抛射'。";
        }

        String lowerCaseKeyword = keyword.toLowerCase().trim();

        // 关键词匹配逻辑 - 支持部分匹配
        Optional<PhETSimulation> foundSim = SIMULATIONS.stream()
            .filter(sim -> sim.keywords.stream()
                .anyMatch(k -> k.toLowerCase().contains(lowerCaseKeyword) || 
                             lowerCaseKeyword.contains(k.toLowerCase())))
            .findFirst();

        if (foundSim.isPresent()) {
            PhETSimulation sim = foundSim.get();
            String embedHtml = String.format(
                "<iframe src=\"%s\" width=\"800\" height=\"600\" allowfullscreen style=\"border:1px solid #ccc; border-radius: 8px;\"></iframe>", 
                sim.url
            );
            
            return String.format(
                "我为您找到了一个完美的交互式模拟：**%s**！\n\n%s\n\n%s\n\n💡 **使用提示**：您可以直接在模拟中拖拽、点击各种元素来进行互动学习。",
                sim.title,
                sim.description,
                embedHtml
            );
        } else {
            return String.format(
                "抱歉，我没有找到与关键词 '%s' 匹配的交互式模拟。\n\n🔍 **可用的模拟主题**：\n" +
                "• **电路** - 构建直流电路，学习电学基础\n" +
                "• **力与运动** - 探索物理力学概念\n" +
                "• **分数** - 可视化学习分数概念\n" +
                "• **函数图像** - 交互式绘制线性函数\n" +
                "• **抛体运动** - 模拟抛射轨迹\n\n" +
                "请尝试使用这些关键词之一！",
                keyword
            );
        }
    }

    /**
     * 获取所有可用的PhET模拟列表
     * 
     * @return 所有模拟的简要信息
     */
    @Tool("获取所有可用的PhET交互式模拟列表")
    public String listAllPhetSimulations() {
        StringBuilder result = new StringBuilder();
        result.append("📚 **可用的PhET交互式模拟** (共").append(SIMULATIONS.size()).append("个)：\n\n");
        
        for (int i = 0; i < SIMULATIONS.size(); i++) {
            PhETSimulation sim = SIMULATIONS.get(i);
            result.append(String.format("%d. **%s**\n   %s\n   关键词：%s\n\n", 
                i + 1, 
                sim.title, 
                sim.description,
                String.join(", ", sim.keywords.subList(0, Math.min(6, sim.keywords.size())))
            ));
        }
        
        result.append("💡 使用 `findPhetSimulationByKeyword(\"关键词\")` 来获取具体模拟的嵌入代码！");
        return result.toString();
    }
}
