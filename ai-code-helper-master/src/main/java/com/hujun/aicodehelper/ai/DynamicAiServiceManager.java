package com.hujun.aicodehelper.ai;

import java.util.Comparator;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.hujun.aicodehelper.ai.catalog.CompositeCatalog;
import com.hujun.aicodehelper.ai.catalog.UseCase;
import com.hujun.aicodehelper.ai.catalog.ModelCatalog;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType;

import com.hujun.aicodehelper.ai.tools.PhETTool;
import com.hujun.aicodehelper.ai.tools.math.MathToolsConfig;
import com.hujun.aicodehelper.ai.tools.math.P0BasicMathTool;
import com.hujun.aicodehelper.ai.tools.math.P1AdvancedMathTool;
import com.hujun.aicodehelper.ai.tools.math.P2SpecializedMathTool;

import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.service.AiServices;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 动态AI服务管理器
 * 负责根据使用场景动态创建和管理不同的AI服务实例
 */
@Slf4j
@Component
public class DynamicAiServiceManager {

    @Resource
    private CompositeCatalog compositeCatalog;

    @Resource
    private ModelSelectionStrategy modelSelectionStrategy;

    @Resource
    private ContentRetriever contentRetriever;

    @Resource
    private ChatMemoryProvider chatMemoryProvider;

    @Resource
    private P0BasicMathTool p0BasicMathTool;

    @Resource
    private P1AdvancedMathTool p1AdvancedMathTool;

    @Resource
    private P2SpecializedMathTool p2SpecializedMathTool;

    @Resource
    private PhETTool phetTool;

    // provider 路由改由 CompositeCatalog 管理

    @Value("${ai.chat.memory.debug-mode:false}")
    private boolean debugMode;

    // 缓存不同模型类型的AI服务实例
    private final Map<ModelType, AiCodeHelperService> aiServiceCache = new ConcurrentHashMap<>();

    // 服务可用性状态
    private final Map<ModelType, Boolean> serviceAvailability = new ConcurrentHashMap<>();

    @PostConstruct
    public void initServices() {
        log.info("正在初始化动态AI服务管理器...");

        // 预热所有模型类型的服务
        warmUpServices();

        log.info("动态AI服务管理器初始化完成，可用服务: {}", getAvailableModelTypes());
    }

    /**
     * 预热所有模型服务（提高首次调用速度）
     */
    public void warmUpServices() {
        for (ModelType modelType : ModelType.values()) {
            try {
                createAiService(modelType);
                serviceAvailability.put(modelType, true);
                log.debug("服务 [{}] 预热成功", modelType.name());
            } catch (Exception e) {
                serviceAvailability.put(modelType, false);
                log.error("服务 [{}] 预热失败: {}", modelType.name(), e.getMessage());
            }
        }
    }

    /**
     * 根据用户消息智能选择AI服务
     * 
     * @param userMessage         用户输入消息
     * @param conversationHistory 对话历史
     * @return 适合的AI服务实例
     */
    public AiCodeHelperService getAiService(String userMessage, List<String> conversationHistory) {
        // 1. 检查是否有显式模型请求
        ModelType explicitModel = modelSelectionStrategy.getExplicitModelRequest(userMessage);
        if (explicitModel != null) {
            log.info("用户显式请求使用模型: {}", explicitModel.getDescription());
            return getAiServiceByModelType(explicitModel);
        }

        // 2. 使用智能选择策略
        ModelType selectedModel = modelSelectionStrategy.selectModel(userMessage, conversationHistory);
        return getAiServiceByModelType(selectedModel);
    }

    /**
     * 根据模型类型获取AI服务
     * 
     * @param modelType 模型类型
     * @return AI服务实例
     */
    public AiCodeHelperService getAiServiceByModelType(ModelType modelType) {
        // 检查服务可用性
        if (!isServiceAvailable(modelType)) {
            log.warn("模型服务 [{}] 不可用，降级到默认模型", modelType.name());
            modelType = ModelType.LITE; // 降级到轻量级模型
        }

        // 从缓存获取或创建新服务
        return aiServiceCache.computeIfAbsent(modelType, this::createAiService);
    }

    /**
     * 创建指定模型类型的AI服务
     * 
     * @param modelType 模型类型
     * @return AI服务实例
     */
    private AiCodeHelperService createAiService(ModelType modelType) {
        log.debug("正在为模型类型 [{}] 创建AI服务", modelType.name());

        try {
            // 将 ModelType 映射到 UseCase 并从CompositeCatalog获取模型
            UseCase useCase = modelType.getUseCase();
            StreamingChatModel streamingChatModel = compositeCatalog.getStreaming(useCase)
                    .orElseThrow(() -> new IllegalStateException("未找到 UseCase=" + useCase + " 的StreamingChatModel"));

            if (streamingChatModel == null) {
                throw new IllegalStateException("无法获取模型类型 [" + modelType.name() + "] 的流式模型实例");
            }

            // 根据调试模式选择聊天记忆提供者
            ChatMemoryProvider memoryProvider = getMemoryProvider();

            // 根据模型类型构造AI服务
            AiCodeHelperService service = createServiceForModelType(modelType, streamingChatModel, memoryProvider);

            log.info("成功创建AI服务 [{}]: {}", modelType.name(), modelType.getDescription());
            return service;

        } catch (Exception e) {
            log.error("创建AI服务 [{}] 失败: {}", modelType.name(), e.getMessage(), e);
            throw new RuntimeException("创建AI服务失败", e);
        }
    }

    /**
     * 获取用于多模态（带图片）场景的服务。
     * 在 ModelScope 提供者下优先使用视觉语言模型；否则回退到轻量模型。
     */
    public AiCodeHelperService getMultimodalAiService() {
        try {
            StreamingChatModel streamingChatModel = compositeCatalog.getStreaming(UseCase.MULTIMODAL)
                    .orElseGet(() -> compositeCatalog.getStreaming(UseCase.TEXT).orElse(null));
            if (streamingChatModel == null) {
                throw new IllegalStateException("未找到多模态或文本流式模型");
            }
            ChatMemoryProvider memoryProvider = getMemoryProvider();
            return createServiceForModelType(ModelType.LITE, streamingChatModel, memoryProvider);
        } catch (Exception e) {
            log.error("创建多模态AI服务失败: {}", e.getMessage(), e);
            return getAiServiceByModelType(ModelType.LITE);
        }
    }


    // 模型类型与UseCase的映射已内置于 ModelType 枚举中

    /**
     * 根据模型类型创建特定的服务实例
     */
    private AiCodeHelperService createServiceForModelType(ModelType modelType, StreamingChatModel streamingChatModel,
            ChatMemoryProvider memoryProvider) {
        log.info("🔧 创建AI服务，模型类型: {}, 使用StreamingChatModel", modelType.name());

        switch (modelType) {
            case RAG:
                // RAG模型添加内容检索器
                log.debug("为RAG模型添加内容检索器");
                return AiServices.builder(AiCodeHelperService.class)
                        // 只使用StreamingChatModel，确保流式响应正常工作
                        .streamingChatModel(streamingChatModel)
                        .chatMemoryProvider(memoryProvider)
                        .contentRetriever(contentRetriever)
                        .build();

            case MATH:
                // 数学模型添加K12数学工具和PhET交互式模拟
                log.debug("为数学模型添加K12数学计算工具和PhET交互式模拟");
                List<Object> mathTools = new ArrayList<>();
                mathTools.addAll(MathToolsConfig.getAllMathTools(p0BasicMathTool, p1AdvancedMathTool, p2SpecializedMathTool));
                mathTools.add(phetTool);
                return AiServices.builder(AiCodeHelperService.class)
                        .streamingChatModel(streamingChatModel)
                        .chatMemoryProvider(memoryProvider)
                        .tools(mathTools)
                        .build();

            case PRO:
                // 专业模型添加RAG功能和全部工具（数学+PhET）
                log.debug("为专业模型添加RAG功能和全部工具");
                List<Object> proTools = new ArrayList<>();
                proTools.add(p0BasicMathTool);
                proTools.add(p1AdvancedMathTool);
                proTools.add(p2SpecializedMathTool);
                proTools.add(phetTool);
                return AiServices.builder(AiCodeHelperService.class)
                        .streamingChatModel(streamingChatModel)
                        .chatMemoryProvider(memoryProvider)
                        .contentRetriever(contentRetriever)
                        .tools(proTools)
                        .build();

            case LITE:
            default:
                // 轻量级模型只使用基础功能
                log.debug("轻量级模型使用基础配置");
                return AiServices.builder(AiCodeHelperService.class)
                        // 只使用StreamingChatModel，确保流式响应正常工作
                        .streamingChatModel(streamingChatModel)
                        .chatMemoryProvider(memoryProvider)
                        .build();
        }
    }

    /**
     * 获取内存提供者
     */
    private ChatMemoryProvider getMemoryProvider() {
        if (debugMode) {
            log.debug("🚨 调试模式 - 使用简单内存聊天记忆");
            return memoryId -> MessageWindowChatMemory.withMaxMessages(5);
        } else {
            log.debug("✅ 生产模式 - 使用持久化聊天记忆");
            return chatMemoryProvider;
        }
    }

    /**
     * 检查服务是否可用
     */
    private boolean isServiceAvailable(ModelType modelType) {
        return serviceAvailability.getOrDefault(modelType, false);
    }

    /**
     * 获取模型可用性状态
     */
    public Map<ModelType, Boolean> getModelAvailability() {
        return new HashMap<>(serviceAvailability);
    }

    /**
     * 获取可用的模型类型列表
     */
    public List<ModelType> getAvailableModelTypes() {
        return serviceAvailability.entrySet().stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .sorted(Comparator.comparing(ModelType::name))
                .toList();
    }

    /**
     * 根据工具选择合适的AI服务
     * 
     * @param toolName 工具名称
     * @return AI服务实例
     */
    public AiCodeHelperService getAiServiceForTool(String toolName) {
        ModelType modelType = modelSelectionStrategy.selectModelForTool(toolName);
        return getAiServiceByModelType(modelType);
    }

    /**
     * 刷新服务缓存（重新创建所有服务）
     */
    public void refreshServices() {
        log.info("正在刷新AI服务缓存...");
        aiServiceCache.clear();
        serviceAvailability.clear();
        warmUpServices();
        log.info("AI服务缓存刷新完成");
    }

    /**
     * 获取服务统计信息
     */
    public Map<String, Object> getServiceStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cachedServices", aiServiceCache.size());
        stats.put("availableServices", getAvailableModelTypes().size());
        stats.put("totalModelTypes", ModelType.values().length);
        stats.put("debugMode", debugMode);

        Map<String, Boolean> availability = new HashMap<>();
        for (ModelType type : ModelType.values()) {
            availability.put(type.name(), isServiceAvailable(type));
        }
        stats.put("serviceAvailability", availability);

        return stats;
    }
}
