package com.hujun.aicodehelper.ai.rag;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hujun.aicodehelper.ai.model.MathRagKnowledgeStore;
import com.hujun.aicodehelper.ai.model.PostgresVectorStore;
import com.hujun.aicodehelper.ai.utils.DocumentHashUtil;

import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.document.splitter.DocumentByParagraphSplitter;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.EmbeddingStoreIngestor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

/**
 * 加载 RAG
 */
@Slf4j
@Configuration
public class RagConfig {

    @Value("${rag.math-knowledge.enabled:true}")
    private boolean mathKnowledgeEnabled;

    @Resource
    private EmbeddingModel embeddingModel;

    @Resource
    private EmbeddingStore<TextSegment> embeddingStore;

    @Resource
    private javax.sql.DataSource dataSource;

    @Bean
    public ContentRetriever contentRetriever() {
        log.info("=== 开始初始化 RAG 知识库 ===");

        // 检查向量存储是否为PostgresVectorStore
        PostgresVectorStore postgresStore = null;
        if (embeddingStore instanceof PostgresVectorStore) {
            postgresStore = (PostgresVectorStore) embeddingStore;
        }

        // 检查现有向量数据
        long existingVectorCount = postgresStore != null ? postgresStore.getVectorCount() : 0;
        log.info("📊 通用向量表中现有向量数量: {}", existingVectorCount);

        // 处理数学知识库数据（存储到 math_rag_knowledge 表）
        if (mathKnowledgeEnabled) {
            processMathKnowledgeData();
        }

        // 加载JSON文档
        log.info("📄 正在从JSON文件加载文档...");
        List<Document> allDocuments = loadJsonDocuments();
        log.info("✅ 发现 {} 篇JSON文档", allDocuments.size());

        // 增量更新：过滤出需要处理的文档
        List<Document> documentsToProcess = new ArrayList<>();
        int skippedCount = 0;

        if (postgresStore != null && existingVectorCount > 0) {
            log.info("🔍 正在检查哪些文档需要重新处理...");
            List<String> existingFiles = postgresStore.getExistingFileNames();
            log.info("📋 数据库中已存在 {} 个文件的向量数据", existingFiles.size());

            for (Document doc : allDocuments) {
                String fileName = doc.metadata().getString("file_name");
                String contentHash = DocumentHashUtil.calculateContentHash(doc.text());

                // 添加内容哈希到文档元数据
                if (contentHash != null) {
                    Map<String, Object> metadataMap = new HashMap<>(doc.metadata().toMap());
                    metadataMap.put("content_hash", contentHash);
                    Metadata newMetadata = Metadata.from(metadataMap);
                    doc = Document.from(doc.text(), newMetadata);
                }

                // 检查是否需要处理
                boolean shouldProcess = false;
                if (fileName == null) {
                    shouldProcess = true; // 无文件名的文档总是处理
                } else if (!existingFiles.contains(fileName)) {
                    shouldProcess = true; // 新文件
                } else if (contentHash != null && !postgresStore.hasVectorForContentHash(contentHash)) {
                    // 文件存在但内容已变化，需要删除旧数据并重新处理
                    log.info("🔄 检测到文件内容变化，重新处理: {}", fileName);
                    postgresStore.removeVectorsForFile(fileName);
                    shouldProcess = true;
                }

                if (shouldProcess) {
                    documentsToProcess.add(doc);
                    log.debug("✅ 需要处理: {}", fileName != null ? fileName : "未知文件");
                } else {
                    skippedCount++;
                    log.debug("⏭️ 跳过已处理: {}", fileName);
                }
            }
        } else {
            // 首次运行或内存存储，处理所有文档
            documentsToProcess = allDocuments;
            // 为所有文档添加内容哈希
            for (int i = 0; i < documentsToProcess.size(); i++) {
                Document doc = documentsToProcess.get(i);
                String contentHash = DocumentHashUtil.calculateContentHash(doc.text());
                if (contentHash != null) {
                    Map<String, Object> metadataMap = new HashMap<>(doc.metadata().toMap());
                    metadataMap.put("content_hash", contentHash);
                    Metadata newMetadata = Metadata.from(metadataMap);
                    documentsToProcess.set(i, Document.from(doc.text(), newMetadata));
                }
            }
        }

        log.info("📚 总文档数: {}, 需要处理: {}, 跳过: {}",
                allDocuments.size(), documentsToProcess.size(), skippedCount);

        // 只有需要处理的文档才进行向量化
        if (!documentsToProcess.isEmpty()) {
            // 打印需要处理的文档信息
            for (int i = 0; i < documentsToProcess.size(); i++) {
                Document doc = documentsToProcess.get(i);
                String fileName = doc.metadata().getString("file_name");
                int contentLength = doc.text().length();
                log.info("📄 待处理文档 {}: {} (长度: {} 字符)", i + 1, fileName, contentLength);
            }

            // 文档切割
            log.info("✂️ 正在切割文档，最大片段长度: 1000字符，重叠: 200字符");
            DocumentByParagraphSplitter paragraphSplitter = new DocumentByParagraphSplitter(1000, 200);

            log.info("🔧 正在构建向量存储摄取器...");
            EmbeddingStoreIngestor ingestor = EmbeddingStoreIngestor.builder()
                    .documentSplitter(paragraphSplitter)
                    // 为了提高搜索质量，为每个 TextSegment 添加文档名称和哈希
                    .textSegmentTransformer(textSegment -> {
                        String fileName = textSegment.metadata().getString("file_name");
                        String contentHash = textSegment.metadata().getString("content_hash");
                        String originalText = textSegment.text();

                        // 检查文本内容是否为空或只包含空白字符
                        if (originalText == null || originalText.trim().isEmpty()) {
                            log.warn("⚠️ 发现空文本片段，跳过处理: {}", fileName);
                            return null; // 返回null会被过滤掉
                        }

                        String transformedText = (fileName != null ? fileName + "\n" : "") + originalText.trim();
                        log.debug("📝 转换文档片段: {} (原长度: {} -> 新长度: {})",
                                fileName, originalText.length(), transformedText.length());

                        // 保留元数据中的内容哈希
                        Map<String, Object> metadataMap = new HashMap<>(textSegment.metadata().toMap());
                        if (contentHash != null) {
                            metadataMap.put("content_hash", contentHash);
                        }
                        Metadata newMetadata = Metadata.from(metadataMap);

                        return TextSegment.from(transformedText, newMetadata);
                    })
                    // 使用指定的向量模型
                    .embeddingModel(embeddingModel)
                    .embeddingStore(embeddingStore)
                    .build();

            // 处理文档
            log.info("🚀 开始向量化处理 {} 篇文档，使用当前配置的嵌入模型", documentsToProcess.size());
            long startTime = System.currentTimeMillis();
            ingestor.ingest(documentsToProcess);
            long endTime = System.currentTimeMillis();
            log.info("✅ 向量化完成！耗时: {} 毫秒", endTime - startTime);
        } else {
            log.info("✅ 所有文档都已存在向量数据，跳过向量化处理");
        }

        // 构建内容检索器
        log.info("🔍 正在构建内容检索器...");
        ContentRetriever contentRetriever = EmbeddingStoreContentRetriever.builder()
                .embeddingStore(embeddingStore)
                .embeddingModel(embeddingModel)
                .maxResults(5) // 最多 5 个检索结果
                .minScore(0.75) // 过滤掉分数小于 0.75 的结果
                .build();

        // 最终统计
        long finalVectorCount = postgresStore != null ? postgresStore.getVectorCount() : 0;
        log.info("🎉 RAG 知识库初始化完成！");
        log.info("=== RAG 配置信息 ===");
        log.info("📚 总文档数量: {}", allDocuments.size());
        log.info("🔄 已处理文档: {}", documentsToProcess.size());
        log.info("⏭️ 跳过文档: {}", skippedCount);
        log.info("📊 向量数据总量: {}", finalVectorCount);
        log.info("🔍 最大检索结果: 5");
        log.info("📊 最小相似度分数: 0.75");
        log.info("🤖 嵌入模型: 当前配置的模型");
        log.info("========================");

        return contentRetriever;
    }

    /**
     * 从JSON文件中加载文档（基于math-schema.json结构）
     */
    private List<Document> loadJsonDocuments() {
        List<Document> documents = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();

        // 指定JSON数据目录路径
        String jsonDirectoryPath = "data";
        Path rootPath = Paths.get(jsonDirectoryPath);

        if (!Files.exists(rootPath) || !Files.isDirectory(rootPath)) {
            log.warn("⚠️ JSON数据目录不存在: {}, 跳过加载", jsonDirectoryPath);
            return documents;
        }

        int totalFiles = 0;
        int successfulFiles = 0;
        int totalItems = 0;

        try (Stream<Path> paths = Files.walk(rootPath)) {
            List<Path> jsonFiles = paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().toLowerCase().endsWith(".json"))
                    .toList();

            log.info("📁 找到 {} 个JSON文件", jsonFiles.size());

            for (Path jsonFile : jsonFiles) {
                totalFiles++;
                log.debug("🔍 处理JSON文件: {}", jsonFile);

                try {
                    // 读取JSON文件内容
                    String jsonContent = Files.readString(jsonFile);

                    if (jsonContent != null && !jsonContent.trim().isEmpty()) {
                        // 解析JSON数组
                        JsonNode rootNode = objectMapper.readTree(jsonContent);

                        if (rootNode.isArray()) {
                            // 处理JSON数组中的每个项目
                            for (JsonNode itemNode : rootNode) {
                                Document document = processJsonItem(itemNode, jsonFile);
                                if (document != null) {
                                    documents.add(document);
                                    totalItems++;
                                }
                            }
                        } else {
                            // 处理单个JSON对象
                            Document document = processJsonItem(rootNode, jsonFile);
                            if (document != null) {
                                documents.add(document);
                                totalItems++;
                            }
                        }

                        successfulFiles++;
                        log.debug("✅ 成功加载JSON: {} (包含 {} 个项目)",
                                jsonFile.getFileName(), rootNode.isArray() ? rootNode.size() : 1);
                    } else {
                        log.warn("⚠️ JSON文件内容为空: {}", jsonFile.getFileName());
                    }

                } catch (Exception e) {
                    log.error("❌ 读取JSON文件失败: {}, 错误: {}", jsonFile, e.getMessage());
                }
            }

        } catch (IOException e) {
            log.error("❌ 遍历JSON目录失败: {}", e.getMessage());
        }

        log.info("📊 JSON文件扫描完成 - 总文件: {}, 成功加载: {}, 总项目: {}", totalFiles, successfulFiles, totalItems);
        return documents;
    }

    /**
     * 处理单个JSON项目，转换为Document对象
     */
    private Document processJsonItem(JsonNode itemNode, Path jsonFile) {
        try {
            // 提取必需字段
            String id = getJsonString(itemNode, "id");
            String type = getJsonString(itemNode, "type");
            String content = getJsonString(itemNode, "content");
            String topic = getJsonString(itemNode, "topic");
            String grade = getJsonString(itemNode, "grade");

            // 检查必需字段
            if (id == null || type == null || content == null || topic == null || grade == null) {
                log.warn("⚠️ JSON项目缺少必需字段，跳过: {}", itemNode.toString());
                return null;
            }

            // 提取可选字段
            String answer = getJsonString(itemNode, "answer");
            String latex = getJsonString(itemNode, "latex");
            String sympyExpr = getJsonString(itemNode, "sympy_expr");
            String graph = getJsonString(itemNode, "graph");
            String difficulty = getJsonString(itemNode, "difficulty");
            String source = getJsonString(itemNode, "source");

            // 构建文档内容
            StringBuilder documentContent = new StringBuilder();
            documentContent.append("ID: ").append(id).append("\n");
            documentContent.append("类型: ").append(type).append("\n");
            documentContent.append("主题: ").append(topic).append("\n");
            documentContent.append("年级: ").append(grade).append("\n");
            if (difficulty != null) {
                documentContent.append("难度: ").append(difficulty).append("\n");
            }
            if (source != null) {
                documentContent.append("来源: ").append(source).append("\n");
            }
            documentContent.append("\n内容: ").append(content);

            if (answer != null) {
                documentContent.append("\n\n答案: ").append(answer);
            }

            if (latex != null) {
                documentContent.append("\n\nLaTeX公式: ").append(latex);
            }

            if (sympyExpr != null) {
                documentContent.append("\n\nSymPy表达式: ").append(sympyExpr);
            }

            if (graph != null) {
                documentContent.append("\n\n图形: ").append(graph);
            }

            // 处理图片信息
            JsonNode imagesNode = itemNode.get("images");
            if (imagesNode != null && imagesNode.isArray()) {
                documentContent.append("\n\n图片信息:");
                for (JsonNode imageNode : imagesNode) {
                    String caption = getJsonString(imageNode, "caption");
                    String description = getJsonString(imageNode, "description");
                    if (caption != null) {
                        documentContent.append("\n- 图片标题: ").append(caption);
                    }
                    if (description != null) {
                        documentContent.append("\n- 图片描述: ").append(description);
                    }
                }
            }

            // 处理解题步骤
            JsonNode stepsNode = itemNode.get("steps");
            if (stepsNode != null && stepsNode.isArray() && stepsNode.size() > 0) {
                documentContent.append("\n\n解题步骤:");
                for (int i = 0; i < stepsNode.size(); i++) {
                    JsonNode stepNode = stepsNode.get(i);
                    documentContent.append("\n").append(i + 1).append(". ").append(stepNode.asText());
                }
            }

            // 创建元数据
            Map<String, Object> metadataMap = new HashMap<>();
            metadataMap.put("file_name", jsonFile.getFileName().toString());
            metadataMap.put("file_path", jsonFile.toString());
            metadataMap.put("file_type", "JSON");
            metadataMap.put("source_type", "math_data");
            metadataMap.put("math_id", id);
            metadataMap.put("math_type", type);
            metadataMap.put("math_topic", topic);
            metadataMap.put("math_grade", grade);
            if (difficulty != null) {
                metadataMap.put("math_difficulty", difficulty);
            }
            if (source != null) {
                metadataMap.put("math_source", source);
            }

            // 不再添加原始JSON数据，避免存储空间浪费和JSON转义问题

            Metadata metadata = Metadata.from(metadataMap);

            // 创建Document对象
            Document document = Document.from(documentContent.toString().trim(), metadata);

            log.debug("✅ 成功处理JSON项目: {} (类型: {}, 主题: {})", id, type, topic);
            return document;

        } catch (Exception e) {
            log.error("❌ 处理JSON项目失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 安全地从JsonNode中获取字符串值
     */
    private String getJsonString(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return null;
        }
        return fieldNode.asText();
    }

    /**
     * 处理数学知识库数据，存储到 math_rag_knowledge 表
     */
    private void processMathKnowledgeData() {
        log.info("🎓 正在处理数学知识库数据...");

        try {
            MathRagKnowledgeStore mathStore = new MathRagKnowledgeStore(dataSource, embeddingModel);

            // 检查现有数学知识向量数据
            long existingMathVectorCount = mathStore.getVectorCount();
            log.info("📊 math_rag_knowledge表中现有向量数量: {}", existingMathVectorCount);

            // 加载JSON文档
            List<Document> allDocuments = loadJsonDocuments();
            log.info("📄 发现 {} 篇数学文档", allDocuments.size());

            // 增量更新：过滤出需要处理的文档
            List<Document> documentsToProcess = new ArrayList<>();
            int skippedCount = 0;

            if (existingMathVectorCount > 0) {
                log.info("🔍 正在检查哪些数学文档需要重新处理...");
                List<String> existingFiles = mathStore.getExistingFileNames();
                log.info("📋 math_rag_knowledge表中已存在 {} 个文件的向量数据", existingFiles.size());

                for (Document doc : allDocuments) {
                    String fileName = doc.metadata().getString("file_name");
                    String contentHash = DocumentHashUtil.calculateContentHash(doc.text());

                    // 添加内容哈希到文档元数据
                    if (contentHash != null) {
                        Map<String, Object> metadataMap = new HashMap<>(doc.metadata().toMap());
                        metadataMap.put("content_hash", contentHash);
                        Metadata newMetadata = Metadata.from(metadataMap);
                        doc = Document.from(doc.text(), newMetadata);
                    }

                    // 检查是否需要处理
                    boolean shouldProcess = false;
                    if (fileName == null) {
                        shouldProcess = true; // 无文件名的文档总是处理
                    } else if (!existingFiles.contains(fileName)) {
                        shouldProcess = true; // 新文件
                    } else if (contentHash != null && !mathStore.hasVectorForContentHash(contentHash)) {
                        // 文件存在但内容已变化，需要删除旧数据并重新处理
                        log.info("🔄 检测到数学文件内容变化，重新处理: {}", fileName);
                        mathStore.removeMathKnowledgeForFile(fileName);
                        shouldProcess = true;
                    }

                    if (shouldProcess) {
                        documentsToProcess.add(doc);
                        log.debug("✅ 需要处理数学文档: {}", fileName != null ? fileName : "未知文件");
                    } else {
                        skippedCount++;
                        log.debug("⏭️ 跳过已处理的数学文档: {}", fileName);
                    }
                }
            } else {
                // 首次运行，处理所有文档
                documentsToProcess = allDocuments;
                // 为所有文档添加内容哈希
                for (int i = 0; i < documentsToProcess.size(); i++) {
                    Document doc = documentsToProcess.get(i);
                    String contentHash = DocumentHashUtil.calculateContentHash(doc.text());
                    if (contentHash != null) {
                        Map<String, Object> metadataMap = new HashMap<>(doc.metadata().toMap());
                        metadataMap.put("content_hash", contentHash);
                        Metadata newMetadata = Metadata.from(metadataMap);
                        documentsToProcess.set(i, Document.from(doc.text(), newMetadata));
                    }
                }
            }

            log.info("📚 数学文档总数: {}, 需要处理: {}, 跳过: {}",
                    allDocuments.size(), documentsToProcess.size(), skippedCount);

            // 只有需要处理的文档才进行向量化到数学知识表
            if (!documentsToProcess.isEmpty()) {
                // 打印需要处理的文档信息
                for (int i = 0; i < documentsToProcess.size(); i++) {
                    Document doc = documentsToProcess.get(i);
                    String fileName = doc.metadata().getString("file_name");
                    int contentLength = doc.text().length();
                    log.info("📄 待处理的数学文档 {}: {} (长度: {} 字符)", i + 1, fileName, contentLength);
                }

                // 处理数学文档并存储到 math_rag_knowledge 表
                log.info("🚀 开始向量化数学文档到 math_rag_knowledge 表，使用当前配置的嵌入模型", documentsToProcess.size());
                long startTime = System.currentTimeMillis();

                for (Document doc : documentsToProcess) {
                    processMathDocument(doc, mathStore);
                }

                long endTime = System.currentTimeMillis();
                log.info("✅ 数学文档向量化完成！耗时: {} 毫秒", endTime - startTime);
            } else {
                log.info("✅ 所有数学文档都已存在向量数据，跳过向量化处理");
            }

            // 最终统计
            long finalMathVectorCount = mathStore.getVectorCount();
            log.info("🎉 数学知识库处理完成！");
            log.info("📊 math_rag_knowledge表向量总量: {}", finalMathVectorCount);

        } catch (Exception e) {
            log.error("❌ 处理数学知识库数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理单个数学文档并存储到 math_rag_knowledge 表
     */
    private void processMathDocument(Document doc, MathRagKnowledgeStore mathStore) {
        try {
            // 提取必需字段
            String id = doc.metadata().getString("math_id");
            String type = doc.metadata().getString("math_type");
            String content = doc.text();
            String topic = doc.metadata().getString("math_topic");
            String grade = doc.metadata().getString("math_grade");

            // 检查必需字段
            if (id == null || type == null || content == null || topic == null || grade == null) {
                log.warn("⚠️ 数学文档缺少必需字段，跳过: {}", doc.metadata().toMap());
                return;
            }

            // 提取可选字段
            String answer = extractFieldFromContent(content, "答案:");
            String latex = extractFieldFromContent(content, "LaTeX公式:");
            String sympyExpr = extractFieldFromContent(content, "SymPy表达式:");
            String graph = extractFieldFromContent(content, "图形:");
            String difficulty = doc.metadata().getString("math_difficulty");
            String source = doc.metadata().getString("math_source");

            // 处理图片信息
            String imagesJson = extractImagesJson(doc);

            // 处理解题步骤
            String stepsJson = extractStepsJson(content);

            // 创建元数据JSON
            String metadataJson = createMetadataJson(doc);

            // 存储到数学知识库
            mathStore.addMathKnowledge(id, type, content, topic, grade, answer, latex,
                                     sympyExpr, graph, difficulty, source, imagesJson, stepsJson, metadataJson);

            log.debug("✅ 数学文档处理完成: {} (类型: {}, 主题: {})", id, type, topic);

        } catch (Exception e) {
            log.error("❌ 处理数学文档失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从文档内容中提取字段值
     */
    private String extractFieldFromContent(String content, String fieldPrefix) {
        int startIndex = content.indexOf(fieldPrefix);
        if (startIndex == -1) return null;

        int valueStart = startIndex + fieldPrefix.length();
        int endIndex = content.indexOf('\n', valueStart);
        if (endIndex == -1) endIndex = content.length();

        String value = content.substring(valueStart, endIndex).trim();
        return value.isEmpty() ? null : value;
    }

    /**
     * 提取图片信息为JSON格式
     */
    private String extractImagesJson(Document doc) {
        // 这里可以根据实际需求实现图片信息的提取
        // 目前返回空数组
        return "[]";
    }

    /**
     * 提取解题步骤为JSON格式
     */
    private String extractStepsJson(String content) {
        // 这里可以根据实际需求实现解题步骤的提取
        // 目前返回空数组
        return "[]";
    }

    /**
     * 创建元数据JSON
     */
    private String createMetadataJson(Document doc) {
        Map<String, Object> metadata = new HashMap<>(doc.metadata().toMap());
        try {
            return new ObjectMapper().writeValueAsString(metadata);
        } catch (Exception e) {
            log.warn("创建元数据JSON失败: {}", e.getMessage());
            return "{}";
        }
    }
}
