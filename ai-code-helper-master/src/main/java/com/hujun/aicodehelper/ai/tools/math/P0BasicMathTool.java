package com.hujun.aicodehelper.ai.tools.math;

import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * P0 级别数学工具：基础代数运算
 * 涵盖：一元方程/方程组、因式分解、化简、展开、分式运算、不等式求解
 */
@Slf4j
@Component
public class P0BasicMathTool extends BaseMathTool {

    /**
     * 求解一元方程
     * 例如：x^2 + 2*x - 3 = 0
     */
    @Tool(name = "solveEquation", value = """
            求解一元方程。支持线性方程、二次方程及简单高次方程。
            输入格式：方程式（使用 = 连接等号两边）
            示例：x^2 + 2*x - 3 = 0, 2*x + 5 = 11
            """)
    public String solveEquation(@P("要求解的一元方程，如 'x^2 + 2*x - 3 = 0'") String equation) {
        log.info("🔍 求解一元方程: {}", equation);
        
        if (!isSafeExpression(equation)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            // 转换为 Symja 的 Solve 函数格式
            String symjaExpression = String.format("Solve(%s, x)", equation);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatEquationSolution(equation, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("求解方程时发生错误", e);
            return "❌ 方程求解失败：" + e.getMessage();
        }
    }

    /**
     * 求解方程组
     * 例如：{x + y = 5, x - y = 1}
     */
    @Tool(name = "solveSystem", value = """
            求解线性方程组。支持2-3个未知数的线性方程组。
            输入格式：用大括号包围方程组，方程间用逗号分隔
            示例：{x + y = 5, x - y = 1}, {2*x + y = 7, x - y = 1}
            """)
    public String solveSystem(@P("方程组，如 '{x + y = 5, x - y = 1}'") String system) {
        log.info("🔍 求解方程组: {}", system);
        
        if (!isSafeExpression(system)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            // 转换为 Symja 的 Solve 函数格式，自动检测变量
            String symjaExpression = String.format("Solve(%s, {x, y})", system);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatSystemSolution(system, result.getResult());
            } else {
                // 尝试三变量求解
                symjaExpression = String.format("Solve(%s, {x, y, z})", system);
                result = evaluateExpression(symjaExpression);
                if (result.isSuccess()) {
                    return formatSystemSolution(system, result.getResult());
                }
                return result.getError();
            }
        } catch (Exception e) {
            log.error("求解方程组时发生错误", e);
            return "❌ 方程组求解失败：" + e.getMessage();
        }
    }

    /**
     * 因式分解
     * 例如：x^2 + 5*x + 6
     */
    @Tool(name = "factorExpression", value = """
            对多项式进行因式分解。支持常见的二次式、三次式等多项式因式分解。
            输入格式：多项式表达式
            示例：x^2 + 5*x + 6, x^3 - 8, a^2 - b^2
            """)
    public String factorExpression(@P("需要因式分解的多项式，如 'x^2 + 5*x + 6'") String expression) {
        log.info("🧮 因式分解: {}", expression);
        
        if (!isSafeExpression(expression)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String symjaExpression = String.format("Factor(%s)", expression);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatFactorResult(expression, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("因式分解时发生错误", e);
            return "❌ 因式分解失败：" + e.getMessage();
        }
    }

    /**
     * 表达式化简
     * 例如：(x^2 + 2*x + 1)/(x + 1)
     */
    @Tool(name = "simplifyExpression", value = """
            化简数学表达式。包括分式化简、根式化简、三角函数化简等。
            输入格式：数学表达式
            示例：(x^2 + 2*x + 1)/(x + 1), sqrt(8), sin(x)^2 + cos(x)^2
            """)
    public String simplifyExpression(@P("需要化简的数学表达式") String expression) {
        log.info("✨ 化简表达式: {}", expression);
        
        if (!isSafeExpression(expression)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String symjaExpression = String.format("Simplify(%s)", expression);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatSimplificationResult(expression, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("化简表达式时发生错误", e);
            return "❌ 表达式化简失败：" + e.getMessage();
        }
    }

    /**
     * 表达式展开
     * 例如：(x + 1)^2, (a + b)(c + d)
     */
    @Tool(name = "expandExpression", value = """
            展开数学表达式。包括多项式展开、幂次展开等。
            输入格式：包含括号或幂次的数学表达式
            示例：(x + 1)^2, (a + b)(c + d), (x + y + z)^3
            """)
    public String expandExpression(@P("需要展开的数学表达式，如 '(x + 1)^2'") String expression) {
        log.info("📐 展开表达式: {}", expression);
        
        if (!isSafeExpression(expression)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String symjaExpression = String.format("Expand(%s)", expression);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatExpansionResult(expression, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("展开表达式时发生错误", e);
            return "❌ 表达式展开失败：" + e.getMessage();
        }
    }

    /**
     * 求解一次不等式
     * 例如：2*x + 3 > 7, x^2 - 4 >= 0
     */
    @Tool(name = "solveInequality", value = """
            求解不等式。支持一次不等式和简单的二次不等式。
            输入格式：不等式（使用 >, <, >=, <= 符号）
            示例：2*x + 3 > 7, x^2 - 4 >= 0, |x - 1| < 3
            """)
    public String solveInequality(@P("不等式，如 '2*x + 3 > 7'") String inequality) {
        log.info("📊 求解不等式: {}", inequality);
        
        if (!isSafeExpression(inequality)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String symjaExpression = String.format("Solve(%s, x, Reals)", inequality);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatInequalityResult(inequality, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("求解不等式时发生错误", e);
            return "❌ 不等式求解失败：" + e.getMessage();
        }
    }

    /**
     * 表达式求值
     * 例如：在 x=2 时计算 x^2 + 3*x + 1 的值
     */
    @Tool(name = "evaluateAt", value = """
            在指定变量值处计算表达式的数值。
            输入格式：表达式;变量=值 或 表达式;{变量1=值1,变量2=值2}
            示例：x^2 + 3*x + 1;x=2, a^2 + b^2;{a=3,b=4}
            """)
    public String evaluateAt(@P("表达式和变量赋值，如 'x^2 + 3*x + 1;x=2'") String expressionAndValues) {
        log.info("🎯 表达式求值: {}", expressionAndValues);
        
        if (!isSafeExpression(expressionAndValues)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = expressionAndValues.split(";");
            if (parts.length != 2) {
                return "❌ 格式错误，请使用 '表达式;变量=值' 的格式";
            }
            
            String expression = parts[0].trim();
            String substitution = parts[1].trim();
            
            // 处理单变量赋值
            if (substitution.contains("=") && !substitution.contains("{")) {
                substitution = "{" + substitution + "}";
            }
            
            String symjaExpression = String.format("ReplaceAll(%s, %s)", expression, substitution);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                // 尝试数值化计算
                String numericExpression = String.format("N(%s)", result.getResult());
                MathResult numericResult = evaluateExpression(numericExpression);
                
                return formatEvaluationResult(expression, substitution, 
                    numericResult.isSuccess() ? numericResult.getResult() : result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("表达式求值时发生错误", e);
            return "❌ 表达式求值失败：" + e.getMessage();
        }
    }

    // === 格式化输出方法 ===

    private String formatEquationSolution(String equation, String solution) {
        return String.format("""
            📝 **一元方程求解**
            原方程：%s
            解：%s
            
            💡 可以将解代入原方程进行验证。
            """, equation, solution);
    }

    private String formatSystemSolution(String system, String solution) {
        return String.format("""
            📝 **方程组求解**
            原方程组：%s
            解：%s
            
            💡 可以将解代入每个方程进行验证。
            """, system, solution);
    }

    private String formatFactorResult(String original, String factored) {
        if (original.equals(factored)) {
            return String.format("""
                📝 **因式分解**
                原表达式：%s
                结果：无法进一步因式分解（可能已经是最简形式）
                
                💡 该表达式可能是素多项式或需要在更大的数域内分解。
                """, original);
        } else {
            return String.format("""
                📝 **因式分解**
                原表达式：%s
                分解结果：%s
                
                💡 可以通过展开分解结果来验证正确性。
                """, original, factored);
        }
    }

    private String formatSimplificationResult(String original, String simplified) {
        if (original.equals(simplified)) {
            return String.format("""
                📝 **表达式化简**
                原表达式：%s
                结果：已经是最简形式
                
                💡 该表达式已经无法进一步化简。
                """, original);
        } else {
            return String.format("""
                📝 **表达式化简**
                原表达式：%s
                化简结果：%s
                
                💡 化简后的表达式在数学上等价于原表达式。
                """, original, simplified);
        }
    }

    private String formatExpansionResult(String original, String expanded) {
        return String.format("""
            📝 **表达式展开**
            原表达式：%s
            展开结果：%s
            
            💡 展开后便于进行进一步的代数运算。
            """, original, expanded);
    }

    private String formatInequalityResult(String inequality, String solution) {
        return String.format("""
            📝 **不等式求解**
            原不等式：%s
            解集：%s
            
            💡 解集表示所有满足不等式的 x 的取值范围。
            """, inequality, solution);
    }

    private String formatEvaluationResult(String expression, String substitution, String value) {
        return String.format("""
            📝 **表达式求值**
            原表达式：%s
            代入条件：%s
            计算结果：%s
            
            💡 这是在给定条件下表达式的具体数值。
            """, expression, substitution, value);
    }
}
