package com.hujun.aicodehelper.ai.model;

import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.chat.listener.ChatModelListener;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import dev.langchain4j.model.googleai.GoogleAiGeminiStreamingChatModel;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多模型Gemini配置
 * 支持根据不同使用场景创建相应的模型实例
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "langchain4j.google.ai.gemini")
@Data
public class MultiGeminiModelConfig {

    /**
     * 单个模型配置
     */
    @Data
    public static class ModelConfig {
        private String apiKey;
        private String modelName;
        private String description;
        private Double temperature = 0.7;
        private Integer maxTokens = 4096;
    }

    /**
     * 默认聊天模型配置（保持兼容性）
     */
    @Data
    public static class ChatModelConfig {
        private String apiKey;
        private String modelName;
    }

    private ChatModelConfig chatModel;
    private Map<String, ModelConfig> models = new HashMap<>();

    @Resource
    private ChatModelListener chatModelListener;

    // 存储各个模型的ChatModel实例
    private Map<ModelType, ChatModel> chatModels = new HashMap<>();
    private Map<ModelType, StreamingChatModel> streamingChatModels = new HashMap<>();

    @PostConstruct
    public void initModels() {
        log.info("正在初始化多模型配置...");
        
        // 初始化所有配置的模型
        for (ModelType modelType : ModelType.values()) {
            String configKey = modelType.getConfigKey();
            ModelConfig config = models.get(configKey);
            
            if (config != null) {
                createModelsForType(modelType, config);
                log.info("已初始化模型 [{}]: {} - {}", 
                    modelType.name(), config.getModelName(), config.getDescription());
            } else {
                log.warn("未找到模型类型 [{}] 的配置，使用默认配置", modelType.name());
                createDefaultModelForType(modelType);
            }
        }
        
        log.info("多模型配置初始化完成，共初始化 {} 个模型", chatModels.size());
    }

    /**
     * 为指定模型类型创建模型实例
     */
    private void createModelsForType(ModelType modelType, ModelConfig config) {
        try {
            // 创建ChatModel
            ChatModel chatModel = GoogleAiGeminiChatModel.builder()
                    .apiKey(config.getApiKey())
                    .modelName(config.getModelName())
                    .temperature(config.getTemperature())
                    .maxOutputTokens(config.getMaxTokens())
                    .listeners(List.of(chatModelListener))
                    .build();
            chatModels.put(modelType, chatModel);

            // 创建StreamingChatModel
            StreamingChatModel streamingChatModel = GoogleAiGeminiStreamingChatModel.builder()
                    .apiKey(config.getApiKey())
                    .modelName(config.getModelName())
                    .temperature(config.getTemperature())
                    .maxOutputTokens(config.getMaxTokens())
                    .listeners(List.of(chatModelListener))
                    .build();
            streamingChatModels.put(modelType, streamingChatModel);
            
        } catch (Exception e) {
            log.error("创建模型 [{}] 失败: {}", modelType.name(), e.getMessage(), e);
            createDefaultModelForType(modelType);
        }
    }

    /**
     * 为指定模型类型创建默认模型实例（使用全局配置）
     */
    private void createDefaultModelForType(ModelType modelType) {
        if (chatModel == null || chatModel.getApiKey() == null) {
            log.error("默认聊天模型配置不存在，无法创建模型 [{}]", modelType.name());
            return;
        }

        try {
            // 使用默认配置创建模型
            ChatModel defaultChatModel = GoogleAiGeminiChatModel.builder()
                    .apiKey(chatModel.getApiKey())
                    .modelName(chatModel.getModelName())
                    .listeners(List.of(chatModelListener))
                    .build();
            chatModels.put(modelType, defaultChatModel);

            StreamingChatModel defaultStreamingChatModel = GoogleAiGeminiStreamingChatModel.builder()
                    .apiKey(chatModel.getApiKey())
                    .modelName(chatModel.getModelName())
                    .listeners(List.of(chatModelListener))
                    .build();
            streamingChatModels.put(modelType, defaultStreamingChatModel);
            
            log.info("已为模型类型 [{}] 创建默认配置实例", modelType.name());
            
        } catch (Exception e) {
            log.error("创建默认模型 [{}] 失败: {}", modelType.name(), e.getMessage(), e);
        }
    }

    /**
     * 获取指定类型的ChatModel
     */
    public ChatModel getChatModel(ModelType modelType) {
        ChatModel model = chatModels.get(modelType);
        if (model == null) {
            log.warn("未找到模型类型 [{}] 的ChatModel，返回默认模型", modelType.name());
            return chatModels.get(ModelType.LITE);
        }
        return model;
    }

    /**
     * 获取指定类型的StreamingChatModel
     */
    public StreamingChatModel getStreamingChatModel(ModelType modelType) {
        StreamingChatModel model = streamingChatModels.get(modelType);
        if (model == null) {
            log.warn("未找到模型类型 [{}] 的StreamingChatModel，返回默认模型", modelType.name());
            return streamingChatModels.get(ModelType.LITE);
        }
        return model;
    }

    /**
     * 获取所有可用的模型类型
     */
    public Map<ModelType, Boolean> getAvailableModels() {
        Map<ModelType, Boolean> availability = new HashMap<>();
        for (ModelType modelType : ModelType.values()) {
            availability.put(modelType, chatModels.containsKey(modelType) && 
                                      streamingChatModels.containsKey(modelType));
        }
        return availability;
    }

    /**
     * 获取模型配置信息
     */
    public Map<String, Object> getModelInfo() {
        Map<String, Object> info = new HashMap<>();
        
        Map<String, Object> modelDetails = new HashMap<>();
        for (ModelType modelType : ModelType.values()) {
            Map<String, Object> details = new HashMap<>();
            ModelConfig config = models.get(modelType.getConfigKey());
            
            if (config != null) {
                details.put("modelName", config.getModelName());
                details.put("description", config.getDescription());
                details.put("temperature", config.getTemperature());
                details.put("maxTokens", config.getMaxTokens());
                details.put("available", chatModels.containsKey(modelType));
            } else {
                // 使用默认聊天配置的模型名（如可用），否则用类型名占位
                String fallbackName = this.chatModel != null ? this.chatModel.getModelName() : modelType.name().toLowerCase();
                details.put("modelName", fallbackName);
                details.put("description", modelType.getDescription());
                details.put("available", false);
                details.put("note", "使用默认配置");
            }
            
            modelDetails.put(modelType.name().toLowerCase(), details);
        }
        
        info.put("models", modelDetails);
        info.put("totalModels", chatModels.size());
        info.put("configuredModels", models.size());
        
        return info;
    }

    // 为了保持兼容性，提供默认的Bean
    @Bean
    @Primary
    public ChatModel myGeminiChatModel() {
        // 返回LITE模型作为默认模型
        return getChatModel(ModelType.LITE);
    }

    @Bean
    @Primary
    public StreamingChatModel myGeminiStreamingChatModel() {
        // 返回LITE模型作为默认流式模型
        return getStreamingChatModel(ModelType.LITE);
    }
}
