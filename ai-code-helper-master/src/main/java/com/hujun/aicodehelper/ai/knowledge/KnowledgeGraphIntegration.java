package com.hujun.aicodehelper.ai.knowledge;

import com.hujun.aicodehelper.model.StructuredOutput.KnowledgeMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 知识图谱集成组件
 * 负责知识点映射和关联分析
 */
@Slf4j
@Component
public class KnowledgeGraphIntegration {

    @Value("${thinky.knowledge.graph.enabled:false}")
    private boolean knowledgeGraphEnabled;

    @Value("${thinky.knowledge.graph.endpoint:}")
    private String graphEndpoint;

    // 内置的知识图谱数据（简化版）
    private static final Map<String, KnowledgeNode> KNOWLEDGE_GRAPH = new ConcurrentHashMap<>();

    // 课程标准映射
    private static final Map<String, String> CURRICULUM_MAPPING = new HashMap<>();

    static {
        // 初始化基础知识图谱
        initializeBasicKnowledgeGraph();
        initializeCurriculumMapping();
    }

    /**
     * 将主题映射到知识图谱
     */
    public KnowledgeMapping mapToKnowledgeGraph(String topic, String curriculum) {
        log.debug("映射主题到知识图谱: {} (课程: {})", topic, curriculum);

        try {
            if (knowledgeGraphEnabled && graphEndpoint != null && !graphEndpoint.trim().isEmpty()) {
                // 如果配置了外部知识图谱服务，使用外部服务
                return queryExternalKnowledgeGraph(topic, curriculum);
            } else {
                // 使用内置知识图谱
                return queryInternalKnowledgeGraph(topic, curriculum);
            }
        } catch (Exception e) {
            log.warn("知识图谱映射失败，使用默认映射: {}", e.getMessage());
            return createDefaultMapping(topic);
        }
    }

    /**
     * 获取知识点的前置条件
     */
    public List<String> getPrerequisites(String concept) {
        KnowledgeNode node = KNOWLEDGE_GRAPH.get(concept.toLowerCase());
        return node != null ? node.getPrerequisites() : new ArrayList<>();
    }

    /**
     * 获取相关知识点
     */
    public List<String> getRelatedTopics(String concept) {
        KnowledgeNode node = KNOWLEDGE_GRAPH.get(concept.toLowerCase());
        return node != null ? node.getRelatedTopics() : new ArrayList<>();
    }

    /**
     * 获取知识点难度等级
     */
    public int getDifficultyLevel(String concept) {
        KnowledgeNode node = KNOWLEDGE_GRAPH.get(concept.toLowerCase());
        return node != null ? node.getDifficultyLevel() : 1;
    }

    /**
     * 查找知识断层
     */
    public List<String> findKnowledgeGaps(List<String> masteredConcepts, String targetConcept) {
        List<String> gaps = new ArrayList<>();
        
        List<String> prerequisites = getPrerequisites(targetConcept);
        for (String prerequisite : prerequisites) {
            if (!masteredConcepts.contains(prerequisite)) {
                gaps.add(prerequisite);
                // 递归查找前置条件的前置条件
                gaps.addAll(findKnowledgeGaps(masteredConcepts, prerequisite));
            }
        }
        
        return gaps;
    }

    // ========== 私有方法 ==========

    /**
     * 查询外部知识图谱服务
     */
    private KnowledgeMapping queryExternalKnowledgeGraph(String topic, String curriculum) {
        // TODO: 实现外部知识图谱查询
        log.info("查询外部知识图谱: {} -> {}", graphEndpoint, topic);
        
        // 这里应该调用外部API，现在先返回内置结果
        return queryInternalKnowledgeGraph(topic, curriculum);
    }

    /**
     * 查询内置知识图谱
     */
    private KnowledgeMapping queryInternalKnowledgeGraph(String topic, String curriculum) {
        String key = topic.toLowerCase();
        KnowledgeNode node = KNOWLEDGE_GRAPH.get(key);
        
        if (node == null) {
            // 尝试模糊匹配
            node = findSimilarNode(key);
        }
        
        if (node != null) {
            return KnowledgeMapping.builder()
                    .primaryConcepts(List.of(node.getName()))
                    .prerequisites(node.getPrerequisites())
                    .relatedTopics(node.getRelatedTopics())
                    .curriculumMapping(CURRICULUM_MAPPING.getOrDefault(key, "通用"))
                    .build();
        } else {
            return createDefaultMapping(topic);
        }
    }

    /**
     * 创建默认知识映射
     */
    private KnowledgeMapping createDefaultMapping(String topic) {
        return KnowledgeMapping.builder()
                .primaryConcepts(List.of(topic))
                .prerequisites(List.of("基础数学概念"))
                .relatedTopics(List.of("问题解决", "逻辑推理"))
                .curriculumMapping("通用")
                .build();
    }

    /**
     * 查找相似的知识节点
     */
    private KnowledgeNode findSimilarNode(String topic) {
        for (Map.Entry<String, KnowledgeNode> entry : KNOWLEDGE_GRAPH.entrySet()) {
            if (entry.getKey().contains(topic) || topic.contains(entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }

    /**
     * 初始化基础知识图谱
     */
    private static void initializeBasicKnowledgeGraph() {
        // 数学基础概念
        addKnowledgeNode("数字", List.of(), List.of("运算", "计数"), 1);
        addKnowledgeNode("加法", List.of("数字"), List.of("减法", "运算"), 1);
        addKnowledgeNode("减法", List.of("数字", "加法"), List.of("加法", "运算"), 1);
        addKnowledgeNode("乘法", List.of("加法"), List.of("除法", "倍数"), 2);
        addKnowledgeNode("除法", List.of("乘法"), List.of("乘法", "分数"), 2);
        
        // 代数概念
        addKnowledgeNode("代数", List.of("四则运算"), List.of("方程", "函数"), 3);
        addKnowledgeNode("方程", List.of("代数"), List.of("不等式", "函数"), 3);
        addKnowledgeNode("函数", List.of("代数", "方程"), List.of("图像", "导数"), 4);
        
        // 几何概念
        addKnowledgeNode("几何", List.of("基础图形"), List.of("面积", "体积"), 2);
        addKnowledgeNode("三角形", List.of("几何"), List.of("勾股定理", "面积"), 2);
        addKnowledgeNode("圆", List.of("几何"), List.of("周长", "面积", "扇形"), 3);
        
        // 物理概念
        addKnowledgeNode("物理", List.of("数学基础"), List.of("力", "运动"), 3);
        addKnowledgeNode("力", List.of("物理"), List.of("牛顿定律", "功"), 3);
        addKnowledgeNode("运动", List.of("物理"), List.of("速度", "加速度"), 3);
        addKnowledgeNode("速度", List.of("运动"), List.of("加速度", "位移"), 3);
        
        log.info("初始化基础知识图谱完成，节点数量: {}", KNOWLEDGE_GRAPH.size());
    }

    /**
     * 初始化课程标准映射
     */
    private static void initializeCurriculumMapping() {
        // 新加坡数学课程标准
        CURRICULUM_MAPPING.put("加法", "Singapore-Math-P1");
        CURRICULUM_MAPPING.put("减法", "Singapore-Math-P1");
        CURRICULUM_MAPPING.put("乘法", "Singapore-Math-P2");
        CURRICULUM_MAPPING.put("除法", "Singapore-Math-P2");
        CURRICULUM_MAPPING.put("方程", "Singapore-Math-S1");
        CURRICULUM_MAPPING.put("函数", "Singapore-Math-S2");
        
        // 中国数学课程标准
        CURRICULUM_MAPPING.put("数字", "China-Math-G1");
        CURRICULUM_MAPPING.put("几何", "China-Math-G3");
        CURRICULUM_MAPPING.put("代数", "China-Math-G7");
        
        log.info("初始化课程标准映射完成，映射数量: {}", CURRICULUM_MAPPING.size());
    }

    /**
     * 添加知识节点
     */
    private static void addKnowledgeNode(String name, List<String> prerequisites, 
                                       List<String> relatedTopics, int difficultyLevel) {
        KnowledgeNode node = KnowledgeNode.builder()
                .name(name)
                .prerequisites(new ArrayList<>(prerequisites))
                .relatedTopics(new ArrayList<>(relatedTopics))
                .difficultyLevel(difficultyLevel)
                .build();
        KNOWLEDGE_GRAPH.put(name.toLowerCase(), node);
    }

    /**
     * 知识节点数据类
     */
    public static class KnowledgeNode {
        private String name;
        private List<String> prerequisites;
        private List<String> relatedTopics;
        private int difficultyLevel;

        public static KnowledgeNodeBuilder builder() {
            return new KnowledgeNodeBuilder();
        }

        // Getters
        public String getName() { return name; }
        public List<String> getPrerequisites() { return prerequisites; }
        public List<String> getRelatedTopics() { return relatedTopics; }
        public int getDifficultyLevel() { return difficultyLevel; }

        public static class KnowledgeNodeBuilder {
            private String name;
            private List<String> prerequisites;
            private List<String> relatedTopics;
            private int difficultyLevel;

            public KnowledgeNodeBuilder name(String name) { this.name = name; return this; }
            public KnowledgeNodeBuilder prerequisites(List<String> prerequisites) { this.prerequisites = prerequisites; return this; }
            public KnowledgeNodeBuilder relatedTopics(List<String> relatedTopics) { this.relatedTopics = relatedTopics; return this; }
            public KnowledgeNodeBuilder difficultyLevel(int difficultyLevel) { this.difficultyLevel = difficultyLevel; return this; }

            public KnowledgeNode build() {
                KnowledgeNode node = new KnowledgeNode();
                node.name = this.name;
                node.prerequisites = this.prerequisites;
                node.relatedTopics = this.relatedTopics;
                node.difficultyLevel = this.difficultyLevel;
                return node;
            }
        }
    }
}
