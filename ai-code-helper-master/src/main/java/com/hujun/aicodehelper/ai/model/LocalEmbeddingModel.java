package com.hujun.aicodehelper.ai.model;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

/**
 * 本地嵌入模型实现
 * 支持通过 HTTP API 调用本地部署的向量模型
 */
@Slf4j
public class LocalEmbeddingModel implements EmbeddingModel {

    private final String baseUrl;
    private final String modelName;
    private final int dimension;
    private final RestTemplate restTemplate;

    public LocalEmbeddingModel(String baseUrl, String modelName, int dimension) {
        this.baseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        this.modelName = modelName;
        this.dimension = dimension;
        this.restTemplate = new RestTemplate();
        
        log.info("🤖 正在初始化本地嵌入模型...");
        log.info("📋 模型配置信息:");
        log.info("   - 服务地址: {}", this.baseUrl);
        log.info("   - 模型名称: {}", this.modelName);
        log.info("   - 向量维度: {}", this.dimension);
    }

    @Override
    public Response<Embedding> embed(TextSegment textSegment) {
        return embed(textSegment.text());
    }

    @Override
    public Response<Embedding> embed(String text) {
        log.debug("🚀 调用本地模型进行向量化: '{}'", text.substring(0, Math.min(100, text.length())) + "...");
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 构建请求体
            Map<String, Object> requestBody = Map.of(
                "input", text,
                "model", modelName
            );
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            // 调用本地API
            String url = baseUrl + "/v1/embeddings";
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            long endTime = System.currentTimeMillis();
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> data = (List<Map<String, Object>>) responseBody.get("data");
                
                if (data != null && !data.isEmpty()) {
                    List<Double> embeddingList = (List<Double>) data.get(0).get("embedding");
                    
                    // 转换为Float类型
                    List<Float> floatEmbedding = embeddingList.stream()
                            .map(Double::floatValue)
                            .toList();
                    
                    log.debug("✅ 本地模型向量化完成！耗时: {} 毫秒，向量维度: {}", 
                            endTime - startTime, floatEmbedding.size());
                    
                    Embedding embedding = Embedding.from(floatEmbedding);
                    return Response.from(embedding);
                } else {
                    log.error("❌ 本地模型返回数据格式错误: data 为空");
                    throw new RuntimeException("本地模型返回数据格式错误");
                }
            } else {
                log.error("❌ 本地模型调用失败，状态码: {}", response.getStatusCode());
                throw new RuntimeException("本地模型调用失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("❌ 调用本地嵌入模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用本地嵌入模型失败", e);
        }
    }

    @Override
    public Response<List<Embedding>> embedAll(List<TextSegment> textSegments) {
        log.debug("🚀 批量调用本地模型进行向量化，数量: {}", textSegments.size());
        
        List<String> texts = textSegments.stream()
                .map(TextSegment::text)
                .toList();
        
        return embedTexts(texts);
    }

    /**
     * 批量向量化文本列表
     */
    public Response<List<Embedding>> embedTexts(List<String> texts) {
        log.debug("🚀 批量调用本地模型进行向量化，数量: {}", texts.size());
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 构建请求体 - 支持批量处理
            Map<String, Object> requestBody = Map.of(
                "input", texts,
                "model", modelName
            );
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            // 调用本地API
            String url = baseUrl + "/v1/embeddings";
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            long endTime = System.currentTimeMillis();
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> data = (List<Map<String, Object>>) responseBody.get("data");
                
                if (data != null && !data.isEmpty()) {
                    List<Embedding> embeddings = data.stream()
                            .map(item -> {
                                List<Double> embeddingList = (List<Double>) item.get("embedding");
                                List<Float> floatEmbedding = embeddingList.stream()
                                        .map(Double::floatValue)
                                        .toList();
                                return Embedding.from(floatEmbedding);
                            })
                            .toList();
                    
                    log.debug("✅ 本地模型批量向量化完成！耗时: {} 毫秒，处理数量: {}", 
                            endTime - startTime, embeddings.size());
                    
                    return Response.from(embeddings);
                } else {
                    log.error("❌ 本地模型返回数据格式错误: data 为空");
                    throw new RuntimeException("本地模型返回数据格式错误");
                }
            } else {
                log.error("❌ 本地模型调用失败，状态码: {}", response.getStatusCode());
                throw new RuntimeException("本地模型调用失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("❌ 批量调用本地嵌入模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量调用本地嵌入模型失败", e);
        }
    }

    /**
     * 获取向量维度
     */
    public int getDimension() {
        return dimension;
    }

    /**
     * 获取模型名称
     */
    public String getModelName() {
        return modelName;
    }

    /**
     * 获取服务地址
     */
    public String getBaseUrl() {
        return baseUrl;
    }

    /**
     * 测试连接
     */
    public boolean testConnection() {
        try {
            log.info("🔍 正在测试本地嵌入模型连接...");
            Response<Embedding> response = embed("test connection");
            log.info("✅ 本地嵌入模型连接测试成功");
            return true;
        } catch (Exception e) {
            log.error("❌ 本地嵌入模型连接测试失败: {}", e.getMessage());
            return false;
        }
    }
} 