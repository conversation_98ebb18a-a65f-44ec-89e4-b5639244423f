package com.hujun.aicodehelper.ai.catalog;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;

/**
 * Composite catalog that dispatches to provider catalogs based on configuration.
 * Properties:
 *  - ai.catalog.text=gemini|modelscope|bigmodel
 *  - ai.catalog.multimodal=gemini|modelscope|bigmodel
 *  - ai.catalog.rag=gemini|modelscope|bigmodel
 *  - ai.catalog.dictionary=gemini|modelscope|bigmodel
 *  - ai.catalog.pro=gemini|modelscope|bigmodel
 */
@Component
public class CompositeCatalog implements ModelCatalog {

    private final ModelCatalog geminiCatalog;
    private final ModelCatalog modelScopeCatalog;
    private final ModelCatalog bigModelCatalog;
    private final Map<UseCase, String> route = new EnumMap<>(UseCase.class);

    public CompositeCatalog(
            GeminiCatalog geminiCatalog,
            ModelScopeCatalog modelScopeCatalog,
            BigModelCatalog bigModelCatalog,
            @Value("${ai.catalog.text:gemini}") String text,
            @Value("${ai.catalog.multimodal:bigmodel}") String multimodal,
            @Value("${ai.catalog.rag:gemini}") String rag,
            @Value("${ai.catalog.dictionary:gemini}") String dictionary,
            @Value("${ai.catalog.pro:gemini}") String pro) {
        this.geminiCatalog = geminiCatalog;
        this.modelScopeCatalog = modelScopeCatalog;
        this.bigModelCatalog = bigModelCatalog;
        route.put(UseCase.TEXT, text.toLowerCase());
        route.put(UseCase.MULTIMODAL, multimodal.toLowerCase());
        route.put(UseCase.RAG, rag.toLowerCase());
        route.put(UseCase.DICTIONARY, dictionary.toLowerCase());
        route.put(UseCase.PRO, pro.toLowerCase());
    }

    private ModelCatalog resolve(UseCase useCase) {
        String r = route.getOrDefault(useCase, "gemini");
        return switch (r) {
            case "modelscope" -> modelScopeCatalog;
            case "bigmodel" -> bigModelCatalog;
            default -> geminiCatalog;
        };
    }

    @Override
    public Optional<StreamingChatModel> getStreaming(UseCase useCase) {
        return resolve(useCase).getStreaming(useCase);
    }

    @Override
    public Optional<ChatModel> getChat(UseCase useCase) {
        return resolve(useCase).getChat(useCase);
    }

    @Override
    public String getEffectiveModelName(UseCase useCase) {
        return resolve(useCase).getEffectiveModelName(useCase);
    }
}
