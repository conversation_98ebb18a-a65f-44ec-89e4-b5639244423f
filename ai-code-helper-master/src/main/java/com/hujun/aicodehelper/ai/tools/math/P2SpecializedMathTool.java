package com.hujun.aicodehelper.ai.tools.math;

import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * P2 级别数学工具：专业数学领域
 * 涵盖：解析几何、数论、组合数学、概率统计
 */
@Slf4j
@Component
public class P2SpecializedMathTool extends BaseMathTool {

    /**
     * 直线方程分析
     * 例如：求两点间直线方程，点到直线距离等
     */
    @Tool(name = "analyzeLineEquation", value = """
            解析几何中的直线方程分析。支持多种直线相关计算。
            操作类型：
            - two_points: 根据两点求直线方程，格式：two_points;{x1,y1};{x2,y2}
            - point_distance: 点到直线距离，格式：point_distance;{x,y};ax+by+c=0
            - intersection: 两直线交点，格式：intersection;a1*x+b1*y+c1=0;a2*x+b2*y+c2=0
            """)
    public String analyzeLineEquation(@P("直线方程分析，如 'two_points;{1,2};{3,4}'") String input) {
        log.info("📐 直线方程分析: {}", input);
        
        if (!isSafeExpression(input)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = input.split(";");
            if (parts.length < 2) {
                return "❌ 格式错误";
            }
            
            String operation = parts[0].trim();
            
            switch (operation) {
                case "two_points":
                    if (parts.length != 3) return "❌ 两点直线需要格式: two_points;{x1,y1};{x2,y2}";
                    return calculateLineThroughTwoPoints(parts[1], parts[2]);
                    
                case "point_distance":
                    if (parts.length != 3) return "❌ 点到直线距离需要格式: point_distance;{x,y};ax+by+c=0";
                    return calculatePointToLineDistance(parts[1], parts[2]);
                    
                case "intersection":
                    if (parts.length != 3) return "❌ 两直线交点需要格式: intersection;line1;line2";
                    return calculateLineIntersection(parts[1], parts[2]);
                    
                default:
                    return "❌ 不支持的操作类型";
            }
        } catch (Exception e) {
            log.error("直线方程分析时发生错误", e);
            return "❌ 直线方程分析失败：" + e.getMessage();
        }
    }

    /**
     * 圆的方程分析
     * 例如：标准圆方程、圆与直线关系等
     */
    @Tool(name = "analyzeCircleEquation", value = """
            解析几何中的圆相关计算。
            操作类型：
            - standard: 根据圆心和半径求标准方程，格式：standard;{h,k};r
            - general: 将一般方程转为标准形式，格式：general;x^2+y^2+Dx+Ey+F=0
            - tangent: 求圆的切线方程，格式：tangent;circle_eq;{x0,y0}
            """)
    public String analyzeCircleEquation(@P("圆方程分析，如 'standard;{0,0};5'") String input) {
        log.info("⭕ 圆方程分析: {}", input);
        
        if (!isSafeExpression(input)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = input.split(";");
            if (parts.length < 2) {
                return "❌ 格式错误";
            }
            
            String operation = parts[0].trim();
            
            switch (operation) {
                case "standard":
                    if (parts.length != 3) return "❌ 标准圆方程需要格式: standard;{h,k};r";
                    return calculateStandardCircleEquation(parts[1], parts[2]);
                    
                case "general":
                    if (parts.length != 2) return "❌ 一般方程转换需要格式: general;equation";
                    return convertGeneralCircleEquation(parts[1]);
                    
                case "tangent":
                    if (parts.length != 3) return "❌ 切线方程需要格式: tangent;circle_eq;{x0,y0}";
                    return calculateCircleTangent(parts[1], parts[2]);
                    
                default:
                    return "❌ 不支持的操作类型";
            }
        } catch (Exception e) {
            log.error("圆方程分析时发生错误", e);
            return "❌ 圆方程分析失败：" + e.getMessage();
        }
    }

    /**
     * 最大公约数和最小公倍数
     * 例如：GCD(48, 18), LCM(12, 15)
     */
    @Tool(name = "gcdLcm", value = """
            计算最大公约数(GCD)和最小公倍数(LCM)。
            输入格式：操作;数字1;数字2[;数字3...]
            示例：gcd;48;18, lcm;12;15;20
            """)
    public String gcdLcm(@P("最大公约数或最小公倍数计算，如 'gcd;48;18'") String input) {
        log.info("🔢 GCD/LCM计算: {}", input);
        
        try {
            String[] parts = input.split(";");
            if (parts.length < 3) {
                return "❌ 格式错误，至少需要两个数字";
            }
            
            String operation = parts[0].trim().toLowerCase();
            String[] numbers = new String[parts.length - 1];
            System.arraycopy(parts, 1, numbers, 0, numbers.length);
            
            String numberList = String.join(",", numbers);
            String symjaExpression = operation.equals("gcd") ? 
                String.format("GCD(%s)", numberList) : 
                String.format("LCM(%s)", numberList);
            
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatGcdLcmResult(operation.toUpperCase(), numberList, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("GCD/LCM计算时发生错误", e);
            return "❌ GCD/LCM计算失败：" + e.getMessage();
        }
    }

    /**
     * 排列组合计算
     * 例如：P(10,3), C(10,3)
     */
    @Tool(name = "permutationCombination", value = """
            计算排列和组合数。
            输入格式：
            - 排列：perm;n;r 或 P;n;r
            - 组合：comb;n;r 或 C;n;r
            示例：perm;10;3, comb;10;3
            """)
    public String permutationCombination(@P("排列组合计算，如 'perm;10;3' 或 'comb;10;3'") String input) {
        log.info("🔄 排列组合计算: {}", input);
        
        try {
            String[] parts = input.split(";");
            if (parts.length != 3) {
                return "❌ 格式错误，请使用 '操作;n;r' 的格式";
            }
            
            String operation = parts[0].trim().toLowerCase();
            String n = parts[1].trim();
            String r = parts[2].trim();
            
            String symjaExpression;
            if (operation.equals("perm") || operation.equals("p")) {
                symjaExpression = String.format("Permutations(%s, %s)", n, r);
            } else if (operation.equals("comb") || operation.equals("c")) {
                symjaExpression = String.format("Binomial(%s, %s)", n, r);
            } else {
                return "❌ 不支持的操作类型，请使用 perm 或 comb";
            }
            
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatPermCombResult(operation, n, r, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("排列组合计算时发生错误", e);
            return "❌ 排列组合计算失败：" + e.getMessage();
        }
    }

    /**
     * 质因数分解
     * 例如：将 60 分解为质因数
     */
    @Tool(name = "primeFactorization", value = """
            将正整数进行质因数分解。
            输入格式：正整数
            示例：60, 100, 144
            """)
    public String primeFactorization(@P("需要分解的正整数，如 '60'") String number) {
        log.info("🔍 质因数分解: {}", number);
        
        try {
            String symjaExpression = String.format("FactorInteger(%s)", number);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatPrimeFactorizationResult(number, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("质因数分解时发生错误", e);
            return "❌ 质因数分解失败：" + e.getMessage();
        }
    }

    /**
     * 概率分布计算
     * 例如：二项分布、正态分布等
     */
    @Tool(name = "probabilityDistribution", value = """
            计算常见概率分布。
            支持的分布：
            - binomial: 二项分布 P(X=k), 格式：binomial;n;p;k
            - normal: 正态分布概率密度, 格式：normal;mean;std;x
            - poisson: 泊松分布 P(X=k), 格式：poisson;lambda;k
            """)
    public String probabilityDistribution(@P("概率分布计算，如 'binomial;10;0.3;3'") String input) {
        log.info("📊 概率分布计算: {}", input);
        
        try {
            String[] parts = input.split(";");
            if (parts.length < 3) {
                return "❌ 参数不足";
            }
            
            String distribution = parts[0].trim().toLowerCase();
            
            String symjaExpression;
            switch (distribution) {
                case "binomial":
                    if (parts.length != 4) return "❌ 二项分布需要4个参数: binomial;n;p;k";
                    String n = parts[1].trim();
                    String p = parts[2].trim();
                    String k = parts[3].trim();
                    symjaExpression = String.format("PDF(BinomialDistribution(%s, %s), %s)", n, p, k);
                    break;
                    
                case "normal":
                    if (parts.length != 4) return "❌ 正态分布需要4个参数: normal;mean;std;x";
                    String mean = parts[1].trim();
                    String std = parts[2].trim();
                    String x = parts[3].trim();
                    symjaExpression = String.format("PDF(NormalDistribution(%s, %s), %s)", mean, std, x);
                    break;
                    
                case "poisson":
                    if (parts.length != 3) return "❌ 泊松分布需要3个参数: poisson;lambda;k";
                    String lambda = parts[1].trim();
                    String k_poisson = parts[2].trim();
                    symjaExpression = String.format("PDF(PoissonDistribution(%s), %s)", lambda, k_poisson);
                    break;
                    
                default:
                    return "❌ 不支持的分布类型";
            }
            
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatProbabilityResult(distribution, input, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("概率分布计算时发生错误", e);
            return "❌ 概率分布计算失败：" + e.getMessage();
        }
    }

    // === 私有辅助方法 ===

    private String calculateLineThroughTwoPoints(String point1, String point2) {
        String symjaExpression = String.format("Solve[y - %s[[2]] == ((y - %s[[2]])/(x - %s[[1]])) * (x - %s[[1]]), y]",
            point1, point2, point1, point1);
        MathResult result = evaluateExpression(symjaExpression);
        
        if (result.isSuccess()) {
            return String.format("""
                📐 **两点间直线方程**
                点1: %s, 点2: %s
                直线方程: %s
                
                💡 可以验证两点是否满足此方程。
                """, point1, point2, result.getResult());
        } else {
            return result.getError();
        }
    }

    private String calculatePointToLineDistance(String point, String lineEq) {
        // 简化处理：假设直线方程为 ax + by + c = 0 形式
        String symjaExpression = String.format("Distance[%s, %s]", point, lineEq);
        MathResult result = evaluateExpression(symjaExpression);
        return formatGeometryResult("点到直线距离", result);
    }

    private String calculateLineIntersection(String line1, String line2) {
        String symjaExpression = String.format("Solve[{%s, %s}, {x, y}]", line1, line2);
        MathResult result = evaluateExpression(symjaExpression);
        return formatGeometryResult("两直线交点", result);
    }

    private String calculateStandardCircleEquation(String center, String radius) {
        return String.format("""
            ⭕ **标准圆方程**
            圆心: %s
            半径: %s
            标准方程: (x - h)² + (y - k)² = r²
            
            💡 其中 (h,k) 是圆心坐标，r 是半径。
            """, center, radius);
    }

    private String convertGeneralCircleEquation(String equation) {
        String symjaExpression = String.format("CompleteSquare(%s, {x, y})", equation);
        MathResult result = evaluateExpression(symjaExpression);
        return formatGeometryResult("圆的标准形式", result);
    }

    private String calculateCircleTangent(String circleEq, String point) {
        // 简化实现，实际需要更复杂的处理
        return String.format("""
            ⭕ **圆的切线方程**
            圆方程: %s
            切点: %s
            
            💡 需要根据具体的圆方程形式进行计算。
            """, circleEq, point);
    }

    // === 格式化输出方法 ===

    private String formatGeometryResult(String operation, MathResult result) {
        if (result.isSuccess()) {
            return String.format("""
                📐 **%s**
                计算结果: %s
                
                💡 解析几何将几何问题转化为代数计算。
                """, operation, result.getResult());
        } else {
            return result.getError();
        }
    }

    private String formatGcdLcmResult(String operation, String numbers, String result) {
        String operationName = operation.equals("GCD") ? "最大公约数" : "最小公倍数";
        return String.format("""
            🔢 **%s计算**
            数字: %s
            %s: %s
            
            💡 %s在分数化简和数论问题中很重要。
            """, operationName, numbers, operationName, result, operationName);
    }

    private String formatPermCombResult(String operation, String n, String r, String result) {
        String operationName = operation.toLowerCase().startsWith("perm") || operation.equals("p") ? 
            "排列" : "组合";
        String symbol = operation.toLowerCase().startsWith("perm") || operation.equals("p") ? 
            String.format("P(%s,%s)", n, r) : String.format("C(%s,%s)", n, r);
            
        return String.format("""
            🔄 **%s计算**
            %s = %s
            
            💡 排列考虑顺序，组合不考虑顺序。
            """, operationName, symbol, result);
    }

    private String formatPrimeFactorizationResult(String number, String factorization) {
        return String.format("""
            🔍 **质因数分解**
            原数: %s
            质因数分解: %s
            
            💡 质因数分解是数论的基础，每个正整数都有唯一的质因数分解。
            """, number, factorization);
    }

    private String formatProbabilityResult(String distribution, String input, String result) {
        return String.format("""
            📊 **概率分布计算**
            分布类型: %s
            参数: %s
            概率/密度: %s
            
            💡 概率分布描述了随机事件的规律性。
            """, distribution, input, result);
    }
}
