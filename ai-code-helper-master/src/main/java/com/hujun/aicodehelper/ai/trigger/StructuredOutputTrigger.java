package com.hujun.aicodehelper.ai.trigger;

import com.hujun.aicodehelper.ai.ThinkyAIService;
import com.hujun.aicodehelper.context.ConversationContext;
import com.hujun.aicodehelper.context.ConversationContextManager;
import com.hujun.aicodehelper.model.StructuredOutput.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 结构化输出触发器
 * 负责检测何时应该生成结构化输出内容
 */
@Slf4j
@Component
public class StructuredOutputTrigger {

    @Autowired
    private ThinkyAIService thinkyAIService;

    @Autowired
    private ConversationContextManager contextManager;

    // 触发关键词配置
    private static final List<String> COMPLETION_KEYWORDS = Arrays.asList(
        "我做完了", "我的答案是", "最终答案", "我觉得是", "应该是", "看看对不对",
        "我算出来是", "结果是", "答案应该是", "我认为答案是"
    );

    private static final List<String> CONFUSION_KEYWORDS = Arrays.asList(
        "不懂", "不会", "为什么", "怎么办", "不明白", "搞不清楚",
        "看不懂", "不理解", "这是什么意思", "完全不知道"
    );

    private static final List<String> HELP_KEYWORDS = Arrays.asList(
        "怎么做", "如何解决", "能帮我吗", "不知道怎么开始", "从哪里入手"
    );

    /**
     * 检查并触发结构化输出
     */
    public Optional<Object> checkAndTrigger(String userInput, String sessionId) {
        ConversationContext context = contextManager.getOrCreate(sessionId);
        
        // 更新对话上下文
        updateContext(context, userInput);
        
        // 检查是否应该避免重复触发
        String triggerType = determineTriggerType(userInput, context);
        if ("NONE".equals(triggerType) || context.shouldAvoidRepeatTrigger(triggerType)) {
            log.debug("跳过触发：类型={}, 会话={}", triggerType, sessionId);
            return Optional.empty();
        }

        // 根据触发类型生成相应的结构化输出
        try {
            Object structuredOutput = generateStructuredOutput(triggerType, context);
            if (structuredOutput != null) {
                context.markStructuredOutput(triggerType);
                contextManager.save(context);
                log.info("生成结构化输出：类型={}, 会话={}", triggerType, sessionId);
                return Optional.of(structuredOutput);
            }
        } catch (Exception e) {
            log.error("生成结构化输出失败：{}", e.getMessage(), e);
        }

        return Optional.empty();
    }

    /**
     * 确定触发类型
     */
    private String determineTriggerType(String userInput, ConversationContext context) {
        // 1. 检查完整解题触发条件
        if (isCompleteSolution(userInput, context)) {
            return "SOLUTION";
        }

        // 2. 检查知识诊断触发条件
        if (needsDiagnosis(context)) {
            return "DIAGNOSIS";
        }

        // 3. 检查进度里程碑触发条件
        if (reachedMilestone(context)) {
            return "PROGRESS";
        }

        // 4. 使用AI判断（作为兜底策略）
        try {
            String aiDecision = thinkyAIService.shouldTriggerStructuredOutput(
                userInput,
                context.getRoundCount(),
                context.isSameTopic(),
                context.getConsecutiveMistakes(),
                context.getCompletedProblems()
            );
            return aiDecision != null ? aiDecision.toUpperCase() : "NONE";
        } catch (Exception e) {
            log.warn("AI触发判断失败：{}", e.getMessage());
            return "NONE";
        }
    }

    /**
     * 检查是否完成解题
     */
    private boolean isCompleteSolution(String input, ConversationContext context) {
        // 关键词匹配
        boolean keywordMatch = COMPLETION_KEYWORDS.stream()
                .anyMatch(keyword -> input.contains(keyword));

        // 对话轮次检查 - 同一主题连续讨论超过3轮
        boolean prolongedDiscussion = context.getRoundCount() > 3 && context.isSameTopic();

        // 检查是否包含数值答案（简单正则匹配）
        boolean hasNumericAnswer = input.matches(".*\\d+.*") && 
            (input.contains("等于") || input.contains("是") || input.contains("="));

        return keywordMatch || prolongedDiscussion || hasNumericAnswer;
    }

    /**
     * 检查是否需要知识诊断
     */
    private boolean needsDiagnosis(ConversationContext context) {
        // 连续错误次数超过阈值
        boolean tooManyMistakes = context.getConsecutiveMistakes() != null && 
            context.getConsecutiveMistakes() >= 2;

        // 在同一主题上卡壳时间较长
        boolean stuckOnSameTopic = context.isSameTopic() && context.getRoundCount() > 5;

        // 知识盲区数量较多
        boolean manyGaps = context.getIdentifiedGaps() != null && 
            context.getIdentifiedGaps().size() >= 3;

        return tooManyMistakes || stuckOnSameTopic || manyGaps;
    }

    /**
     * 检查是否达到学习里程碑
     */
    private boolean reachedMilestone(ConversationContext context) {
        // 完成题目数达到里程碑
        boolean problemMilestone = context.getCompletedProblems() != null && 
            (context.getCompletedProblems() % 5 == 0) && context.getCompletedProblems() > 0;

        // 学习时长达到里程碑（30分钟）
        context.updateStudyTime();
        boolean timeMilestone = context.getStudyTimeMinutes() != null && 
            (context.getStudyTimeMinutes() % 30 == 0) && context.getStudyTimeMinutes() > 0;

        // 掌握新知识点数量达到阈值
        boolean knowledgeMilestone = context.getMasteredConcepts() != null && 
            context.getMasteredConcepts().size() > 0 && 
            (context.getMasteredConcepts().size() % 3 == 0);

        return problemMilestone || timeMilestone || knowledgeMilestone;
    }

    /**
     * 生成结构化输出
     */
    private Object generateStructuredOutput(String triggerType, ConversationContext context) {
        switch (triggerType) {
            case "SOLUTION":
                return generateSolutionStructure(context);
            case "DIAGNOSIS":
                return generateDiagnosis(context);
            case "PROGRESS":
                return generateProgressReport(context);
            default:
                return null;
        }
    }

    /**
     * 生成解题步骤结构
     */
    private StepByStepSolution generateSolutionStructure(ConversationContext context) {
        try {
            String conversationHistory = String.join("\n", context.getConversationHistory());
            String currentQuestion = context.getCurrentProblem() != null ? 
                context.getCurrentProblem() : "当前讨论的问题";
            String studentState = buildStudentStateDescription(context);

            return thinkyAIService.generateStructuredSolution(
                conversationHistory, currentQuestion, studentState);
        } catch (Exception e) {
            log.error("生成解题步骤失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成学习诊断
     */
    private LearningDiagnosis generateDiagnosis(ConversationContext context) {
        try {
            List<String> mistakes = context.getCommonMistakes();
            List<String> interactions = context.getConversationHistory();
            
            return thinkyAIService.diagnoseLearning(mistakes, interactions, context.getKnowledgeMastery());
        } catch (Exception e) {
            log.error("生成学习诊断失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成进度报告
     */
    private ProgressReport generateProgressReport(ConversationContext context) {
        try {
            return thinkyAIService.generateProgressReport(
                context.getCompletedProblems(),
                context.getStudyTimeMinutes(),
                context.getMasteredConcepts(),
                context.getCurrentSubject()
            );
        } catch (Exception e) {
            log.error("生成进度报告失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新对话上下文
     */
    private void updateContext(ConversationContext context, String userInput) {
        context.incrementRound();
        context.addToHistory("用户: " + userInput);

        // 分析用户输入，更新相关状态
        analyzeUserInput(context, userInput);
    }

    /**
     * 分析用户输入，提取学习状态信息
     */
    private void analyzeUserInput(ConversationContext context, String userInput) {
        // 检测困惑信号
        boolean isConfused = CONFUSION_KEYWORDS.stream()
                .anyMatch(userInput::contains);
        
        if (isConfused) {
            context.recordMistake("表达困惑: " + userInput);
        }

        // 检测求助信号
        boolean needsHelp = HELP_KEYWORDS.stream()
                .anyMatch(userInput::contains);
        
        if (needsHelp && context.getStuckPoint() == null) {
            context.setStuckPoint("需要解题指导");
        }

        // 简单的主题提取（可以后续用NLP优化）
        if (userInput.length() > 10) {
            String newTopic = extractTopic(userInput);
            if (newTopic != null) {
                context.updateTopic(newTopic);
            }
        }

        // 检测成功信号
        boolean showsSuccess = COMPLETION_KEYWORDS.stream()
                .anyMatch(userInput::contains);
        
        if (showsSuccess) {
            context.recordSuccess();
        }
    }

    /**
     * 简单的主题提取
     */
    private String extractTopic(String input) {
        // 简单的关键词提取（实际应用中可以用更复杂的NLP）
        if (input.contains("函数")) return "函数";
        if (input.contains("方程")) return "方程";
        if (input.contains("几何")) return "几何";
        if (input.contains("物理") || input.contains("力") || input.contains("运动")) return "物理";
        if (input.contains("数学") || input.contains("计算")) return "数学";
        
        // 提取可能的数学概念
        String[] mathConcepts = {"三角函数", "导数", "积分", "概率", "统计", "代数", "立体几何"};
        for (String concept : mathConcepts) {
            if (input.contains(concept)) return concept;
        }
        
        return null;
    }

    /**
     * 构建学生状态描述
     */
    private String buildStudentStateDescription(ConversationContext context) {
        StringBuilder state = new StringBuilder();
        
        state.append("当前学科: ").append(context.getCurrentSubject() != null ? 
            context.getCurrentSubject() : "未知").append("\n");
        
        state.append("对话轮次: ").append(context.getRoundCount()).append("\n");
        
        if (context.getConsecutiveMistakes() != null && context.getConsecutiveMistakes() > 0) {
            state.append("连续错误次数: ").append(context.getConsecutiveMistakes()).append("\n");
        }
        
        if (context.getStuckPoint() != null) {
            state.append("卡壳点: ").append(context.getStuckPoint()).append("\n");
        }
        
        if (context.getMasteredConcepts() != null && !context.getMasteredConcepts().isEmpty()) {
            state.append("已掌握概念: ").append(String.join(", ", context.getMasteredConcepts())).append("\n");
        }
        
        if (context.getIdentifiedGaps() != null && !context.getIdentifiedGaps().isEmpty()) {
            state.append("知识盲区: ").append(String.join(", ", context.getIdentifiedGaps())).append("\n");
        }
        
        return state.toString();
    }
}
