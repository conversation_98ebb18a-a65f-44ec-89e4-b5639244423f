package com.hujun.aicodehelper.ai.model;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.listener.ChatModelListener;
import dev.langchain4j.model.openai.OpenAiChatModel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import java.util.List;

/**
 * ThinkyAI 专用 ChatModel 配置
 * 专门为结构化输出优化的 ChatModel
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "thinky.ai.chat-model")
@Data
public class ThinkyAiChatModelConfig {

    private String provider = "bigmodel";  // 默认使用智谱AI
    private String apiKey;
    private String modelName;
    private String baseUrl;
    private Double temperature = 0.3;  // 结构化输出使用较低温度
    private Integer maxTokens = 2000;

    @Resource
    private ChatModelListener chatModelListener;

    @Bean(name = "thinkyAiChatModel")
    public ChatModel thinkyAiChatModel() {
        log.info("正在创建ThinkyAI专用ChatModel...");
        
        // 根据配置选择不同的提供商
        ChatModel chatModel = createChatModelByProvider();
        
        log.info("ThinkyAI专用ChatModel创建完成 - 提供商: {}, 模型: {}", provider, modelName);
        return chatModel;
    }

    private ChatModel createChatModelByProvider() {
        switch (provider.toLowerCase()) {
            case "bigmodel":
                return createBigModelChatModel();
            case "modelscope":
                return createModelScopeChatModel();
            case "gemini":
                return createGeminiChatModel();
            default:
                log.warn("未知的提供商: {}, 使用默认BigModel", provider);
                return createBigModelChatModel();
        }
    }

    private ChatModel createBigModelChatModel() {
        String finalApiKey = apiKey != null && !apiKey.trim().isEmpty() ? apiKey : 
            System.getProperty("BIGMODEL_API_KEY", "a08cf598f1d6486f8e19f6783966ebcd.eubFlWtgOU1X8nrz");
        String finalModelName = modelName != null && !modelName.trim().isEmpty() ? modelName : "glm-4-flash";
        String finalBaseUrl = baseUrl != null && !baseUrl.trim().isEmpty() ? baseUrl : "https://open.bigmodel.cn/api/paas/v4";

        log.info("创建BigModel ChatModel - API Key: {}..., 模型: {}, BaseURL: {}, 温度: {}", 
                finalApiKey.substring(0, Math.min(8, finalApiKey.length())), finalModelName, finalBaseUrl, temperature);
        
        return OpenAiChatModel.builder()
                .apiKey(finalApiKey)
                .modelName(finalModelName)
                .baseUrl(finalBaseUrl)
                .temperature(temperature)
                .maxTokens(maxTokens)
                .listeners(chatModelListener != null ? List.of(chatModelListener) : List.of())
                .build();
    }

    private ChatModel createModelScopeChatModel() {
        String finalApiKey = apiKey != null && !apiKey.trim().isEmpty() ? apiKey : 
            System.getProperty("MODELSCOPE_API_KEY", "4b358b91-dd36-42c2-8017-8acee9afa005");
        String finalModelName = modelName != null && !modelName.trim().isEmpty() ? modelName : "Qwen/Qwen2.5-72B-Instruct";
        String finalBaseUrl = baseUrl != null && !baseUrl.trim().isEmpty() ? baseUrl : "https://api-inference.modelscope.cn/v1";

        log.info("创建ModelScope ChatModel - API Key: {}..., 模型: {}, BaseURL: {}, 温度: {}", 
                finalApiKey.substring(0, Math.min(8, finalApiKey.length())), finalModelName, finalBaseUrl, temperature);
        
        return OpenAiChatModel.builder()
                .apiKey(finalApiKey)
                .modelName(finalModelName)
                .baseUrl(finalBaseUrl)
                .temperature(temperature)
                .maxTokens(maxTokens)
                .listeners(chatModelListener != null ? List.of(chatModelListener) : List.of())
                .build();
    }

    private ChatModel createGeminiChatModel() {
        // 注意：Gemini 需要特殊处理，因为它不是 OpenAI 兼容的
        log.warn("Gemini 提供商暂不支持，回退到 BigModel");
        return createBigModelChatModel();
    }
}
