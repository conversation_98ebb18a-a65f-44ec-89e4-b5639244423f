package com.hujun.aicodehelper.ai.catalog;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.hujun.aicodehelper.ai.model.MultiModelScopeConfig;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;

/**
 * Provider catalog for ModelScope using multi-model configuration.
 */
@Component
public class ModelScopeCatalog implements ModelCatalog {

    private final MultiModelScopeConfig multiModelScopeConfig;
    private final Map<UseCase, String> mapping = new EnumMap<>(UseCase.class);

    public ModelScopeCatalog(
            MultiModelScopeConfig multiModelScopeConfig,
            @Value("${ai.catalog.modelscope.text:lite}") String textSlot,
            @Value("${ai.catalog.modelscope.multimodal:multimodal}") String multimodalSlot,
            @Value("${ai.catalog.modelscope.rag:rag}") String ragSlot,
            @Value("${ai.catalog.modelscope.dictionary:dictionary}") String dictionarySlot,
            @Value("${ai.catalog.modelscope.pro:pro}") String proSlot
    ) {
        this.multiModelScopeConfig = multiModelScopeConfig;
        mapping.put(UseCase.TEXT, textSlot);
        mapping.put(UseCase.MULTIMODAL, multimodalSlot);
        mapping.put(UseCase.RAG, ragSlot);
        mapping.put(UseCase.DICTIONARY, dictionarySlot);
        mapping.put(UseCase.PRO, proSlot);
    }

    @Override
    public Optional<StreamingChatModel> getStreaming(UseCase useCase) {
        String modelKey = mapping.getOrDefault(useCase, "lite");
        return Optional.ofNullable(multiModelScopeConfig.getStreamingChatModel(modelKey));
    }

    @Override
    public Optional<ChatModel> getChat(UseCase useCase) {
        String modelKey = mapping.getOrDefault(useCase, "lite");
        return Optional.ofNullable(multiModelScopeConfig.getChatModel(modelKey));
    }

    @Override
    public String getEffectiveModelName(UseCase useCase) {
        String modelKey = mapping.getOrDefault(useCase, "lite");
        var cfg = multiModelScopeConfig.getModels().get(modelKey);
        return (cfg != null && cfg.getModelName() != null) ? cfg.getModelName() : modelKey;
    }
}
