package com.hujun.aicodehelper.ai.model;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.listener.ChatModelListener;
import dev.langchain4j.model.openai.OpenAiChatModel;
import lombok.Data;
import jakarta.annotation.Resource;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "langchain4j.modelscope.openai.chat-model")
@Data
public class ModelScopeOpenAiChatModelConfig {

    private String apiKey;
    private String modelName;
    private String baseUrl = "https://api-inference.modelscope.cn/v1";

    @Resource
    private ChatModelListener chatModelListener;

    @Bean(name = "myModelScopeChatModel")
    public ChatModel myModelScopeChatModel() {
        return OpenAiChatModel.builder()
                .apiKey(apiKey)
                .modelName(modelName)
                .baseUrl(baseUrl)
                .listeners(List.of(chatModelListener))
                .build();
    }
}
