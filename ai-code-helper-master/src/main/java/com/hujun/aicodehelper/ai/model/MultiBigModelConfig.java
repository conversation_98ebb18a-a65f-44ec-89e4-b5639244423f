package com.hujun.aicodehelper.ai.model;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.chat.listener.ChatModelListener;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;

@Slf4j
@Configuration
@ConfigurationProperties(prefix = "langchain4j.bigmodel.openai")
@Data
public class MultiBigModelConfig {

    @Data
    public static class ModelConfig {
        private String apiKey;
        private String modelName;
        private String description;
        private String baseUrl;
    }

    private String baseUrl;
    private Map<String, ModelConfig> models = new HashMap<>();

    private Map<String, ChatModel> chatModels = new HashMap<>();
    private Map<String, StreamingChatModel> streamingChatModels = new HashMap<>();

    @Resource
    private ChatModelListener chatModelListener;

    @PostConstruct
    public void initBigModelConfig() {
        log.info("正在初始化BigModel多模型配置...");

        for (Map.Entry<String, ModelConfig> entry : models.entrySet()) {
            String modelKey = entry.getKey();
            ModelConfig config = entry.getValue();

            try {
                ChatModel chatModel = OpenAiChatModel.builder()
                        .apiKey(config.getApiKey())
                        .modelName(config.getModelName())
                        .baseUrl(config.getBaseUrl())
                        .listeners(List.of(chatModelListener))
                        .build();
                chatModels.put(modelKey, chatModel);

                StreamingChatModel streamingChatModel = OpenAiStreamingChatModel.builder()
                        .apiKey(config.getApiKey())
                        .modelName(config.getModelName())
                        .baseUrl(config.getBaseUrl())
                        .listeners(List.of(chatModelListener))
                        .build();
                streamingChatModels.put(modelKey, streamingChatModel);

                log.info("已初始化BigModel [{}]: {} - {}",
                    modelKey, config.getModelName(), config.getDescription());
            } catch (Exception e) {
                log.error("创建BigModel [{}] 失败: {}", modelKey, e.getMessage(), e);
            }
        }

        log.info("BigModel多模型配置初始化完成，共初始化 {} 个模型", chatModels.size());
    }

    public ChatModel getChatModel(String modelKey) {
        ChatModel model = chatModels.get(modelKey);
        if (model == null) {
            log.warn("未找到BigModel [{}] 的ChatModel，返回lite模型", modelKey);
            return chatModels.get("lite");
        }
        return model;
    }

    public StreamingChatModel getStreamingChatModel(String modelKey) {
        StreamingChatModel model = streamingChatModels.get(modelKey);
        if (model == null) {
            log.warn("未找到BigModel [{}] 的StreamingChatModel，返回lite模型", modelKey);
            return streamingChatModels.get("lite");
        }
        return model;
    }

    public Map<String, Boolean> getAvailableModels() {
        Map<String, Boolean> availability = new HashMap<>();
        for (String modelKey : models.keySet()) {
            availability.put(modelKey, chatModels.containsKey(modelKey) &&
                                    streamingChatModels.containsKey(modelKey));
        }
        return availability;
    }
}
