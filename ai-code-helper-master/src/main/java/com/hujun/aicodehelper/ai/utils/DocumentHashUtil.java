package com.hujun.aicodehelper.ai.utils;

import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.nio.charset.StandardCharsets;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 文档哈希工具类
 * 用于计算文档内容的哈希值，避免重复处理相同文档
 */
@Slf4j
public class DocumentHashUtil {

    /**
     * 计算字符串内容的SHA-256哈希值
     */
    public static String calculateContentHash(String content) {
        if (content == null || content.trim().isEmpty()) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(content.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
        } catch (NoSuchAlgorithmException e) {
            log.error("❌ 计算内容哈希失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 计算文件内容的SHA-256哈希值
     */
    public static String calculateFileHash(Path filePath) {
        try {
            String content = Files.readString(filePath, StandardCharsets.UTF_8);
            return calculateContentHash(content);
        } catch (IOException e) {
            log.error("❌ 读取文件计算哈希失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 计算文件元信息的哈希值（文件名 + 最后修改时间 + 文件大小）
     */
    public static String calculateFileMetaHash(Path filePath) {
        try {
            String fileName = filePath.getFileName().toString();
            long lastModified = Files.getLastModifiedTime(filePath).toMillis();
            long fileSize = Files.size(filePath);
            
            String metaInfo = fileName + "|" + lastModified + "|" + fileSize;
            return calculateContentHash(metaInfo);
        } catch (IOException e) {
            log.error("❌ 计算文件元信息哈希失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 生成文档的唯一标识符（文件名 + 内容哈希的前8位）
     */
    public static String generateDocumentId(String fileName, String contentHash) {
        if (fileName == null || contentHash == null) {
            return null;
        }
        
        String shortHash = contentHash.length() > 8 ? contentHash.substring(0, 8) : contentHash;
        return fileName + "_" + shortHash;
    }
}