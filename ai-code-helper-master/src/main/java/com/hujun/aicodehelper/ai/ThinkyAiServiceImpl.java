package com.hujun.aicodehelper.ai;

import com.hujun.aicodehelper.model.StructuredOutput.*;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Arrays;
import java.util.ArrayList;

/**
 * ThinkyAI 服务实现类
 * 专注于引导式学习的结构化输出服务
 */
@Slf4j
@Service
public class ThinkyAiServiceImpl implements ThinkyAIService {

    private final ThinkyAIService thinkyAiService;

    @Autowired
    public ThinkyAiServiceImpl(
            @org.springframework.beans.factory.annotation.Qualifier("thinkyAiChatModel") ChatModel chatModel,  // 指定专用的ChatModel
            ChatMemoryProvider chatMemoryProvider,
            ContentRetriever contentRetriever
    ) {
        log.info("初始化ThinkyAI结构化输出服务...");
        
        try {
            // 使用LangChain4j的AI Services构建真正的结构化输出服务
            this.thinkyAiService = AiServices.builder(ThinkyAIService.class)
                    .chatModel(chatModel)
                    .chatMemoryProvider(chatMemoryProvider)
                    .contentRetriever(contentRetriever)
                    .build();
                    
            log.info("ThinkyAI结构化输出服务初始化完成");
        } catch (Exception e) {
            log.error("初始化ThinkyAI服务失败: {}", e.getMessage(), e);
            throw e; // 重新抛出异常，让Spring处理
        }
    }

    @Override
    public StepByStepSolution generateStructuredSolution(
            String conversationHistory, 
            String currentQuestion, 
            String studentState
    ) {
        try {
            log.info("🎓 开始生成结构化解题步骤");
            log.info("📝 对话历史: {}", conversationHistory != null ? conversationHistory.substring(0, Math.min(100, conversationHistory.length())) : "空");
            log.info("❓ 当前问题: {}", currentQuestion != null ? currentQuestion.substring(0, Math.min(100, currentQuestion.length())) : "空");
            log.info("👤 学生状态: {}", studentState != null ? studentState.substring(0, Math.min(100, studentState.length())) : "空");
            
            if (thinkyAiService == null) {
                log.error("❌ ThinkyAI服务未初始化！");
                return createFallbackSolution(currentQuestion);
            }
            
            log.info("🚀 调用AI服务生成结构化解题步骤...");
            StepByStepSolution solution = thinkyAiService.generateStructuredSolution(
                    conversationHistory, currentQuestion, studentState);
            
            log.info("✅ AI服务成功生成结构化解题步骤");
            return solution;
            
        } catch (Exception e) {
            log.error("❌ 生成结构化解题步骤失败: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            log.error("📋 详细错误信息:", e);
            
            // 返回备用的简化结构
            log.warn("🔄 使用备用解题方案");
            return createFallbackSolution(currentQuestion);
        }
    }

    @Override
    public LearningDiagnosis diagnoseLearning(
            List<String> mistakes, 
            List<String> interactions, 
            Map<String, Float> knowledgeMastery
    ) {
        try {
            log.debug("进行学习诊断 - 错误数量: {}, 互动数量: {}", 
                    mistakes != null ? mistakes.size() : 0,
                    interactions != null ? interactions.size() : 0);
                    
            return thinkyAiService.diagnoseLearning(mistakes, interactions, knowledgeMastery);
        } catch (Exception e) {
            log.error("学习诊断失败: {}", e.getMessage(), e);
            // 返回备用的简化诊断
            return createFallbackDiagnosis();
        }
    }

    @Override
    public ProgressReport generateProgressReport(
            Integer completedProblems, 
            Integer studyTime, 
            List<String> masteredConcepts, 
            String currentStage
    ) {
        try {
            log.debug("生成进度报告 - 完成题目: {}, 学习时长: {}分钟", 
                    completedProblems, studyTime);
                    
            return thinkyAiService.generateProgressReport(
                    completedProblems, studyTime, masteredConcepts, currentStage);
        } catch (Exception e) {
            log.error("生成进度报告失败: {}", e.getMessage(), e);
            // 返回备用的简化报告
            return createFallbackProgressReport(completedProblems, studyTime);
        }
    }

    @Override
    public String shouldTriggerStructuredOutput(
            String userInput, 
            Integer roundCount, 
            Boolean sameTopic, 
            Integer mistakeCount, 
            Integer completedCount
    ) {
        try {
            log.debug("判断结构化输出触发 - 轮次: {}, 同主题: {}, 错误: {}", 
                    roundCount, sameTopic, mistakeCount);
                    
            String result = thinkyAiService.shouldTriggerStructuredOutput(
                    userInput, roundCount, sameTopic, mistakeCount, completedCount);
            // 确保返回值是有效的
            if (Arrays.asList("SOLUTION", "DIAGNOSIS", "PROGRESS", "NONE").contains(result.trim())) {
                return result.trim();
            } else {
                log.warn("AI服务返回无效触发类型: {}, 使用规则判断", result);
                // 使用备用规则判断
                if (userInput.contains("做完了") || userInput.contains("我的答案")) {
                    return "SOLUTION";
                } else if (mistakeCount != null && mistakeCount >= 2) {
                    return "DIAGNOSIS"; 
                } else if (completedCount != null && completedCount > 0 && completedCount % 5 == 0) {
                    return "PROGRESS";
                }
                return "NONE";
            }
        } catch (Exception e) {
            log.error("结构化输出触发判断失败: {}", e.getMessage(), e);
            // 返回保守的判断结果
            return "NONE";
        }
    }

    /**
     * 创建备用解题步骤（当AI服务失败时使用）
     */
    private StepByStepSolution createFallbackSolution(String question) {
        log.warn("使用备用解题步骤，问题: {}", question);
        
        // 智能识别问题类型
        ProblemAnalysis analysis = analyzeQuestion(question);
        
        // 根据问题类型生成对应的解题步骤
        List<SolutionStep> steps = generateFallbackSteps(question, analysis);
        
        KnowledgeMapping knowledge = KnowledgeMapping.builder()
                .primaryConcepts(analysis.concepts)
                .prerequisites(analysis.prerequisites)
                .relatedTopics(analysis.relatedTopics)
                .curriculumMapping(analysis.curriculum)
                .build();
                
        ProblemIdentification problem = ProblemIdentification.builder()
                .subject(analysis.subject)
                .topic(analysis.topic)
                .difficulty(analysis.difficulty)
                .type(analysis.type)
                .description("基于关键词识别的问题类型：" + analysis.topic + "，题目内容：" + question)
                .build();
                
        List<CommonMistake> mistakes = generateCommonMistakes(analysis.topic);
        List<ExtendedExercise> recommendations = generateExtendedExercises(analysis.topic);
                
        return StepByStepSolution.builder()
                .problem(problem)
                .steps(steps)
                .knowledgePoints(knowledge)
                .mistakes(mistakes)
                .recommendations(recommendations)
                .build();
    }

    /**
     * 分析问题类型的辅助类
     */
    private static class ProblemAnalysis {
        String subject = "数学";
        String topic = "问题分析";
        String difficulty = "中等";
        String type = "计算题";
        String curriculum = "中学数学";
        List<String> concepts = List.of("基础概念");
        List<String> prerequisites = List.of("基础知识");
        List<String> relatedTopics = List.of("相关知识");
    }

    /**
     * 智能分析问题类型
     */
    private ProblemAnalysis analyzeQuestion(String question) {
        ProblemAnalysis analysis = new ProblemAnalysis();
        
        if (question == null || question.trim().isEmpty()) {
            return analysis;
        }
        
        String lowerQuestion = question.toLowerCase();
        
        // 二次方程识别
        if (lowerQuestion.contains("x²") || lowerQuestion.contains("x^2") || 
            (lowerQuestion.contains("x") && lowerQuestion.contains("=") && lowerQuestion.contains("0"))) {
            analysis.topic = "二次方程";
            analysis.type = "方程求解";
            analysis.concepts = List.of("二次方程", "因式分解", "求根公式");
            analysis.prerequisites = List.of("一元二次方程基础", "因式分解");
            analysis.relatedTopics = List.of("韦达定理", "二次函数", "配方法");
            analysis.curriculum = "九年级数学";
        }
        // 一元一次方程识别
        else if (lowerQuestion.contains("x") && lowerQuestion.contains("=") && !lowerQuestion.contains("²")) {
            analysis.topic = "一元一次方程";
            analysis.type = "方程求解";
            analysis.concepts = List.of("一元一次方程", "等式性质");
            analysis.prerequisites = List.of("基本运算", "移项");
            analysis.relatedTopics = List.of("不等式", "函数");
            analysis.curriculum = "七年级数学";
        }
        // 分数运算识别
        else if (lowerQuestion.contains("/") || lowerQuestion.contains("分数")) {
            analysis.topic = "分数运算";
            analysis.type = "计算题";
            analysis.concepts = List.of("分数加减", "分数乘除", "通分");
            analysis.prerequisites = List.of("分数基本概念", "最大公约数");
            analysis.relatedTopics = List.of("小数", "百分数");
            analysis.curriculum = "小学数学";
        }
        // 几何题识别
        else if (lowerQuestion.contains("面积") || lowerQuestion.contains("周长") || 
                 lowerQuestion.contains("角") || lowerQuestion.contains("三角形")) {
            analysis.topic = "几何计算";
            analysis.type = "几何题";
            analysis.concepts = List.of("面积公式", "周长公式", "几何性质");
            analysis.prerequisites = List.of("基本几何概念");
            analysis.relatedTopics = List.of("相似", "全等", "勾股定理");
            analysis.curriculum = "中学数学";
        }
        
        return analysis;
    }

    /**
     * 生成备用解题步骤
     */
    private List<SolutionStep> generateFallbackSteps(String question, ProblemAnalysis analysis) {
        List<SolutionStep> steps = new ArrayList<>();
        
        // 第一步：题目分析
        steps.add(SolutionStep.builder()
                .stepNumber(1)
                .guidingQuestion("你能找出题目中的已知条件和未知数吗？")
                .explanation("仔细阅读题目，识别已知信息和需要求解的内容")
                .studentThinking("分析题目结构，明确解题目标")
                .keyFormula("列出已知条件")
                .visualization("可以用图表整理信息")
                .build());
        
        // 根据问题类型添加专门步骤
        if ("二次方程".equals(analysis.topic)) {
            steps.add(SolutionStep.builder()
                    .stepNumber(2)
                    .guidingQuestion("这个二次方程可以用什么方法来解？")
                    .explanation("观察方程的形式，选择合适的解法（因式分解、公式法、配方法）")
                    .studentThinking("比较不同解法的适用性")
                    .keyFormula("ax² + bx + c = 0")
                    .visualization("可以画出二次函数图像")
                    .build());
            
            steps.add(SolutionStep.builder()
                    .stepNumber(3)
                    .guidingQuestion("按照选定的方法，你能一步步求出解吗？")
                    .explanation("运用选定的方法逐步计算")
                    .studentThinking("注意计算过程中的符号和运算顺序")
                    .keyFormula("根据具体方法应用相应公式")
                    .visualization("展示每一步的变换过程")
                    .build());
            
            steps.add(SolutionStep.builder()
                    .stepNumber(4)
                    .guidingQuestion("你能把求得的解代入原方程验证吗？")
                    .explanation("将解代入原方程，检查是否满足等式")
                    .studentThinking("验证是确保答案正确的重要步骤")
                    .keyFormula("代入验证")
                    .visualization("展示验证过程")
                    .build());
        }
        
        return steps;
    }

    /**
     * 生成常见错误提醒
     */
    private List<CommonMistake> generateCommonMistakes(String topic) {
        List<CommonMistake> mistakes = new ArrayList<>();
        
        if ("二次方程".equals(topic)) {
            mistakes.add(CommonMistake.builder()
                    .mistake("符号错误，特别是负号的处理")
                    .explanation("在移项和变形过程中容易出现符号错误")
                    .prevention("仔细检查每一步的符号变化，多次验算")
                    .build());
            
            mistakes.add(CommonMistake.builder()
                    .mistake("盲目使用公式法，没有考虑更简单的因式分解")
                    .explanation("没有观察方程的特殊形式，直接套用求根公式")
                    .prevention("先观察能否因式分解，再考虑公式法")
                    .build());
        }
        
        return mistakes;
    }

    /**
     * 生成延伸练习推荐
     */
    private List<ExtendedExercise> generateExtendedExercises(String topic) {
        List<ExtendedExercise> exercises = new ArrayList<>();
        
        if ("二次方程".equals(topic)) {
            exercises.add(ExtendedExercise.builder()
                    .title("因式分解法练习")
                    .description("练习用因式分解法求解二次方程")
                    .difficulty("中等")
                    .skills(List.of("因式分解", "二次方程求解"))
                    .build());
            
            exercises.add(ExtendedExercise.builder()
                    .title("判别式应用")
                    .description("利用判别式判断二次方程根的性质")
                    .difficulty("中等")
                    .skills(List.of("判别式", "根的性质"))
                    .build());
            
            exercises.add(ExtendedExercise.builder()
                    .title("二次方程实际应用")
                    .description("解决生活中的二次方程应用问题")
                    .difficulty("困难")
                    .skills(List.of("建模", "二次方程", "实际应用"))
                    .build());
        } else {
            exercises.add(ExtendedExercise.builder()
                    .title("基础练习")
                    .description("加强基础概念的理解和应用")
                    .difficulty("简单")
                    .skills(List.of("基础概念", "计算技巧"))
                    .build());
        }
        
        return exercises;
    }

    /**
     * 创建备用学习诊断
     */
    private LearningDiagnosis createFallbackDiagnosis() {
        log.warn("使用备用学习诊断");
        
        StudentCapability capability = StudentCapability.builder()
                .currentLevel("评估中")
                .proficiencyScore(50)
                .strengths(List.of("积极参与"))
                .weaknesses(List.of("需要更多练习"))
                .build();
                
        ThinkingPattern pattern = ThinkingPattern.builder()
                .patternType("探索型")
                .description("正在学习和探索中")
                .characteristics(List.of("愿意尝试", "需要引导"))
                .guidance("继续保持学习热情，多练习基础知识")
                .build();
                
        PersonalizedPath path = PersonalizedPath.builder()
                .nextTopics(List.of("基础概念复习"))
                .practiceTypes(List.of("基础练习", "概念理解"))
                .learningStrategy("循序渐进")
                .estimatedTime(30)
                .build();
                
        return LearningDiagnosis.builder()
                .capability(capability)
                .gaps(List.of())
                .pattern(pattern)
                .learningPath(path)
                .build();
    }

    /**
     * 创建备用进度报告
     */
    private ProgressReport createFallbackProgressReport(Integer completedProblems, Integer studyTime) {
        log.warn("使用备用进度报告");
        
        return ProgressReport.builder()
                .milestone("学习进行中")
                .completedProblems(completedProblems != null ? completedProblems : 0)
                .masteredConcepts(List.of("基础概念"))
                .improvementAreas(List.of("继续练习"))
                .recommendations("保持学习节奏，继续努力！")
                .build();
    }
}
