package com.hujun.aicodehelper.ai;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hujun.aicodehelper.ai.guardrail.SafeInputGuardrail;
import com.hujun.aicodehelper.services.ChatOrchestrationService;

import dev.langchain4j.data.message.Content;
import dev.langchain4j.service.Result;
import dev.langchain4j.service.guardrail.InputGuardrails;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * 智能AI代码助手服务 - 重构后的轻量级门面服务
 * 主要职责：接口适配和请求分发
 */
@Slf4j
@Service("aiCodeHelperService")
@InputGuardrails({ SafeInputGuardrail.class })
public class SmartAiCodeHelperService implements AiCodeHelperService {

    @Resource
    private ChatOrchestrationService chatOrchestrationService;

    @Resource
    private DynamicAiServiceManager dynamicAiServiceManager;

    @Resource
    private com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy modelSelectionStrategy;

    @Override
    public String chat(String userMessage) {
        log.info("📞 收到同步聊天请求: {}", userMessage.substring(0, Math.min(50, userMessage.length())));

        // 同步聊天直接使用动态服务管理器
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiService(userMessage, null);
        return aiService.chat(userMessage);
    }

    @Override
    public Report chatForReport(String userMessage) {
        log.info("📊 收到报告生成请求: {}", userMessage.substring(0, Math.min(50, userMessage.length())));

        // 报告生成使用PRO模型
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(
                com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType.PRO);
        return aiService.chatForReport(userMessage);
    }

    @Override
    public Result<String> chatWithRag(String userMessage) {
        log.info("🔍 收到RAG聊天请求: {}", userMessage.substring(0, Math.min(50, userMessage.length())));

        // RAG查询使用专门的RAG模型
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(
                com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType.RAG);
        return aiService.chatWithRag(userMessage);
    }

    @Override
    public Flux<String> chatStream(int memoryId, String userMessage) {
        log.info("💬 收到流式聊天请求 [会话:{}]: {}", memoryId,
                userMessage.substring(0, Math.min(50, userMessage.length())));

        // 委托给聊天编排服务处理
        return chatOrchestrationService.processTextChat(memoryId, userMessage);
    }

    @Override
    public Flux<String> chatStream(int memoryId, List<Content> contents) {
        log.info("🖼️ 收到多模态流式聊天请求 [会话:{}], 内容数量: {}", memoryId, contents.size());

        // 委托给聊天编排服务处理多模态内容
        return chatOrchestrationService.processMultiModalChat(memoryId, contents);
    }

    @Override
    public Flux<String> chatStreamWithLanguage(int memoryId, String userMessage, String language) {
        log.info("🌍 收到带语言参数的流式聊天请求 [会话:{}], 语言: {}", memoryId, language);

        // 委托给聊天编排服务处理语言相关聊天
        return chatOrchestrationService.processChatWithLanguage(memoryId, userMessage, language);
    }

    @Override
    public Flux<String> chatStreamWithImage(int memoryId, String textMessage, String imageUrl) {
        log.info("🖼️ 收到带图片的流式聊天请求 [会话:{}], 图片URL: {}", memoryId, imageUrl);

        // 使用动态服务管理器获取支持图片的AI服务
        AiCodeHelperService aiService = dynamicAiServiceManager.getMultimodalAiService();
        return aiService.chatStreamWithImage(memoryId, textMessage, imageUrl);
    }

    /**
     * 优化模型选择
     */
    public com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType optimizeModelSelection(
            String userMessage, java.util.ArrayList<Object> context, int memoryId) {
        log.info("🎯 开始优化模型选择，会话: {}", memoryId);

        // 将 ArrayList<Object> 转换为 List<String>
        java.util.List<String> conversationHistory = null;
        if (context != null) {
            conversationHistory = context.stream()
                    .filter(obj -> obj instanceof String)
                    .map(obj -> (String) obj)
                    .collect(java.util.stream.Collectors.toList());
        }

        // 使用模型选择策略来确定模型类型
        return modelSelectionStrategy.selectModel(userMessage, conversationHistory);
    }

    /**
     * 获取动态AI服务管理器
     */
    public DynamicAiServiceManager getDynamicAiServiceManager() {
        return dynamicAiServiceManager;
    }
}
