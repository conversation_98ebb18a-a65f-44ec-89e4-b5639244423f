package com.hujun.aicodehelper.ai.tools.math;

import lombok.extern.slf4j.Slf4j;
import org.matheclipse.core.eval.ExprEvaluator;
import org.matheclipse.core.interfaces.IExpr;
import org.matheclipse.parser.client.SyntaxError;
import org.matheclipse.parser.client.math.MathException;
import org.springframework.stereotype.Component;

/**
 * 数学计算基础工具类
 * 提供 Symja 引擎的基础封装和错误处理
 */
@Slf4j
@Component
public class BaseMathTool {

    private final ExprEvaluator evaluator;

    public BaseMathTool() {
        // 初始化 Symja 计算引擎
        // false: 不使用放松模式，确保数学计算准确性
        // 100: 递归深度限制，防止无限递归
        this.evaluator = new ExprEvaluator(false, (short) 100);
        log.info("🧮 Symja 数学引擎已初始化");
    }

    /**
     * 安全执行数学表达式计算
     * 
     * @param expression 数学表达式字符串
     * @return 计算结果，包含成功/失败状态和结果值
     */
    protected MathResult evaluateExpression(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return MathResult.error("表达式不能为空");
        }

        try {
            log.debug("🔢 计算表达式: {}", expression);
            
            IExpr result = evaluator.eval(expression);
            String resultStr = result.toString();
            
            log.debug("✅ 计算结果: {}", resultStr);
            return MathResult.success(resultStr, expression);
            
        } catch (SyntaxError e) {
            log.warn("❌ 语法错误: {} - 表达式: {}", e.getMessage(), expression);
            return MathResult.error("语法错误：" + e.getMessage());
            
        } catch (MathException e) {
            log.warn("❌ 数学计算错误: {} - 表达式: {}", e.getMessage(), expression);
            return MathResult.error("计算错误：" + e.getMessage());
            
        } catch (StackOverflowError e) {
            log.error("❌ 栈溢出 - 表达式可能过于复杂: {}", expression);
            return MathResult.error("计算过于复杂，导致栈溢出");
            
        } catch (OutOfMemoryError e) {
            log.error("❌ 内存不足 - 表达式: {}", expression);
            return MathResult.error("计算需要过多内存资源");
            
        } catch (Exception e) {
            log.error("❌ 未知错误: {} - 表达式: {}", e.getMessage(), expression, e);
            return MathResult.error("计算失败：" + e.getMessage());
        }
    }

    /**
     * 数学计算结果封装类
     */
    public static class MathResult {
        private final boolean success;
        private final String result;
        private final String originalExpression;
        private final String error;

        private MathResult(boolean success, String result, String originalExpression, String error) {
            this.success = success;
            this.result = result;
            this.originalExpression = originalExpression;
            this.error = error;
        }

        public static MathResult success(String result, String originalExpression) {
            return new MathResult(true, result, originalExpression, null);
        }

        public static MathResult error(String error) {
            return new MathResult(false, null, null, error);
        }

        public boolean isSuccess() { return success; }
        public String getResult() { return result; }
        public String getOriginalExpression() { return originalExpression; }
        public String getError() { return error; }

        /**
         * 格式化输出结果，便于 LLM 理解和展示
         */
        public String toDisplayString() {
            if (success) {
                return String.format("📊 计算结果：%s", result);
            } else {
                return String.format("❌ 计算失败：%s", error);
            }
        }

        /**
         * 生成详细的教学格式输出
         */
        public String toTeachingFormat() {
            if (success) {
                return String.format("""
                    📝 **计算过程**
                    原始表达式：%s
                    计算结果：%s
                    
                    💡 这个结果可以用于进一步的数学分析和讲解。
                    """, originalExpression, result);
            } else {
                return String.format("""
                    ⚠️ **计算遇到问题**
                    错误信息：%s
                    
                    💡 请检查表达式的语法，或尝试简化问题。
                    """, error);
            }
        }
    }

    /**
     * 验证表达式是否包含危险操作
     */
    protected boolean isSafeExpression(String expression) {
        // 基础安全检查：避免文件操作、网络访问等
        String[] dangerousKeywords = {
            "Import", "Export", "Get", "Put", "Run", "Install",
            "URLRead", "URLExecute", "CreateFile", "DeleteFile"
        };
        
        String upperExpression = expression.toUpperCase();
        for (String keyword : dangerousKeywords) {
            if (upperExpression.contains(keyword.toUpperCase())) {
                return false;
            }
        }
        return true;
    }
}
