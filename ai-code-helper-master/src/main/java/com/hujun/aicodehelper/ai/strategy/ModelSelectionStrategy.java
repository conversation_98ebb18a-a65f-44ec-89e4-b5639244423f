package com.hujun.aicodehelper.ai.strategy;

import java.util.List;
import java.util.regex.Pattern;

import org.springframework.stereotype.Component;

import com.hujun.aicodehelper.ai.catalog.CompositeCatalog;
import com.hujun.aicodehelper.ai.catalog.UseCase;

import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 模型选择策略
 * 根据用户输入和上下文自动选择最合适的AI模型
 */
@Slf4j
@Component
public class ModelSelectionStrategy {

    @Resource
    private CompositeCatalog compositeCatalog;

    /**
     * 模型类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ModelType {
        LITE("lite", "轻量级模型，用于日常聊天", UseCase.TEXT),
        RAG("rag", "RAG检索增强生成专用模型", UseCase.RAG),
        DICTIONARY("dictionary", "剑桥词典查询专用模型", UseCase.DICTIONARY),
        MATH("math", "K12数学计算专用模型，支持方程求解、微积分等", UseCase.TEXT),
        PRO("pro", "专业模型，用于复杂任务和高级需求", UseCase.PRO);

        private final String configKey;
        private final String description;
        private final UseCase useCase;
    }

    // RAG相关关键词匹配模式
    private static final Pattern RAG_PATTERNS = Pattern.compile(
            "(?i).*(搜索|查找|知识库|文档|学习资料|教程|课程|练习|习题|查询资料|找资料|检索).*",
            Pattern.CASE_INSENSITIVE);

    // 词典查询关键词匹配模式
    private static final Pattern DICTIONARY_PATTERNS = Pattern.compile(
            "(?i).*(define|meaning|dictionary|词典|什么意思|定义|解释|翻译|单词|发音|音标).*",
            Pattern.CASE_INSENSITIVE);

    // 数学计算关键词匹配模式
    private static final Pattern MATH_PATTERNS = Pattern.compile(
            "(?i).*(计算|求解|方程|积分|导数|微分|极限|因式分解|化简|展开|求导|积分|矩阵|行列式|" +
            "线性方程|二次方程|不等式|排列|组合|概率|统计|几何|三角函数|对数|指数|分数|小数|" +
            "学习.*分数|学习.*数学|学习.*几何|学习.*函数|学习.*方程|交互|模拟|可视化|" +
            "fraction.*learning|learning.*fraction|math.*learning|learning.*math|" +
            "solve|calculate|factor|simplify|expand|derivative|integral|limit|matrix|" +
            "equation|inequality|probability|geometry|trigonometry|logarithm|exponential|fraction|" +
            "数学题|数学问题|算式|公式|定理|证明|函数|图像|坐标|向量|使用工具|调用工具|工具解答|工具计算).*",
            Pattern.CASE_INSENSITIVE);

    // 复杂任务关键词匹配模式
    private static final Pattern COMPLEX_TASK_PATTERNS = Pattern.compile(
            "(?i).*(分析|详细|深入|比较|对比|设计|架构|规划|方案|策略|教程|指南|完整|全面|报告|总结|评估).*",
            Pattern.CASE_INSENSITIVE);

    /**
     * 根据用户消息和对话历史选择最合适的模型
     * 
     * @param userMessage         用户输入消息
     * @param conversationHistory 对话历史
     * @return 选择的模型类型
     */
    public ModelType selectModel(String userMessage, List<String> conversationHistory) {
        if (userMessage == null || userMessage.trim().isEmpty()) {
            return ModelType.LITE;
        }

        // 1. 优先检查数学计算请求（最高优先级）
        if (isMathRequest(userMessage)) {
            log.info("🎯 模型选择: {} -> {} (检测到数学计算请求)", 
                resolveEffectiveModelName(ModelType.MATH), ModelType.MATH.getDescription());
            log.debug("数学请求内容: {}", userMessage.substring(0, Math.min(100, userMessage.length())));
            return ModelType.MATH;
        }

        // 2. 检查是否明确请求词典功能
        if (isDictionaryRequest(userMessage)) {
            log.info("🎯 模型选择: {} -> {} (检测到词典请求)", 
                resolveEffectiveModelName(ModelType.DICTIONARY), ModelType.DICTIONARY.getDescription());
            log.debug("词典请求内容: {}", userMessage.substring(0, Math.min(100, userMessage.length())));
            return ModelType.DICTIONARY;
        }

        // 3. 检查是否需要RAG功能
        if (isRagRequest(userMessage, conversationHistory)) {
            log.info("🎯 模型选择: {} -> {} (检测到RAG请求)", 
                resolveEffectiveModelName(ModelType.RAG), ModelType.RAG.getDescription());
            log.debug("RAG请求内容: {}", userMessage.substring(0, Math.min(100, userMessage.length())));
            return ModelType.RAG;
        }

        // 4. 检查是否是复杂任务（降低优先级，避免与数学任务冲突）
        if (isComplexTask(userMessage) && !containsMathKeywords(userMessage)) {
            log.info("🎯 模型选择: {} -> {} (检测到复杂任务)", 
                resolveEffectiveModelName(ModelType.PRO), ModelType.PRO.getDescription());
            log.debug("复杂任务内容: {}", userMessage.substring(0, Math.min(100, userMessage.length())));
            return ModelType.PRO;
        }

        // 5. 默认使用轻量级模型
        log.info("🎯 模型选择: {} -> {} (默认选择)", 
            resolveEffectiveModelName(ModelType.LITE), ModelType.LITE.getDescription());
        log.debug("默认处理内容: {}", userMessage.substring(0, Math.min(100, userMessage.length())));
        return ModelType.LITE;
    }

    /**
     * 检测是否是词典查询请求
     */
    private boolean isDictionaryRequest(String message) {
        return DICTIONARY_PATTERNS.matcher(message).matches() ||
                containsEnglishWordPattern(message);
    }

    /**
     * 检测是否是数学计算请求
     */
    private boolean isMathRequest(String message) {
        return MATH_PATTERNS.matcher(message).matches() ||
                containsMathExpressionPattern(message);
    }

    /**
     * 检测是否包含数学表达式模式
     */
    private boolean containsMathExpressionPattern(String message) {
        String cleanMessage = message.trim();
        
        // 检测数学运算符和表达式
        if (cleanMessage.matches(".*[\\+\\-\\*/\\^=<>].*") && 
            cleanMessage.matches(".*[0-9x-z].*")) {
            return true;
        }
        
        // 检测常见数学函数
        if (cleanMessage.matches(".*(sin|cos|tan|log|ln|sqrt|exp)\\s*\\(.*") ||
            cleanMessage.matches(".*∫.*d[a-z].*") || // 积分符号
            cleanMessage.matches(".*d/d[a-z].*") || // 导数符号
            cleanMessage.matches(".*lim.*→.*")) { // 极限符号
            return true;
        }
        
        // 检测数学等式或不等式
        if (cleanMessage.matches(".*[a-z]\\s*[=<>]\\s*[0-9].*") ||
            cleanMessage.matches(".*[0-9]\\s*[=<>]\\s*[a-z].*")) {
            return true;
        }
        
        return false;
    }

    /**
     * 检测是否包含英文单词查询模式
     */
    private boolean containsEnglishWordPattern(String message) {
        // 检测 "what does [word] mean" 或 "[word]是什么意思" 等模式
        String lowerMessage = message.toLowerCase().trim();

        // 简单的英文单词查询模式
        if (lowerMessage.matches(".*what\\s+does\\s+\\w+\\s+mean.*") ||
                lowerMessage.matches(".*\\w+\\s*是什么意思.*") ||
                lowerMessage.matches(".*\\w+\\s*的意思.*") ||
                lowerMessage.matches(".*解释\\s*\\w+.*")) {
            return true;
        }

        // 如果消息很短且包含单个英文单词，可能是查词
        if (message.length() < 20 && message.matches(".*\\b[a-zA-Z]{3,}\\b.*")) {
            return true;
        }

        return false;
    }

    /**
     * 检测是否需要RAG功能
     */
    private boolean isRagRequest(String message, List<String> conversationHistory) {
        // 直接匹配RAG相关关键词
        if (RAG_PATTERNS.matcher(message).matches()) {
            return true;
        }

        // 基于对话历史判断
        if (conversationHistory != null && !conversationHistory.isEmpty()) {
            // 检查最近几次对话是否涉及知识检索
            return conversationHistory.stream()
                    .limit(3) // 只检查最近3次对话
                    .anyMatch(hist -> RAG_PATTERNS.matcher(hist).matches());
        }

        return false;
    }

    /**
     * 检测是否是复杂任务
     */
    private boolean isComplexTask(String message) {
        // 长消息通常表示复杂需求
        if (message.length() > 200) {
            return true;
        }

        // 匹配复杂任务关键词
        if (COMPLEX_TASK_PATTERNS.matcher(message).matches()) {
            return true;
        }

        // 包含多个问号，表示复杂查询
        if (message.chars().filter(ch -> ch == '?').count() >= 2 ||
                message.chars().filter(ch -> ch == '？').count() >= 2) {
            return true;
        }

        return false;
    }

    /**
     * 检测消息是否包含数学相关关键词
     */
    private boolean containsMathKeywords(String message) {
        return MATH_PATTERNS.matcher(message).find() ||
               containsMathExpressionPattern(message);
    }

    /**
     * 根据工具使用情况选择模型
     * 
     * @param toolName 使用的工具名称
     * @return 推荐的模型类型
     */
    public ModelType selectModelForTool(String toolName) {
        if (toolName == null) {
            return ModelType.LITE;
        }

        switch (toolName.toLowerCase()) {
            case "dictionarysearch":
            case "cambridge":
                return ModelType.DICTIONARY;
            case "solveequation":
            case "factorexpression":
            case "simplifyexpression":
            case "expandexpression":
            case "calculatederivative":
            case "calculateintegral":
            case "calculatelimit":
            case "matrixoperations":
            case "gcdlcm":
            case "permutationcombination":
            case "mathtools":
                return ModelType.MATH;
            default:
                return ModelType.LITE;
        }
    }

    /**
     * 强制选择特定模型（用于显式模型切换）
     * 
     * @param message 用户消息
     * @return 如果消息包含模型切换指令，返回对应模型类型，否则返回null
     */
    public ModelType getExplicitModelRequest(String message) {
        String lowerMessage = message.toLowerCase();

        if (lowerMessage.contains("使用pro模型") || lowerMessage.contains("切换到专业模型")) {
            return ModelType.PRO;
        }

        if (lowerMessage.contains("使用轻量模型") || lowerMessage.contains("切换到基础模型")) {
            return ModelType.LITE;
        }

        if (lowerMessage.contains("使用rag模型") || lowerMessage.contains("搜索知识库")) {
            return ModelType.RAG;
        }

        if (lowerMessage.contains("查词典") || lowerMessage.contains("词典模式")) {
            return ModelType.DICTIONARY;
        }

        if (lowerMessage.contains("使用数学模型") || lowerMessage.contains("数学计算模式") || 
            lowerMessage.contains("切换到数学模型") || lowerMessage.contains("数学工具")) {
            return ModelType.MATH;
        }

        return null;
    }

    /**
     * 解析当前实际使用的模型名（根据UseCase与路由的Provider）
     */
    private String resolveEffectiveModelName(ModelType modelType) {
        try {
            return compositeCatalog.getEffectiveModelName(modelType.getUseCase());
        } catch (Exception e) {
            log.debug("无法获取实际模型名，使用类型名代替: {}", e.getMessage());
            return modelType.name().toLowerCase();
        }
    }
}
