package com.hujun.aicodehelper.ai.tools.math;

import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * P1 级别数学工具：高等数学基础
 * 涵盖：导数、积分、极限、数列、线性代数基础
 */
@Slf4j
@Component
public class P1AdvancedMathTool extends BaseMathTool {

    /**
     * 计算导数
     * 例如：对 x^3 + 2*x^2 + 1 求导
     */
    @Tool(name = "calculateDerivative", value = """
            计算函数的导数。支持基本函数的一阶导数和高阶导数。
            输入格式：函数表达式[;阶数] （阶数可选，默认为1）
            示例：x^3 + 2*x^2 + 1, sin(x^2), x^3 + 2*x;2 （二阶导数）
            """)
    public String calculateDerivative(@P("函数表达式，可选阶数，如 'x^3 + 2*x^2 + 1' 或 'x^3;2'") String input) {
        log.info("📈 计算导数: {}", input);
        
        if (!isSafeExpression(input)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = input.split(";");
            String function = parts[0].trim();
            int order = parts.length > 1 ? Integer.parseInt(parts[1].trim()) : 1;
            
            String symjaExpression = String.format("D(%s, {x, %d})", function, order);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatDerivativeResult(function, order, result.getResult());
            } else {
                return result.getError();
            }
        } catch (NumberFormatException e) {
            return "❌ 导数阶数必须为正整数";
        } catch (Exception e) {
            log.error("计算导数时发生错误", e);
            return "❌ 导数计算失败：" + e.getMessage();
        }
    }

    /**
     * 计算不定积分
     * 例如：∫(x^2 + 2*x)dx
     */
    @Tool(name = "calculateIntegral", value = """
            计算函数的不定积分。支持基本函数的积分运算。
            输入格式：被积函数表达式
            示例：x^2 + 2*x, sin(x), 1/x, e^x, x*sin(x)
            """)
    public String calculateIntegral(@P("被积函数表达式，如 'x^2 + 2*x'") String function) {
        log.info("∫ 计算不定积分: {}", function);
        
        if (!isSafeExpression(function)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String symjaExpression = String.format("Integrate(%s, x)", function);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatIntegralResult(function, result.getResult(), false);
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("计算积分时发生错误", e);
            return "❌ 积分计算失败：" + e.getMessage();
        }
    }

    /**
     * 计算定积分
     * 例如：∫[0,1](x^2)dx
     */
    @Tool(name = "calculateDefiniteIntegral", value = """
            计算函数的定积分。需要指定积分区间。
            输入格式：函数表达式;下限;上限
            示例：x^2;0;1, sin(x);0;π, 1/x;1;e
            """)
    public String calculateDefiniteIntegral(@P("被积函数及积分区间，如 'x^2;0;1'") String input) {
        log.info("∫[] 计算定积分: {}", input);
        
        if (!isSafeExpression(input)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = input.split(";");
            if (parts.length != 3) {
                return "❌ 格式错误，请使用 '函数;下限;上限' 的格式";
            }
            
            String function = parts[0].trim();
            String lowerBound = parts[1].trim();
            String upperBound = parts[2].trim();
            
            String symjaExpression = String.format("Integrate(%s, {x, %s, %s})", function, lowerBound, upperBound);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                // 尝试数值计算
                String numericExpression = String.format("N(%s)", result.getResult());
                MathResult numericResult = evaluateExpression(numericExpression);
                
                return formatDefiniteIntegralResult(function, lowerBound, upperBound, 
                    result.getResult(), numericResult.isSuccess() ? numericResult.getResult() : null);
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("计算定积分时发生错误", e);
            return "❌ 定积分计算失败：" + e.getMessage();
        }
    }

    /**
     * 计算极限
     * 例如：lim(x→0) sin(x)/x
     */
    @Tool(name = "calculateLimit", value = """
            计算函数的极限。支持各种类型的极限计算。
            输入格式：函数表达式;趋向值[;方向]
            示例：sin(x)/x;0, (x^2-1)/(x-1);1, 1/x;0;+ （右极限）
            方向：+ 表示右极限，- 表示左极限，省略表示双侧极限
            """)
    public String calculateLimit(@P("函数及极限，如 'sin(x)/x;0' 或 '1/x;0;+'") String input) {
        log.info("→ 计算极限: {}", input);
        
        if (!isSafeExpression(input)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = input.split(";");
            if (parts.length < 2) {
                return "❌ 格式错误，请使用 '函数;趋向值[;方向]' 的格式";
            }
            
            String function = parts[0].trim();
            String approachValue = parts[1].trim();
            String direction = parts.length > 2 ? parts[2].trim() : "";
            
            String symjaExpression;
            if (direction.equals("+")) {
                symjaExpression = String.format("Limit(%s, x -> %s, Direction -> \"FromBelow\")", function, approachValue);
            } else if (direction.equals("-")) {
                symjaExpression = String.format("Limit(%s, x -> %s, Direction -> \"FromAbove\")", function, approachValue);
            } else {
                symjaExpression = String.format("Limit(%s, x -> %s)", function, approachValue);
            }
            
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatLimitResult(function, approachValue, direction, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("计算极限时发生错误", e);
            return "❌ 极限计算失败：" + e.getMessage();
        }
    }

    /**
     * 数列求和
     * 例如：∑(k=1 to n) k^2
     */
    @Tool(name = "calculateSeries", value = """
            计算级数和数列求和。支持有限和与无穷级数。
            输入格式：通项表达式;下标变量;起始值;终止值
            示例：k^2;k;1;n （∑k²从1到n）, 1/k^2;k;1;Infinity （∑1/k²从1到∞）
            """)
    public String calculateSeries(@P("级数求和，如 'k^2;k;1;n' 或 '1/k^2;k;1;Infinity'") String input) {
        log.info("∑ 计算级数: {}", input);
        
        if (!isSafeExpression(input)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = input.split(";");
            if (parts.length != 4) {
                return "❌ 格式错误，请使用 '通项;变量;起始值;终止值' 的格式";
            }
            
            String term = parts[0].trim();
            String variable = parts[1].trim();
            String start = parts[2].trim();
            String end = parts[3].trim();
            
            String symjaExpression = String.format("Sum(%s, {%s, %s, %s})", term, variable, start, end);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatSeriesResult(term, variable, start, end, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("计算级数时发生错误", e);
            return "❌ 级数计算失败：" + e.getMessage();
        }
    }

    /**
     * 矩阵运算
     * 例如：计算矩阵的行列式、逆矩阵等
     */
    @Tool(name = "matrixOperations", value = """
            矩阵基本运算。支持行列式、逆矩阵、特征值等计算。
            输入格式：操作类型;矩阵
            操作类型：det（行列式）, inverse（逆矩阵）, eigenvalues（特征值）
            示例：det;{{1,2},{3,4}}, inverse;{{2,1},{1,1}}, eigenvalues;{{1,2},{3,4}}
            """)
    public String matrixOperations(@P("矩阵运算，如 'det;{{1,2},{3,4}}'") String input) {
        log.info("📊 矩阵运算: {}", input);
        
        if (!isSafeExpression(input)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = input.split(";", 2);
            if (parts.length != 2) {
                return "❌ 格式错误，请使用 '操作类型;矩阵' 的格式";
            }
            
            String operation = parts[0].trim().toLowerCase();
            String matrix = parts[1].trim();
            
            String symjaExpression;
            switch (operation) {
                case "det":
                    symjaExpression = String.format("Det(%s)", matrix);
                    break;
                case "inverse":
                    symjaExpression = String.format("Inverse(%s)", matrix);
                    break;
                case "eigenvalues":
                    symjaExpression = String.format("Eigenvalues(%s)", matrix);
                    break;
                default:
                    return "❌ 不支持的操作类型。支持：det, inverse, eigenvalues";
            }
            
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatMatrixResult(operation, matrix, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("矩阵运算时发生错误", e);
            return "❌ 矩阵运算失败：" + e.getMessage();
        }
    }

    /**
     * 求解线性方程组（矩阵法）
     * 例如：Ax = b 形式的方程组
     */
    @Tool(name = "solveLinearSystem", value = """
            用矩阵方法求解线性方程组 Ax = b。
            输入格式：系数矩阵A;常数向量b
            示例：{{2,1},{1,3}};{5,6} 表示方程组 2x+y=5, x+3y=6
            """)
    public String solveLinearSystem(@P("线性方程组矩阵形式，如 '{{2,1},{1,3}};{5,6}'") String input) {
        log.info("🔍 矩阵法求解线性方程组: {}", input);
        
        if (!isSafeExpression(input)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = input.split(";");
            if (parts.length != 2) {
                return "❌ 格式错误，请使用 '系数矩阵A;常数向量b' 的格式";
            }
            
            String matrixA = parts[0].trim();
            String vectorB = parts[1].trim();
            
            String symjaExpression = String.format("LinearSolve(%s, %s)", matrixA, vectorB);
            MathResult result = evaluateExpression(symjaExpression);
            
            if (result.isSuccess()) {
                return formatLinearSystemResult(matrixA, vectorB, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("求解线性方程组时发生错误", e);
            return "❌ 线性方程组求解失败：" + e.getMessage();
        }
    }

    // === 格式化输出方法 ===

    private String formatDerivativeResult(String function, int order, String derivative) {
        String orderText = order == 1 ? "一阶导数" : String.format("%d阶导数", order);
        return String.format("""
            📈 **%s计算**
            原函数：f(x) = %s
            导数：f'(x) = %s
            
            💡 导数表示函数的变化率，可用于求函数的极值和单调性。
            """, orderText, function, derivative);
    }

    private String formatIntegralResult(String function, String integral, boolean isDefinite) {
        if (isDefinite) {
            return String.format("""
                ∫ **定积分计算**
                被积函数：%s
                积分结果：%s
                
                💡 定积分表示曲线与x轴之间的有向面积。
                """, function, integral);
        } else {
            return String.format("""
                ∫ **不定积分计算**
                被积函数：%s
                原函数：%s + C
                
                💡 不定积分是导数的逆运算，C为积分常数。
                """, function, integral);
        }
    }

    private String formatDefiniteIntegralResult(String function, String lower, String upper, String exact, String numeric) {
        if (numeric != null && !exact.equals(numeric)) {
            return String.format("""
                ∫ **定积分计算**
                被积函数：%s
                积分区间：[%s, %s]
                精确值：%s
                数值近似：%s
                
                💡 定积分表示曲线与x轴之间在指定区间的有向面积。
                """, function, lower, upper, exact, numeric);
        } else {
            return String.format("""
                ∫ **定积分计算**
                被积函数：%s
                积分区间：[%s, %s]
                结果：%s
                
                💡 定积分表示曲线与x轴之间在指定区间的有向面积。
                """, function, lower, upper, exact);
        }
    }

    private String formatLimitResult(String function, String approach, String direction, String limit) {
        String directionText = direction.equals("+") ? "右极限" : 
                              direction.equals("-") ? "左极限" : "极限";
        
        return String.format("""
            → **%s计算**
            函数：%s
            x → %s
            极限值：%s
            
            💡 极限描述函数在某点附近的变化趋势。
            """, directionText, function, approach, limit);
    }

    private String formatSeriesResult(String term, String variable, String start, String end, String sum) {
        return String.format("""
            ∑ **级数求和**
            通项：%s
            求和变量：%s
            求和范围：%s 到 %s
            和：%s
            
            💡 级数求和是多项式展开和数列分析的重要工具。
            """, term, variable, start, end, sum);
    }

    private String formatMatrixResult(String operation, String matrix, String result) {
        String operationText = switch (operation) {
            case "det" -> "行列式";
            case "inverse" -> "逆矩阵";
            case "eigenvalues" -> "特征值";
            default -> operation;
        };
        
        return String.format("""
            📊 **矩阵%s计算**
            原矩阵：%s
            %s：%s
            
            💡 矩阵运算是线性代数的核心，广泛应用于方程组求解。
            """, operationText, matrix, operationText, result);
    }

    private String formatLinearSystemResult(String matrixA, String vectorB, String solution) {
        return String.format("""
            🔍 **线性方程组求解**
            系数矩阵A：%s
            常数向量b：%s
            解向量x：%s
            
            💡 可以通过 Ax = b 验证解的正确性。
            """, matrixA, vectorB, solution);
    }
}
