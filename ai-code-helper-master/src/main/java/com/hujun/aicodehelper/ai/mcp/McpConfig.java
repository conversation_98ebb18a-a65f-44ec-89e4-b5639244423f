package com.hujun.aicodehelper.ai.mcp;

import dev.langchain4j.mcp.McpToolProvider;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.McpClient;
import dev.langchain4j.mcp.client.transport.McpTransport;
import dev.langchain4j.mcp.client.transport.http.HttpMcpTransport;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class McpConfig {

    @Value("${bigmodel.api-key:}")
    private String apiKey;

    @Bean
    public McpToolProvider mcpToolProvider() {
        // 临时创建空的 MCP 工具提供者，避免连接问题
        return McpToolProvider.builder()
                .mcpClients(java.util.Collections.emptyList())
                .build();
    }
}
