package com.hujun.aicodehelper.ai.tools.math;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * K12 数学工具使用示例
 * 展示各个级别数学工具的典型用法
 */
@Slf4j
@Component
public class MathToolsUsageExample {

    /**
     * P0 级别工具使用示例
     * 基础代数运算
     */
    public void demonstrateP0Tools() {
        log.info("=== P0 级别数学工具示例 ===");
        
        P0BasicMathTool p0Tool = new P0BasicMathTool();
        
        // 1. 求解一元方程
        log.info("1. 求解一元方程:");
        String equationResult = p0Tool.solveEquation("x^2 + 2*x - 3 = 0");
        log.info("方程 x^2 + 2*x - 3 = 0 的解:\n{}", equationResult);
        
        // 2. 因式分解
        log.info("2. 因式分解:");
        String factorResult = p0Tool.factorExpression("x^2 + 5*x + 6");
        log.info("x^2 + 5*x + 6 的因式分解:\n{}", factorResult);
        
        // 3. 表达式化简
        log.info("3. 表达式化简:");
        String simplifyResult = p0Tool.simplifyExpression("(x^2 + 2*x + 1)/(x + 1)");
        log.info("(x^2 + 2*x + 1)/(x + 1) 的化简结果:\n{}", simplifyResult);
        
        // 4. 表达式展开
        log.info("4. 表达式展开:");
        String expandResult = p0Tool.expandExpression("(x + 1)^3");
        log.info("(x + 1)^3 的展开结果:\n{}", expandResult);
    }

    /**
     * P1 级别工具使用示例
     * 高等数学运算
     */
    public void demonstrateP1Tools() {
        log.info("=== P1 级别数学工具示例 ===");
        
        P1AdvancedMathTool p1Tool = new P1AdvancedMathTool();
        
        // 1. 计算导数
        log.info("1. 计算导数:");
        String derivativeResult = p1Tool.calculateDerivative("x^3 + 2*x^2 + 1");
        log.info("f(x) = x^3 + 2*x^2 + 1 的导数:\n{}", derivativeResult);
        
        // 2. 计算不定积分
        log.info("2. 计算不定积分:");
        String integralResult = p1Tool.calculateIntegral("3*x^2 + 4*x");
        log.info("∫(3*x^2 + 4*x)dx 的结果:\n{}", integralResult);
        
        // 3. 计算定积分
        log.info("3. 计算定积分:");
        String definiteIntegralResult = p1Tool.calculateDefiniteIntegral("x^2;0;2");
        log.info("∫[0,2] x^2 dx 的结果:\n{}", definiteIntegralResult);
        
        // 4. 计算极限
        log.info("4. 计算极限:");
        String limitResult = p1Tool.calculateLimit("sin(x)/x;0");
        log.info("lim(x→0) sin(x)/x 的结果:\n{}", limitResult);
        
        // 5. 矩阵运算
        log.info("5. 矩阵运算:");
        String matrixResult = p1Tool.matrixOperations("det;{{1,2},{3,4}}");
        log.info("矩阵 [[1,2],[3,4]] 的行列式:\n{}", matrixResult);
    }

    /**
     * P2 级别工具使用示例
     * 专业数学运算
     */
    public void demonstrateP2Tools() {
        log.info("=== P2 级别数学工具示例 ===");
        
        P2SpecializedMathTool p2Tool = new P2SpecializedMathTool();
        
        // 1. 最大公约数计算
        log.info("1. 最大公约数计算:");
        String gcdResult = p2Tool.gcdLcm("gcd;48;18");
        log.info("GCD(48, 18) 的结果:\n{}", gcdResult);
        
        // 2. 排列组合计算
        log.info("2. 排列组合计算:");
        String combResult = p2Tool.permutationCombination("comb;10;3");
        log.info("C(10,3) 的结果:\n{}", combResult);
        
        // 3. 质因数分解
        log.info("3. 质因数分解:");
        String factorResult = p2Tool.primeFactorization("60");
        log.info("60 的质因数分解:\n{}", factorResult);
        
        // 4. 概率分布计算
        log.info("4. 概率分布计算:");
        String probResult = p2Tool.probabilityDistribution("binomial;10;0.3;3");
        log.info("二项分布 B(10,0.3) 中 P(X=3) 的结果:\n{}", probResult);
    }

    /**
     * 综合示例：解决实际数学问题
     */
    public void solvePracticalMathProblem() {
        log.info("=== 综合数学问题求解示例 ===");
        
        P0BasicMathTool p0Tool = new P0BasicMathTool();
        P1AdvancedMathTool p1Tool = new P1AdvancedMathTool();
        
        log.info("问题：已知函数 f(x) = x^2 - 4x + 3");
        log.info("1. 求 f(x) = 0 的解");
        log.info("2. 求 f'(x)");
        log.info("3. 求 f(x) 的因式分解形式");
        
        // 步骤1：求方程的解
        String roots = p0Tool.solveEquation("x^2 - 4*x + 3 = 0");
        log.info("解方程结果:\n{}", roots);
        
        // 步骤2：求导数
        String derivative = p1Tool.calculateDerivative("x^2 - 4*x + 3");
        log.info("导数结果:\n{}", derivative);
        
        // 步骤3：因式分解
        String factored = p0Tool.factorExpression("x^2 - 4*x + 3");
        log.info("因式分解结果:\n{}", factored);
        
        log.info("综合分析完成！");
    }

    /**
     * 教学提示生成示例
     */
    public void generateTeachingHints() {
        log.info("=== 数学教学提示示例 ===");
        
        String[] topics = {"equation", "factorization", "derivative", "integral", "probability"};
        
        for (String topic : topics) {
            String hint = MathToolsConfig.generateTeachingHint(topic);
            log.info("{} 相关提示: {}", topic, hint);
        }
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        try {
            demonstrateP0Tools();
            log.info("\n" + "=".repeat(50) + "\n");
            
            demonstrateP1Tools();
            log.info("\n" + "=".repeat(50) + "\n");
            
            demonstrateP2Tools();
            log.info("\n" + "=".repeat(50) + "\n");
            
            solvePracticalMathProblem();
            log.info("\n" + "=".repeat(50) + "\n");
            
            generateTeachingHints();
            
            log.info("🎉 所有数学工具示例运行完成！");
        } catch (Exception e) {
            log.error("运行示例时发生错误", e);
        }
    }
}
