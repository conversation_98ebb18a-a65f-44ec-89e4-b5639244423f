package com.hujun.aicodehelper.ai.catalog;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.hujun.aicodehelper.ai.model.MultiBigModelConfig;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;

/**
 * Provider catalog for BigModel (GLM) using multi-model configuration.
 */
@Component
public class BigModelCatalog implements ModelCatalog {

    private final MultiBigModelConfig multiBigModelConfig;
    private final Map<UseCase, String> mapping = new EnumMap<>(UseCase.class);

    public BigModelCatalog(
            MultiBigModelConfig multiBigModelConfig,
            @Value("${ai.catalog.bigmodel.text:lite}") String textSlot,
            @Value("${ai.catalog.bigmodel.multimodal:multimodal}") String multimodalSlot,
            @Value("${ai.catalog.bigmodel.rag:rag}") String ragSlot,
            @Value("${ai.catalog.bigmodel.dictionary:dictionary}") String dictionarySlot,
            @Value("${ai.catalog.bigmodel.pro:pro}") String proSlot
    ) {
        this.multiBigModelConfig = multiBigModelConfig;
        mapping.put(UseCase.TEXT, textSlot);
        mapping.put(UseCase.MULTIMODAL, multimodalSlot);
        mapping.put(UseCase.RAG, ragSlot);
        mapping.put(UseCase.DICTIONARY, dictionarySlot);
        mapping.put(UseCase.PRO, proSlot);
    }

    @Override
    public Optional<StreamingChatModel> getStreaming(UseCase useCase) {
        String modelKey = mapping.getOrDefault(useCase, "lite");
        return Optional.ofNullable(multiBigModelConfig.getStreamingChatModel(modelKey));
    }

    @Override
    public Optional<ChatModel> getChat(UseCase useCase) {
        String modelKey = mapping.getOrDefault(useCase, "lite");
        return Optional.ofNullable(multiBigModelConfig.getChatModel(modelKey));
    }

    @Override
    public String getEffectiveModelName(UseCase useCase) {
        String modelKey = mapping.getOrDefault(useCase, "lite");
        var cfg = multiBigModelConfig.getModels().get(modelKey);
        return (cfg != null && cfg.getModelName() != null) ? cfg.getModelName() : modelKey;
    }
}
