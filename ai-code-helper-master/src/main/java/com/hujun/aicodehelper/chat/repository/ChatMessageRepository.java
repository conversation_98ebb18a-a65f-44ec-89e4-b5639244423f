package com.hujun.aicodehelper.chat.repository;

import com.hujun.aicodehelper.chat.entity.ChatMessageEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 聊天消息数据访问层
 */
@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessageEntity, Long> {

    /**
     * 根据内存ID查询所有消息，按消息顺序排序
     */
    List<ChatMessageEntity> findByMemoryIdOrderByMessageOrder(String memoryId);

    /**
     * 根据内存ID删除所有消息
     */
    @Modifying
    @Query("DELETE FROM ChatMessageEntity c WHERE c.memoryId = :memoryId")
    void deleteByMemoryId(@Param("memoryId") String memoryId);

    /**
     * 检查指定内存ID是否存在消息
     */
    boolean existsByMemoryId(String memoryId);

    /**
     * 获取指定内存ID的消息数量
     */
    @Query("SELECT COUNT(c) FROM ChatMessageEntity c WHERE c.memoryId = :memoryId")
    long countByMemoryId(@Param("memoryId") String memoryId);
}