package com.hujun.aicodehelper.chat.store;

import com.hujun.aicodehelper.chat.entity.ChatMessageEntity;
import com.hujun.aicodehelper.chat.repository.ChatMessageRepository;
import dev.langchain4j.store.memory.chat.ChatMemoryStore; // 将 ChatMemoryStore 移动到 dev.langchain4j.store.memory.chat 包下
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.ChatMessageDeserializer;
import dev.langchain4j.data.message.ChatMessageSerializer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.IntStream;

/**
 * PostgreSQL 聊天记忆存储实现
 * 实现 LangChain4j ChatMemoryStore 接口，将聊天记录持久化到 PostgreSQL 数据库
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PostgreSqlChatMemoryStore implements ChatMemoryStore {

    private final ChatMessageRepository chatMessageRepository;
    private final com.hujun.aicodehelper.chat.config.ChatMemoryCacheProperties cacheProperties;
    
    // 缓存机制：避免频繁的数据库查询
    private final Map<String, List<ChatMessage>> messageCache = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> cacheVersion = new ConcurrentHashMap<>();
    
    // 缓存统计
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);

    /**
     * 根据内存ID获取所有消息
     * 
     * @param memoryId 内存ID，用于区分不同的聊天会话
     * @return 聊天消息列表
     */
    @Override
    public List<ChatMessage> getMessages(Object memoryId) {
        String memoryIdStr = memoryId.toString();
        
        // 检查缓存是否有效
        List<ChatMessage> cachedMessages = getCachedMessages(memoryIdStr);
        if (cachedMessages != null) {
            log.debug("🔍 [缓存命中] 获取聊天记录，内存ID: {}, 消息数量: {}", memoryIdStr, cachedMessages.size());
            return cachedMessages;
        }
        
        log.debug("🔍 [数据库查询] 获取聊天记录，内存ID: {}", memoryIdStr);
        
        try {
            List<ChatMessageEntity> entities = chatMessageRepository.findByMemoryIdOrderByMessageOrder(memoryIdStr);
            List<ChatMessage> messages = entities.stream()
                    .map(entity -> ChatMessageDeserializer.messageFromJson(entity.getMessageContent()))
                    .toList();
            
            // 更新缓存
            updateCache(memoryIdStr, messages);
            
            log.debug("✅ 成功获取 {} 条聊天记录，内存ID: {}", messages.size(), memoryIdStr);
            
            // 只在DEBUG级别记录详细信息
            if (log.isDebugEnabled()) {
                logMessageDetails(messages, memoryIdStr);
            }
            
            return messages;
        } catch (Exception e) {
            log.error("❌ 获取聊天记录失败，内存ID: {}, 错误: {}", memoryIdStr, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 更新内存中的所有消息
     * 此方法会完全替换指定内存ID的所有消息
     * 
     * @param memoryId 内存ID
     * @param messages 新的消息列表
     */
    @Override
    @Transactional
    public void updateMessages(Object memoryId, List<ChatMessage> messages) {
        String memoryIdStr = memoryId.toString();
        log.debug("🔄 更新聊天记录，内存ID: {}, 消息数量: {}", memoryIdStr, messages.size());
        
        // 只在DEBUG级别记录详细信息
        if (log.isDebugEnabled()) {
            logUpdateDetails(messages, memoryIdStr);
        }
        
        try {
            // 首先删除该内存ID的所有现有消息
            long existingCount = chatMessageRepository.countByMemoryId(memoryIdStr);
            log.debug("🗑️ 删除现有消息，内存ID: {}, 现有消息数量: {}", memoryIdStr, existingCount);
            chatMessageRepository.deleteByMemoryId(memoryIdStr);
            
            // 然后保存新的消息列表
            List<ChatMessageEntity> entities = IntStream.range(0, messages.size())
                    .mapToObj(i -> {
                        ChatMessage message = messages.get(i);
                        ChatMessageEntity entity = new ChatMessageEntity();
                        entity.setMemoryId(memoryIdStr);
                        entity.setMessageOrder(i);
                        entity.setMessageType(message.type().name());
                        entity.setMessageContent(ChatMessageSerializer.messageToJson(message));
                        return entity;
                    })
                    .toList();
            
            chatMessageRepository.saveAll(entities);
            
            // 更新缓存
            updateCache(memoryIdStr, messages);
            
            log.debug("✅ 成功更新聊天记录，内存ID: {}, 保存消息数量: {}", memoryIdStr, entities.size());
        } catch (Exception e) {
            // 清除可能损坏的缓存
            invalidateCache(memoryIdStr);
            log.error("❌ 更新聊天记录失败，内存ID: {}, 错误: {}", memoryIdStr, e.getMessage(), e);
            throw new RuntimeException("更新聊天记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除指定内存ID的所有消息
     * 
     * @param memoryId 内存ID
     */
    @Override
    @Transactional
    public void deleteMessages(Object memoryId) {
        String memoryIdStr = memoryId.toString();
        log.debug("🗑️ 删除聊天记录，内存ID: {}", memoryIdStr);
        
        try {
            long deletedCount = chatMessageRepository.countByMemoryId(memoryIdStr);
            chatMessageRepository.deleteByMemoryId(memoryIdStr);
            
            // 清除缓存
            invalidateCache(memoryIdStr);
            
            log.debug("✅ 成功删除聊天记录，内存ID: {}, 删除数量: {}", memoryIdStr, deletedCount);
        } catch (Exception e) {
            log.error("❌ 删除聊天记录失败，内存ID: {}, 错误: {}", memoryIdStr, e.getMessage(), e);
            throw new RuntimeException("删除聊天记录失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 缓存相关方法
     */
    
    /**
     * 获取缓存中的消息（如果有效）
     */
    private List<ChatMessage> getCachedMessages(String memoryId) {
        if (!cacheProperties.isEnabled()) {
            return null;
        }
        
        AtomicLong version = cacheVersion.get(memoryId);
        if (version == null) {
            cacheMisses.incrementAndGet();
            return null;
        }
        
        long currentTime = System.currentTimeMillis();
        long expireMs = cacheProperties.getExpireSeconds() * 1000;
        if (currentTime - version.get() > expireMs) {
            // 缓存过期，清理
            invalidateCache(memoryId);
            cacheMisses.incrementAndGet();
            return null;
        }
        
        cacheHits.incrementAndGet();
        logCacheStats();
        return messageCache.get(memoryId);
    }
    
    /**
     * 更新缓存
     */
    private void updateCache(String memoryId, List<ChatMessage> messages) {
        if (!cacheProperties.isEnabled()) {
            return;
        }
        
        // 检查缓存大小限制
        if (messageCache.size() >= cacheProperties.getMaxSize()) {
            // 简单的LRU策略：清理最旧的条目
            cleanOldestCacheEntries();
        }
        
        messageCache.put(memoryId, List.copyOf(messages));
        cacheVersion.put(memoryId, new AtomicLong(System.currentTimeMillis()));
    }
    
    /**
     * 清除指定会话的缓存
     */
    private void invalidateCache(String memoryId) {
        messageCache.remove(memoryId);
        cacheVersion.remove(memoryId);
    }
    
    /**
     * 记录消息详细信息（仅在DEBUG级别）
     */
    private void logMessageDetails(List<ChatMessage> messages, String memoryId) {
        for (int i = 0; i < messages.size(); i++) {
            ChatMessage message = messages.get(i);
            String messageText = getMessageText(message);
            log.debug("📝 消息[{}]: 类型={}, 内容长度={}", 
                i, message.getClass().getSimpleName(), 
                messageText != null ? messageText.length() : 0);
                
            // 特别关注工具相关消息
            if (message instanceof dev.langchain4j.data.message.AiMessage) {
                var aiMsg = (dev.langchain4j.data.message.AiMessage) message;
                if (aiMsg.hasToolExecutionRequests()) {
                    log.debug("🔧 [存储]AI消息包含工具调用请求: 数量={}", 
                        aiMsg.toolExecutionRequests().size());
                }
            } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
                var toolMsg = (dev.langchain4j.data.message.ToolExecutionResultMessage) message;
                log.debug("🛠️ [存储]工具执行结果消息: 工具名={}, 结果长度={}", 
                    toolMsg.toolName(), toolMsg.text() != null ? toolMsg.text().length() : 0);
            }
        }
    }
    
    /**
     * 记录更新详细信息（仅在DEBUG级别）
     */
    private void logUpdateDetails(List<ChatMessage> messages, String memoryId) {
        for (int i = 0; i < messages.size(); i++) {
            ChatMessage message = messages.get(i);
            String messageText = getMessageText(message);
            log.debug("💾 准备保存消息[{}]: 类型={}, 内容长度={}", 
                i, message.getClass().getSimpleName(), 
                messageText != null ? messageText.length() : 0);
                
            // 特别关注工具相关消息
            if (message instanceof dev.langchain4j.data.message.AiMessage) {
                var aiMsg = (dev.langchain4j.data.message.AiMessage) message;
                if (aiMsg.hasToolExecutionRequests()) {
                    log.debug("🔧 [即将保存]AI消息包含工具调用请求: 数量={}", 
                        aiMsg.toolExecutionRequests().size());
                }
            } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
                var toolMsg = (dev.langchain4j.data.message.ToolExecutionResultMessage) message;
                log.debug("🛠️ [即将保存]工具执行结果消息: 工具名={}, 结果长度={}", 
                    toolMsg.toolName(), toolMsg.text() != null ? toolMsg.text().length() : 0);
            }
        }
    }

    /**
     * 辅助方法：获取消息文本内容
     */
    private String getMessageText(ChatMessage message) {
        if (message instanceof dev.langchain4j.data.message.UserMessage) {
            return ((dev.langchain4j.data.message.UserMessage) message).singleText();
        } else if (message instanceof dev.langchain4j.data.message.AiMessage) {
            return ((dev.langchain4j.data.message.AiMessage) message).text();
        } else if (message instanceof dev.langchain4j.data.message.SystemMessage) {
            return ((dev.langchain4j.data.message.SystemMessage) message).text();
        } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
            return ((dev.langchain4j.data.message.ToolExecutionResultMessage) message).text();
        }
        return null;
    }
    
    /**
     * 清理最旧的缓存条目（简单LRU策略）
     */
    private void cleanOldestCacheEntries() {
        // 找到最旧的10%条目并清理
        int entriesToRemove = Math.max(1, cacheProperties.getMaxSize() / 10);
        
        cacheVersion.entrySet().stream()
            .sorted(Map.Entry.comparingByValue((v1, v2) -> Long.compare(v1.get(), v2.get())))
            .limit(entriesToRemove)
            .map(Map.Entry::getKey)
            .forEach(this::invalidateCache);
    }
    
    /**
     * 记录缓存统计信息
     */
    private void logCacheStats() {
        if (cacheProperties.isEnableStats()) {
            long hits = cacheHits.get();
            long misses = cacheMisses.get();
            long total = hits + misses;
            if (total > 0 && total % 100 == 0) { // 每100次访问记录一次统计
                double hitRate = (double) hits / total * 100;
                log.info("📊 缓存统计 - 命中率: {:.1f}% ({}/{}), 缓存大小: {}", 
                    hitRate, hits, total, messageCache.size());
            }
        }
    }
}