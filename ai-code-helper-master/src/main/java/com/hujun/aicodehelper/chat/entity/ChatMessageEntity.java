package com.hujun.aicodehelper.chat.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 聊天消息实体类
 * 用于存储 LangChain4j ChatMessage 的持久化数据
 */
@Data
@Entity
@Table(name = "chat_messages", indexes = {
    @Index(name = "idx_memory_id", columnList = "memoryId"),
    @Index(name = "idx_memory_id_order", columnList = "memoryId, messageOrder")
})
public class ChatMessageEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 内存ID，用于区分不同的聊天会话
     */
    @Column(nullable = false, length = 255)
    private String memoryId;

    /**
     * 消息在对话中的顺序
     */
    @Column(nullable = false)
    private Integer messageOrder;

    /**
     * 消息类型（USER, AI, SYSTEM, TOOL_EXECUTION_REQUEST, TOOL_EXECUTION_RESULT）
     */
    @Column(nullable = false, length = 50)
    private String messageType;

    /**
     * 消息内容（JSON格式存储完整的 ChatMessage）
     */
    @Column(nullable = false, columnDefinition = "TEXT")
    private String messageContent;

    @CreationTimestamp
    @Column(nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;
}