package com.hujun.aicodehelper.chat.example;

import com.hujun.aicodehelper.chat.service.ChatMemoryService;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 聊天记忆使用示例
 * 展示如何使用 PostgreSQL 持久化聊天记忆
 * 
 * 启动方式：使用 --spring.profiles.active=postgresql,demo 参数
 */
@Slf4j
@Component
@Profile("demo")
@RequiredArgsConstructor
public class ChatMemoryExample implements CommandLineRunner {

    private final ChatMemoryService chatMemoryService;

    @Override
    public void run(String... args) throws Exception {
        log.info("🚀 开始聊天记忆示例演示");
        
        // 示例1：基础会话记忆
        basicChatMemoryExample();
        
        // 示例2：用户专属记忆
        userSpecificMemoryExample();
        
        // 示例3：查询和管理记忆
        memoryManagementExample();
        
        log.info("✅ 聊天记忆示例演示完成");
    }

    /**
     * 基础聊天记忆示例
     */
    private void basicChatMemoryExample() {
        log.info("\n📝 === 基础聊天记忆示例 ===");
        
        String sessionId = "example_session_" + System.currentTimeMillis();
        
        // 添加系统消息
        SystemMessage systemMsg = SystemMessage.from("你是一个专业的Java编程助手，请用简洁明了的方式回答问题。");
        chatMemoryService.addMessage(sessionId, systemMsg);
        
        // 添加用户消息
        UserMessage userMsg1 = UserMessage.from("什么是Java的多态性？");
        chatMemoryService.addMessage(sessionId, userMsg1);
        
        // 模拟AI回复
        AiMessage aiMsg1 = AiMessage.from("多态性是Java面向对象编程的三大特性之一，允许同一个接口表示不同的具体类型。");
        chatMemoryService.addMessage(sessionId, aiMsg1);
        
        // 继续对话
        UserMessage userMsg2 = UserMessage.from("能举个具体例子吗？");
        chatMemoryService.addMessage(sessionId, userMsg2);
        
        AiMessage aiMsg2 = AiMessage.from("例如：Animal animal = new Dog(); animal.makeSound(); 这里Animal引用可以指向Dog或Cat对象。");
        chatMemoryService.addMessage(sessionId, aiMsg2);
        
        // 获取完整对话历史
        List<ChatMessage> messages = chatMemoryService.getMessages(sessionId);
        log.info("💬 会话 {} 包含 {} 条消息", sessionId, messages.size());
        
        // 打印对话内容
        for (int i = 0; i < messages.size(); i++) {
            ChatMessage msg = messages.get(i);
            log.info("  {}. [{}] {}", i + 1, msg.type(), truncateMessage(getMessageText(msg)));
        }
    }

    /**
     * 用户专属记忆示例
     */
    private void userSpecificMemoryExample() {
        log.info("\n👤 === 用户专属记忆示例 ===");
        
        String userId = "user_123";
        
        // 用户第一次对话
        UserMessage userMsg1 = UserMessage.from("我是Java初学者，请推荐学习路径");
        chatMemoryService.addUserMessage(userId, userMsg1);
        
        AiMessage aiMsg1 = AiMessage.from("建议从Java基础语法开始：变量、数据类型、控制结构、面向对象概念。");
        chatMemoryService.addUserMessage(userId, aiMsg1);
        
        // 用户第二次对话（系统会记住之前的对话）
        UserMessage userMsg2 = UserMessage.from("我已经学完了基础语法，下一步学什么？");
        chatMemoryService.addUserMessage(userId, userMsg2);
        
        AiMessage aiMsg2 = AiMessage.from("很好！既然基础语法已掌握，建议学习：集合框架、异常处理、IO流、多线程。");
        chatMemoryService.addUserMessage(userId, aiMsg2);
        
        // 获取用户完整学习记录
        List<ChatMessage> userMessages = chatMemoryService.getUserMessages(userId);
        log.info("👤 用户 {} 的学习记录包含 {} 条消息", userId, userMessages.size());
        
        for (int i = 0; i < userMessages.size(); i++) {
            ChatMessage msg = userMessages.get(i);
            log.info("  {}. [{}] {}", i + 1, msg.type(), truncateMessage(getMessageText(msg)));
        }
    }

    /**
     * 记忆管理示例
     */
    private void memoryManagementExample() {
        log.info("\n🔧 === 记忆管理示例 ===");
        
        String testSessionId = "test_session_" + System.currentTimeMillis();
        String testUserId = "test_user_456";
        
        // 检查是否有历史记录
        log.info("📊 记录统计:");
        log.info("  - 会话 {} 是否有记录: {}", testSessionId, chatMemoryService.hasMessages(testSessionId));
        log.info("  - 用户 {} 是否有记录: {}", testUserId, chatMemoryService.hasUserMessages(testUserId));
        
        // 添加一些测试消息
        chatMemoryService.addMessage(testSessionId, UserMessage.from("测试消息1"));
        chatMemoryService.addMessage(testSessionId, AiMessage.from("测试回复1"));
        chatMemoryService.addUserMessage(testUserId, UserMessage.from("用户测试消息"));
        
        // 查看消息数量
        log.info("  - 会话 {} 消息数量: {}", testSessionId, chatMemoryService.getMessageCount(testSessionId));
        log.info("  - 用户 {} 消息数量: {}", testUserId, chatMemoryService.getUserMessageCount(testUserId));
        
        // 清空记忆（演示功能，实际使用需谨慎）
        log.info("🗑️ 清空测试记录...");
        chatMemoryService.clearChatMemory(testSessionId);
        chatMemoryService.clearUserChatMemory(testUserId);
        
        // 验证清空结果
        log.info("  - 清空后会话 {} 消息数量: {}", testSessionId, chatMemoryService.getMessageCount(testSessionId));
        log.info("  - 清空后用户 {} 消息数量: {}", testUserId, chatMemoryService.getUserMessageCount(testUserId));
    }

    /**
     * 截断长消息用于日志显示
     */
    private String truncateMessage(String message) {
        if (message == null) return "null";
        return message.length() > 50 ? message.substring(0, 50) + "..." : message;
    }
    
    /**
     * 获取消息文本内容
     */
    private String getMessageText(ChatMessage message) {
        if (message instanceof UserMessage) {
            return ((UserMessage) message).singleText();
        } else if (message instanceof AiMessage) {
            return ((AiMessage) message).text();
        } else if (message instanceof SystemMessage) {
            return ((SystemMessage) message).text();
        } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
            return ((dev.langchain4j.data.message.ToolExecutionResultMessage) message).text();
        }
        return null;
    }
}