package com.hujun.aicodehelper.chat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 聊天记忆缓存配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "chat.memory.cache")
public class ChatMemoryCacheProperties {
    
    /**
     * 是否启用缓存，默认启用
     */
    private boolean enabled = true;
    
    /**
     * 缓存过期时间（秒），默认30秒
     */
    private long expireSeconds = 30;
    
    /**
     * 最大缓存条目数，默认1000
     */
    private int maxSize = 1000;
    
    /**
     * 是否启用统计信息，默认false
     */
    private boolean enableStats = false;
}
