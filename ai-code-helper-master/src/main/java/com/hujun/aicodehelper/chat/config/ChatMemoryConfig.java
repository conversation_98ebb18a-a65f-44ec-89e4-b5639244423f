package com.hujun.aicodehelper.chat.config;

import com.hujun.aicodehelper.chat.store.PostgreSqlChatMemoryStore;
import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
// import dev.langchain4j.memory.chat.TokenWindowChatMemory;
// import dev.langchain4j.model.Tokenizer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 聊天记忆配置类
 * 提供基于 PostgreSQL 的持久化聊天记忆功能
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class ChatMemoryConfig {

    private final PostgreSqlChatMemoryStore postgreSqlChatMemoryStore;
    private final ChatMemoryProperties chatMemoryProperties;

    /**
     * 创建聊天记忆提供者
     * 支持基于消息数量和令牌数量的两种窗口模式
     * 
     * @return ChatMemoryProvider
     */
    @Bean
    @Primary
    public ChatMemoryProvider chatMemoryProvider() {
        log.info("🧠 初始化聊天记忆提供者");
        log.info("   - 存储类型: PostgreSQL 持久化");
        log.info("   - 窗口类型: {}", chatMemoryProperties.getType());
        
        // TODO: TokenWindowChatMemory 依赖于 Tokenizer 接口，目前 LangChain4j 1.1.0 版本中 Tokenizer 接口已被移除或移动。
        // 暂时只支持 MessageWindowChatMemory，后续如果需要再实现 Tokenizer 接口。
        // if ("token".equalsIgnoreCase(type)) {
        //     log.info("   - 最大令牌数: {}", chatMemoryProperties.getMaxTokens());
        //     return memoryId -> TokenWindowChatMemory.builder()
        //             .id(memoryId)
        //             .maxTokens(chatMemoryProperties.getMaxTokens(), new SimpleTokenizer()) // 简单的令牌计算器
        //             .chatMemoryStore(postgreSqlChatMemoryStore)
        //             .build();
        // } else {
            log.info("   - 最大消息数: {}", chatMemoryProperties.getMaxMessages());
            return memoryId -> MessageWindowChatMemory.builder()
                    .id(memoryId)
                    .maxMessages(chatMemoryProperties.getMaxMessages())
                    .chatMemoryStore(postgreSqlChatMemoryStore)
                    .build();
        // }
    }

    /**
     * 创建专用于用户会话的聊天记忆提供者
     * 每个用户会有独立的聊天记忆
     * 
     * @return ChatMemoryProvider
     */
    @Bean("userChatMemoryProvider")
    public ChatMemoryProvider userChatMemoryProvider() {
        log.info("👤 初始化用户聊天记忆提供者");
        
        return userId -> {
            String memoryId = "user_" + userId;
            return MessageWindowChatMemory.builder()
                    .id(memoryId)
                    .maxMessages(chatMemoryProperties.getMaxMessages())
                    .chatMemoryStore(postgreSqlChatMemoryStore)
                    .build();
        };
    }

    /**
     * 简单的令牌计算器实现
     * 用于 TokenWindowChatMemory
     */
    @Deprecated
    private static class SimpleTokenizer { // implements Tokenizer {
        // @Override
        public int estimateTokenCountInText(String text) {
            if (text == null || text.isEmpty()) {
                return 0;
            }
            // 简单估算：中文字符按2个token计算，英文单词按1个token计算
            return (int) Math.ceil(text.length() / 3.0);
        }

        // @Override
        public int estimateTokenCountInMessage(dev.langchain4j.data.message.ChatMessage message) {
            String text = getMessageText(message);
            if (text == null) {
                return 0;
            }
            return estimateTokenCountInText(text);
        }
        
        private String getMessageText(dev.langchain4j.data.message.ChatMessage message) {
            if (message instanceof dev.langchain4j.data.message.UserMessage) {
                return ((dev.langchain4j.data.message.UserMessage) message).singleText();
            } else if (message instanceof dev.langchain4j.data.message.AiMessage) {
                return ((dev.langchain4j.data.message.AiMessage) message).text();
            } else if (message instanceof dev.langchain4j.data.message.SystemMessage) {
                return ((dev.langchain4j.data.message.SystemMessage) message).text();
            } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
                return ((dev.langchain4j.data.message.ToolExecutionResultMessage) message).text();
            }
            return null;
        }

        // @Override
        public int estimateTokenCountInMessages(Iterable<dev.langchain4j.data.message.ChatMessage> messages) {
            int totalTokens = 0;
            for (dev.langchain4j.data.message.ChatMessage message : messages) {
                totalTokens += estimateTokenCountInMessage(message);
            }
            return totalTokens;
        }
    }


}