package com.hujun.aicodehelper.context;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 对话上下文管理器服务
 * 负责管理和维护学生的对话上下文状态
 */
@Slf4j
@Service
public class ConversationContextManager {

    // 内存存储会话上下文（实际应用中可以集成Redis或数据库）
    private final Map<String, ConversationContext> contextCache = new ConcurrentHashMap<>();
    
    // 上下文过期时间（2小时）
    private static final long CONTEXT_EXPIRY_HOURS = 2;

    /**
     * 获取或创建会话上下文
     */
    public ConversationContext getOrCreate(String sessionId) {
        return getOrCreate(sessionId, null);
    }

    /**
     * 获取或创建会话上下文（指定学生ID）
     */
    public ConversationContext getOrCreate(String sessionId, String studentId) {
        ConversationContext context = contextCache.get(sessionId);
        
        if (context == null) {
            // 创建新的上下文
            context = ConversationContext.createNew(sessionId, studentId);
            contextCache.put(sessionId, context);
            log.info("创建新的对话上下文：sessionId={}, studentId={}", sessionId, studentId);
        } else {
            // 检查上下文是否过期
            if (isExpired(context)) {
                log.info("对话上下文已过期，重新创建：sessionId={}", sessionId);
                context = ConversationContext.createNew(sessionId, studentId);
                contextCache.put(sessionId, context);
            } else {
                // 更新最后活动时间
                context.setLastUpdateTime(LocalDateTime.now());
            }
        }
        
        return context;
    }

    /**
     * 保存上下文
     */
    public void save(ConversationContext context) {
        if (context != null && context.getSessionId() != null) {
            context.setLastUpdateTime(LocalDateTime.now());
            contextCache.put(context.getSessionId(), context);
        }
    }

    /**
     * 获取上下文（不创建）
     */
    public ConversationContext get(String sessionId) {
        ConversationContext context = contextCache.get(sessionId);
        
        if (context != null && isExpired(context)) {
            // 清理过期上下文
            contextCache.remove(sessionId);
            return null;
        }
        
        return context;
    }

    /**
     * 更新学生学习状态
     */
    public void updateLearningState(String sessionId, String subject, String problem) {
        ConversationContext context = getOrCreate(sessionId);
        context.setCurrentSubject(subject);
        context.setCurrentProblem(problem);
        save(context);
    }

    /**
     * 记录学生错误
     */
    public void recordMistake(String sessionId, String mistake) {
        ConversationContext context = getOrCreate(sessionId);
        context.recordMistake(mistake);
        save(context);
        log.debug("记录错误：sessionId={}, mistake={}", sessionId, mistake);
    }

    /**
     * 记录学生成功
     */
    public void recordSuccess(String sessionId) {
        ConversationContext context = getOrCreate(sessionId);
        context.recordSuccess();
        save(context);
        log.debug("记录成功：sessionId={}", sessionId);
    }

    /**
     * 更新知识掌握度
     */
    public void updateKnowledgeMastery(String sessionId, String concept, Float score) {
        ConversationContext context = getOrCreate(sessionId);
        context.updateKnowledgeMastery(concept, score);
        save(context);
        log.debug("更新知识掌握度：sessionId={}, concept={}, score={}", sessionId, concept, score);
    }

    /**
     * 添加对话记录
     */
    public void addConversation(String sessionId, String message) {
        ConversationContext context = getOrCreate(sessionId);
        context.addToHistory(message);
        save(context);
    }

    /**
     * 清理过期上下文
     */
    public void cleanupExpiredContexts() {
        contextCache.entrySet().removeIf(entry -> {
            boolean expired = isExpired(entry.getValue());
            if (expired) {
                log.info("清理过期上下文：sessionId={}", entry.getKey());
            }
            return expired;
        });
    }

    /**
     * 获取活跃会话数量
     */
    public int getActiveSessionCount() {
        cleanupExpiredContexts();
        return contextCache.size();
    }

    /**
     * 获取会话统计信息
     */
    public String getSessionStats(String sessionId) {
        ConversationContext context = get(sessionId);
        if (context == null) {
            return "会话不存在或已过期";
        }

        StringBuilder stats = new StringBuilder();
        stats.append("会话统计信息:\n");
        stats.append("- 会话ID: ").append(context.getSessionId()).append("\n");
        stats.append("- 学生ID: ").append(context.getStudentId()).append("\n");
        stats.append("- 对话轮次: ").append(context.getRoundCount()).append("\n");
        stats.append("- 完成题目: ").append(context.getCompletedProblems()).append("\n");
        stats.append("- 连续错误: ").append(context.getConsecutiveMistakes()).append("\n");
        
        context.updateStudyTime();
        stats.append("- 学习时长: ").append(context.getStudyTimeMinutes()).append("分钟\n");
        
        if (context.getMasteredConcepts() != null && !context.getMasteredConcepts().isEmpty()) {
            stats.append("- 已掌握概念: ").append(String.join(", ", context.getMasteredConcepts())).append("\n");
        }
        
        if (context.getIdentifiedGaps() != null && !context.getIdentifiedGaps().isEmpty()) {
            stats.append("- 知识盲区: ").append(String.join(", ", context.getIdentifiedGaps())).append("\n");
        }

        return stats.toString();
    }

    /**
     * 重置会话状态
     */
    public void resetSession(String sessionId) {
        ConversationContext context = get(sessionId);
        if (context != null) {
            String studentId = context.getStudentId();
            context = ConversationContext.createNew(sessionId, studentId);
            contextCache.put(sessionId, context);
            log.info("重置会话状态：sessionId={}", sessionId);
        }
    }

    /**
     * 删除会话
     */
    public void removeSession(String sessionId) {
        contextCache.remove(sessionId);
        log.info("删除会话：sessionId={}", sessionId);
    }

    /**
     * 检查上下文是否过期
     */
    private boolean isExpired(ConversationContext context) {
        if (context.getLastUpdateTime() == null) {
            return true;
        }
        
        LocalDateTime expiryTime = context.getLastUpdateTime().plusHours(CONTEXT_EXPIRY_HOURS);
        return LocalDateTime.now().isAfter(expiryTime);
    }

    /**
     * 批量更新学习数据
     */
    public void batchUpdateLearningData(String sessionId, Map<String, Object> learningData) {
        ConversationContext context = getOrCreate(sessionId);
        
        // 更新各种学习数据
        if (learningData.containsKey("subject")) {
            context.setCurrentSubject((String) learningData.get("subject"));
        }
        
        if (learningData.containsKey("problem")) {
            context.setCurrentProblem((String) learningData.get("problem"));
        }
        
        if (learningData.containsKey("stuckPoint")) {
            context.setStuckPoint((String) learningData.get("stuckPoint"));
        }
        
        if (learningData.containsKey("learningPattern")) {
            context.setLearningPattern((String) learningData.get("learningPattern"));
        }
        
        // 批量更新知识掌握度
        if (learningData.containsKey("knowledgeMastery")) {
            @SuppressWarnings("unchecked")
            Map<String, Float> masteryData = (Map<String, Float>) learningData.get("knowledgeMastery");
            for (Map.Entry<String, Float> entry : masteryData.entrySet()) {
                context.updateKnowledgeMastery(entry.getKey(), entry.getValue());
            }
        }
        
        save(context);
        log.debug("批量更新学习数据：sessionId={}, dataSize={}", sessionId, learningData.size());
    }
}
