package com.hujun.aicodehelper.context;

import lombok.Data;
import lombok.Builder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 对话上下文管理器
 * 追踪学生的学习状态和对话历史，用于触发结构化输出
 */
@Data
@Builder
public class ConversationContext {
    
    // 基础信息
    private String sessionId;
    private String studentId;
    private LocalDateTime startTime;
    private LocalDateTime lastUpdateTime;
    
    // 对话状态
    private Integer roundCount;                          // 对话轮次
    private String currentTopic;                         // 当前讨论主题
    private String previousTopic;                        // 上一个主题
    private List<String> conversationHistory;           // 对话历史
    
    // 学习状态
    private String currentSubject;                       // 当前学科
    private String currentProblem;                       // 当前问题
    private List<String> attemptedMethods;              // 尝试过的解题方法
    private String stuckPoint;                          // 卡壳点
    private Integer consecutiveMistakes;                // 连续错误次数
    private Integer completedProblems;                  // 完成题目数
    
    // 知识掌握情况
    private Map<String, Float> knowledgeMastery;        // 知识点掌握度 (0-1)
    private List<String> identifiedGaps;               // 已识别的知识盲区
    private List<String> masteredConcepts;             // 已掌握的概念
    
    // 触发标记
    private LocalDateTime lastStructuredOutput;         // 上次结构化输出时间
    private String lastTriggerType;                     // 上次触发类型
    
    // 学习分析
    private Integer studyTimeMinutes;                   // 学习时长(分钟)
    private String learningPattern;                     // 学习模式
    private List<String> commonMistakes;               // 常见错误
    
    /**
     * 默认构造器
     */
    public static ConversationContext createNew(String sessionId, String studentId) {
        return ConversationContext.builder()
            .sessionId(sessionId)
            .studentId(studentId)
            .startTime(LocalDateTime.now())
            .lastUpdateTime(LocalDateTime.now())
            .roundCount(0)
            .consecutiveMistakes(0)
            .completedProblems(0)
            .conversationHistory(new ArrayList<>())
            .attemptedMethods(new ArrayList<>())
            .knowledgeMastery(new HashMap<>())
            .identifiedGaps(new ArrayList<>())
            .masteredConcepts(new ArrayList<>())
            .commonMistakes(new ArrayList<>())
            .studyTimeMinutes(0)
            .build();
    }
    
    /**
     * 更新对话轮次
     */
    public void incrementRound() {
        this.roundCount++;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 添加对话历史
     */
    public void addToHistory(String message) {
        if (conversationHistory == null) {
            conversationHistory = new ArrayList<>();
        }
        conversationHistory.add(message);
        
        // 保持历史记录在合理范围内
        if (conversationHistory.size() > 20) {
            conversationHistory = conversationHistory.subList(conversationHistory.size() - 20, conversationHistory.size());
        }
    }
    
    /**
     * 检查是否在讨论同一主题
     */
    public boolean isSameTopic() {
        return currentTopic != null && currentTopic.equals(previousTopic);
    }
    
    /**
     * 更新主题
     */
    public void updateTopic(String newTopic) {
        this.previousTopic = this.currentTopic;
        this.currentTopic = newTopic;
        
        // 如果换了主题，重置连续错误计数
        if (!isSameTopic()) {
            this.consecutiveMistakes = 0;
        }
    }
    
    /**
     * 记录错误
     */
    public void recordMistake(String mistake) {
        this.consecutiveMistakes++;
        if (commonMistakes == null) {
            commonMistakes = new ArrayList<>();
        }
        commonMistakes.add(mistake);
    }
    
    /**
     * 记录成功解题
     */
    public void recordSuccess() {
        this.completedProblems++;
        this.consecutiveMistakes = 0; // 重置错误计数
    }
    
    /**
     * 更新知识掌握度
     */
    public void updateKnowledgeMastery(String concept, Float score) {
        if (knowledgeMastery == null) {
            knowledgeMastery = new HashMap<>();
        }
        knowledgeMastery.put(concept, score);
        
        // 根据掌握度更新相关列表
        if (score >= 0.8f && !masteredConcepts.contains(concept)) {
            masteredConcepts.add(concept);
            identifiedGaps.remove(concept); // 从盲区列表中移除
        } else if (score < 0.4f && !identifiedGaps.contains(concept)) {
            identifiedGaps.add(concept);
        }
    }
    
    /**
     * 计算学习时长
     */
    public void updateStudyTime() {
        if (startTime != null) {
            long minutes = java.time.Duration.between(startTime, LocalDateTime.now()).toMinutes();
            this.studyTimeMinutes = (int) minutes;
        }
    }
    
    /**
     * 标记结构化输出已生成
     */
    public void markStructuredOutput(String triggerType) {
        this.lastStructuredOutput = LocalDateTime.now();
        this.lastTriggerType = triggerType;
    }
    
    /**
     * 检查是否应该避免重复触发
     */
    public boolean shouldAvoidRepeatTrigger(String triggerType) {
        if (lastStructuredOutput == null || lastTriggerType == null) {
            return false;
        }
        
        // 同类型触发间隔至少5分钟
        long minutesSinceLastTrigger = java.time.Duration
            .between(lastStructuredOutput, LocalDateTime.now())
            .toMinutes();
            
        return triggerType.equals(lastTriggerType) && minutesSinceLastTrigger < 5;
    }
}
