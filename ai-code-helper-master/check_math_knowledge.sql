-- 检查数学知识库表数据的脚本
-- 用于验证RagConfig向量化JSON数据到math_rag_knowledge表的结果

-- 1. 查看表中的总记录数
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN embedding IS NOT NULL THEN 1 END) as records_with_embedding
FROM math_rag_knowledge;

-- 2. 查看所有记录的基本信息
SELECT
    id,
    type,
    topic,
    grade,
    difficulty,
    source,
    LEFT(content, 100) || '...' as content_preview,
    CASE WHEN embedding IS NOT NULL THEN '✅' ELSE '❌' END as has_embedding,
    created_at
FROM math_rag_knowledge
ORDER BY created_at DESC;

-- 3. 查看向量数据的统计信息
SELECT
    type,
    topic,
    grade,
    COUNT(*) as count
FROM math_rag_knowledge
GROUP BY type, topic, grade
ORDER BY type, topic, grade;

-- 4. 检查向量相似度搜索是否可用（测试查询）
-- 注意：这需要实际的查询向量，这里只是示例
/*
SELECT
    id,
    type,
    content,
    topic,
    grade,
    1 - (embedding <=> '[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]'::vector(1536)) as similarity
FROM math_rag_knowledge
WHERE 1 - (embedding <=> '[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]'::vector(1536)) > 0.5
ORDER BY embedding <=> '[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]'::vector(1536)
LIMIT 5;
*/
