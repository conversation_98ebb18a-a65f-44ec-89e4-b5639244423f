<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式响应测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
        }
        .log {
            background-color: #f5f5f5;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .response {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            min-height: 100px;
        }
        button {
            background-color: #4caf50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>RAG流式响应测试</h1>
    
    <div class="container">
        <h3>测试参数</h3>
        <label>消息: <input type="text" id="messageInput" value="7/10×4/21等于多少？请化简到最简形式。" /></label><br>
        <label>会话ID: <input type="text" id="memoryIdInput" value="test123" /></label><br>
        <button id="testBtn" onclick="testStream()">开始测试</button>
        <button id="stopBtn" onclick="stopStream()" disabled>停止测试</button>
    </div>

    <div class="container">
        <h3>AI响应</h3>
        <div id="response" class="response"></div>
    </div>

    <div class="container">
        <h3>调试日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let currentEventSource = null;
        let hasReceivedData = false;
        let timeoutId = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function resetTimeout() {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            timeoutId = setTimeout(() => {
                if (!hasReceivedData) {
                    log('❌ 响应超时，未收到任何数据');
                    if (currentEventSource) {
                        currentEventSource.close();
                    }
                    resetUI();
                }
            }, 60000); // 60秒超时
        }

        function resetUI() {
            document.getElementById('testBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            currentEventSource = null;
        }

        function testStream() {
            const message = document.getElementById('messageInput').value;
            const memoryId = document.getElementById('memoryIdInput').value;
            
            if (!message.trim()) {
                alert('请输入消息');
                return;
            }

            // 重置状态
            hasReceivedData = false;
            document.getElementById('response').innerHTML = '';
            document.getElementById('log').innerHTML = '';
            document.getElementById('testBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;

            // 构建URL
            const params = new URLSearchParams({
                memoryId: memoryId,
                message: message
            });
            const url = `http://localhost:8081/api/ai/rag/chat-stream?${params.toString()}`;
            
            log(`🚀 开始连接: ${url}`);
            
            // 设置超时
            resetTimeout();
            
            // 创建EventSource
            currentEventSource = new EventSource(url);
            
            currentEventSource.onopen = function(event) {
                log('✅ 连接已建立');
            };
            
            currentEventSource.onmessage = function(event) {
                hasReceivedData = true;
                if (timeoutId) {
                    clearTimeout(timeoutId);
                    timeoutId = null;
                }
                
                const data = event.data;
                log(`📤 收到数据: ${data}`);
                
                // 检查是否是流结束标识
                if (data === '[DONE]' || event.type === 'end') {
                    log('🏁 流式响应正常结束');
                    currentEventSource.close();
                    resetUI();
                    return;
                }
                
                // 检查是否是错误消息
                if (data && data.startsWith('{"error"')) {
                    try {
                        const errorData = JSON.parse(data);
                        log(`❌ 服务端错误: ${errorData.error}`);
                        currentEventSource.close();
                        resetUI();
                        return;
                    } catch (e) {
                        log(`⚠️ 解析错误消息失败: ${e.message}`);
                    }
                }
                
                // 正常的流式内容
                if (data && data.trim() !== '') {
                    const responseDiv = document.getElementById('response');
                    responseDiv.innerHTML += data;
                }
            };
            
            currentEventSource.onerror = function(error) {
                log(`📊 连接状态: ${currentEventSource.readyState}`);
                
                // 清理超时定时器
                if (timeoutId) {
                    clearTimeout(timeoutId);
                    timeoutId = null;
                }
                
                // 只有在连接未正常关闭时才报告错误
                if (currentEventSource.readyState === EventSource.CONNECTING) {
                    log('❌ 连接失败');
                } else if (currentEventSource.readyState === EventSource.OPEN) {
                    log('❌ 连接中断');
                } else {
                    log('ℹ️ 连接正常关闭');
                }
                
                // 确保连接被关闭
                if (currentEventSource.readyState !== EventSource.CLOSED) {
                    currentEventSource.close();
                }
                resetUI();
            };
            
            // 处理连接关闭
            currentEventSource.addEventListener('close', function() {
                log('🔒 连接已关闭');
                if (timeoutId) {
                    clearTimeout(timeoutId);
                    timeoutId = null;
                }
                resetUI();
            });
        }

        function stopStream() {
            if (currentEventSource) {
                log('🛑 手动停止连接');
                currentEventSource.close();
                resetUI();
            }
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            log('📋 页面加载完成，准备测试');
        };
    </script>
</body>
</html>
