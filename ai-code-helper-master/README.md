# 🤖 AI 智能助手 - LangChain4j 实战项目

> 基于 LangChain4j + Google Gemini 的多功能 AI 助手平台

本项目集成了**英语学习辅导**、**文档智能解析**、**RAG增强问答**等多项功能，从 0 到 1 展示了现代 AI 应用的核心技术实践：会话记忆、结构化输出、RAG、工具调用、MCP、护轨、可观测性、向量化处理等。

## ✨ 项目介绍

### 核心功能
- **🎓 英语学习助手**: 专业的B2级英语学习指导，涵盖语法、口语、听力、阅读、写作等各个方面
- **📄 文档智能处理**: PDF解析、OCR识别、JSON转Markdown等文档处理能力  
- **🧠 RAG增强问答**: 基于本地知识库的智能问答，提供精准的英语学习指导
- **💡 编程学习导师**: 提供清晰的学习路线规划和个性化建议


### 技术架构

#### AI 服务
- **LangChain4j 1.1.0**: 业界领先的AI应用开发框架
- **Google Gemini**: gemini-2.5-flash-lite 模型，高效可靠
- **文本嵌入**: text-embedding-004 向量化模型
- **流式响应**: 实时打字机效果，提升用户体验

#### 数据存储
- **单一数据库架构**: PostgreSQL+PGVector统一存储聊天记忆和向量数据
- **向量数据库**: 支持高效的相似度搜索和内容检索

#### 安全与监控
- **输入安全防护**: 检测敏感内容，确保应用安全
- **详细日志记录**: 向量化过程和检索性能的完整监控

#### 工具集成
- **RAG检索增强**: 结合本地知识库，提供精准答案
- **MCP协议支持**: 模型上下文协议，增强AI能力
- **文档处理工具**: PDF解析、OCR识别、格式转换
- **多模态处理**: 支持文本、图像等多种数据类型



## 🚀 快速开始

### 环境要求

- **Java**: JDK 21+
- **Node.js**: 16.0+
- **Maven**: 3.6+
- **Google Gemini API**: 需申请API密钥
- **数据库**: PostgreSQL 15+ (需安装pgvector扩展)
- **OCR支持**: Tesseract (用于PDF扫描版识别，可选)

### 启动步骤

#### 1. 数据库配置
```bash
# PostgreSQL 配置 (用于向量存储和聊天记忆)
# 安装 pgvector 扩展
CREATE EXTENSION IF NOT EXISTS vector;
```

#### 2. 后端启动
```bash
# 克隆项目
git clone <repository-url>
cd ai-code-helper

# 配置API密钥
# 编辑 src/main/resources/application-postgresql.yml
# 填入您的 Google Gemini API 密钥和数据库连接信息

# 启动AI助手服务
mvn spring-boot:run

# 或启动文档处理服务
mvn spring-boot:run -Dspring-boot.run.main-class=com.hujun.pdfparser.MarkdownApplication
```

#### 3. 前端启动
```bash
# 进入前端目录
cd ai-code-helper-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

#### 4. 访问应用
- **英语学习前端**: `http://localhost:5173`
- **AI助手API**: `http://localhost:8081/api`
- **文档处理API**: `http://localhost:8081/api/markdown`



## 🏗️ 技术架构

```
┌──────────────────────────────────────────────────────────────┐
│                     AI 智能助手平台                          │
├──────────────────────────────────────────────────────────────┤
│  前端层                                                       │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   Vue.js 3      │  │  文档处理界面    │                   │
│  │   - 英语学习     │  │  - PDF上传      │                   │
│  │   - 聊天界面     │  │  - 格式转换     │                   │
│  │   - RAG问答      │  │  - 批量处理     │                   │
│  │   - 实时流式     │  │  - 结果展示     │                   │
│  └─────────────────┘  └─────────────────┘                   │
├──────────────────────────────────────────────────────────────┤
│  后端服务层                                                   │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │  AI助手服务      │  │  文档处理服务    │                   │
│  │  - 英语学习指导  │  │  - PDF解析      │                   │
│  │  - RAG增强问答   │  │  - OCR识别      │                   │
│  │  - 流式响应      │  │  - JSON转MD     │                   │
│  │  - 安全防护      │  │  - 数据清洗     │                   │
│  └─────────────────┘  └─────────────────┘                   │
├──────────────────────────────────────────────────────────────┤
│  AI 引擎层                                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  LangChain4j 框架                      │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐  │ │
│  │  │ 对话管理     │ │ RAG检索     │ │ 文档处理工具     │  │ │
│  │  │ - 会话记忆   │ │ - 向量搜索   │ │ - Tika解析      │  │ │
│  │  │ - 上下文     │ │ - 语义检索   │ │ - Tesseract OCR │  │ │
│  │  │ - 流式输出   │ │ - 知识融合   │ │ - 格式转换      │  │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
├──────────────────────────────────────────────────────────────┤
│  AI 模型层                                                    │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │  Google Gemini  │  │  Embedding模型   │                   │
│  │  - 对话生成     │  │  - 文本向量化   │                   │
│  │  - 内容理解     │  │  - 语义计算     │                   │
│  │  - 多语言支持   │  │  - 相似度搜索   │                   │
│  └─────────────────┘  └─────────────────┘                   │
├──────────────────────────────────────────────────────────────┤
│  数据存储层                                                   │
│  ┌─────────────────┐                                        │
│  │  PostgreSQL     │                                        │
│  │  + PGVector     │                                        │
│  │  - 向量存储     │                                        │
│  │  - 聊天记忆     │                                        │
│  │  - 用户会话     │                                        │
│  │  - 语义搜索     │                                        │
│  │  - 文档索引     │                                        │
│  └─────────────────┘                                        │
└──────────────────────────────────────────────────────────────┘
```



## 📦 核心模块

### AI 服务层
- **`SmartAiCodeHelperService`**: 智能对话服务，支持多模型选择
- **`RagService`**: RAG增强问答服务，提供知识库检索
- **`GeminiChatModelConfig`**: Google Gemini模型配置管理
- **`RagConfig`**: 检索增强生成配置
- **`McpConfig`**: 模型上下文协议支持

### 文档处理层  
- **`PdfParsingService`**: PDF文档解析，支持OCR识别
- **`JsonToMarkdownService`**: JSON数据转Markdown格式
- **`TxtToMarkdownService`**: 文本文件处理和格式化
- **`MarkdownController`**: 文档处理REST接口

### 安全与工具
- **`SafeInputGuardrail`**: 输入内容安全检测
- **`ChatModelListener`**: 对话过程监听和日志记录
- **`ModelSelectionStrategy`**: 智能模型选择策略

## 🌟 核心功能详述

### 1. 英语学习助手
基于专业的B2级学习标准，提供全方位的英语学习支持：

#### 🎯 学习模块
- **语法学习**: 时态、从句、语态等核心语法点详解
- **口语练习**: 发音技巧、流利度训练、情景对话
- **听力训练**: 多口音识别、新闻理解、考试策略
- **阅读理解**: 速读技巧、长难句分析、词汇扩展
- **写作技巧**: 结构设计、议论文写作、商务应用
- **词汇扩展**: 记忆方法、词根词缀、专业词汇
- **考试备考**: 托福雅思、四六级、商务英语等

#### 💬 双模式交互
- **普通聊天模式**: 自由对话练习，实时语法建议
- **知识库问答模式**: 基于专业学习资料的精准回答

### 2. RAG 增强问答系统
基于 LangChain4j 构建的检索增强生成系统：

#### 🧠 核心特性
- **知识库问答**: 基于本地知识库进行智能问答
- **语义检索**: 高精度的向量相似度搜索
- **流式响应**: 支持实时流式RAG问答
- **多源融合**: 整合多个数据源的信息

#### 📡 API接口
- `POST /api/ai/rag/chat` - RAG增强问答（同步）
- `GET /api/ai/rag/chat-stream` - RAG增强问答（流式）
- `POST /api/ai/rag/search` - 知识库语义搜索
- `GET /api/ai/rag/stats` - 知识库统计信息

#### 📚 知识库内容
- **编程学习**: Java学习路线、最佳实践指导
- **面试准备**: 程序员常见面试题库和解析
- **求职指导**: 简历优化、职业规划建议
- **英语学习**: 剑桥B2级学习材料和练习

### 3. 文档智能处理
强大的文档解析和格式转换能力：

#### 📄 处理能力
- **PDF解析**: 支持文本PDF和扫描版PDF (OCR)
- **格式转换**: JSON转Markdown、TXT格式化
- **批量处理**: 目录级别的批量文档处理
- **数据清洗**: 自动格式化和结构化处理

#### 🔧 技术特性
- **多引擎支持**: Tika + Tesseract OCR双引擎
- **智能识别**: 自动检测文档类型和最佳处理方式
- **并行处理**: 多线程并行处理提升效率
- **错误恢复**: 完善的错误处理和日志记录

详细使用指南：
- [RAG API 使用指南](./doc/RAG_API_GUIDE.md)
- [数据库配置指南](./doc/SINGLE_DATABASE_SETUP_GUIDE.md)
- [向量化日志分析](./doc/EMBEDDING_LOG_GUIDE.md)



## 📂 项目结构

```
ai-code-helper-master/
├── src/main/java/com/hujun/
│   ├── aicodehelper/              # AI助手核心模块
│   │   ├── ai/                    # AI服务层
│   │   │   ├── SmartAiCodeHelperService.java
│   │   │   ├── RagService.java
│   │   │   ├── rag/RagConfig.java
│   │   │   └── guardrail/SafeInputGuardrail.java
│   │   ├── controller/            # REST控制器
│   │   └── AiCodeHelperApplication.java
│   └── pdfparser/                 # 文档处理模块
│       ├── markdown/
│       │   ├── service/           # 文档处理服务
│       │   └── controller/MarkdownController.java
│       └── MarkdownApplication.java
├── ai-code-helper-frontend/       # Vue.js前端
│   ├── src/
│   │   ├── components/            # Vue组件
│   │   ├── api/                   # API接口
│   │   └── config/englishTopics.js
│   └── index.html
├── data/                          # 英语学习数据
│   ├── cambridge/                 # 剑桥教材数据
│   └── myelt/                     # MyELT平台数据
├── doc/                           # 技术文档
│   ├── RAG_API_GUIDE.md
│   ├── SINGLE_DATABASE_SETUP_GUIDE.md
│   └── EMBEDDING_LOG_GUIDE.md
└── src/main/resources/
    ├── docs/                      # 知识库文档
    ├── application-postgresql.yml # 数据库配置
    └── system-prompt.txt          # AI系统提示词
```

## 🎯 使用场景

### 学习者
- **英语学习**: 系统性提升英语各项技能
- **编程学习**: 获得个性化的学习路线指导
- **面试准备**: 针对性的面试题练习和指导

### 开发者
- **AI应用开发**: 学习LangChain4j框架实践
- **RAG系统构建**: 了解检索增强生成技术
- **多模态处理**: 掌握文档处理和向量化技术

### 研究者
- **技术方案对比**: 观察不同AI模型的表现
- **向量化分析**: 研究文本嵌入和检索效果
- **系统架构**: 参考单一数据库和微服务设计

## 📋 更新日志

### v2.1.0 (当前版本)
- ✅ 数据库架构优化：从双数据库（MySQL+PostgreSQL）迁移到单一PostgreSQL数据库
- ✅ 简化数据源管理，统一聊天记忆和向量数据存储
- ✅ 移除了MySQL依赖，降低了系统复杂性

### v2.0.0 (历史版本)
- ✅ 新增英语学习助手功能，支持B2级别专业指导
- ✅ 完善文档处理系统，支持PDF OCR和批量处理
- ✅ 优化RAG系统，提升检索准确性和响应速度
- ✅ 引入Google Gemini模型，提升对话质量
- ✅ 双数据库架构，分离向量存储和业务数据
- ✅ 完善前端界面，支持双模式交互

### v1.0.0 (历史版本)
- ✅ 基础AI对话功能
- ✅ 简单的RAG实现
- ✅ 通义千问模型集成

## 🤝 致谢

- [LangChain4j](https://github.com/langchain4j/langchain4j) - 强大的AI应用开发框架
- [Google AI](https://ai.google.dev/) - 优秀的Gemini系列模型
- [Spring Boot](https://spring.io/projects/spring-boot) - 企业级Java开发框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript前端框架
- [PostgreSQL](https://www.postgresql.org/) + [PGVector](https://github.com/pgvector/pgvector) - 高性能向量数据库
- [Apache Tika](https://tika.apache.org/) - 文档内容提取工具
- [Tesseract OCR](https://github.com/tesseract-ocr/tesseract) - 开源OCR引擎

## 📄 许可证

本项目遵循 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

---

⭐ 如果这个项目对你有帮助，欢迎给个 Star！