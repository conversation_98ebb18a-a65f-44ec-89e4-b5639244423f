#!/usr/bin/env python3
"""
ThinkyAI API 测试脚本
测试结构化输出功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"
THINKY_API_BASE = f"{BASE_URL}/ai/thinky"

def test_thinky_chat():
    """测试ThinkyAI聊天功能"""
    print("🧠 测试ThinkyAI聊天功能...")
    
    url = f"{THINKY_API_BASE}/chat"
    payload = {
        "sessionId": "test-session-001",
        "message": "我在解一道二次方程：x^2 + 5x + 6 = 0，我的答案是 x = -2 和 x = -3，请看看对不对？",
        "subject": "数学",
        "problem": "二次方程求解"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 聊天响应成功")
            print(f"📝 响应类型: {result.get('type')}")
            print(f"💬 流式内容: {result.get('streamContent', '')[:100]}...")
            
            # 检查是否有结构化输出
            if result.get('structuredData'):
                print(f"🎯 检测到结构化输出: {result.get('structuredType')}")
                structured_data = result.get('structuredData')
                if isinstance(structured_data, dict):
                    print(f"📊 结构化数据预览: {json.dumps(structured_data, ensure_ascii=False, indent=2)[:500]}...")
            
            # 显示学习状态
            if result.get('learningStatus'):
                status = result.get('learningStatus')
                print(f"📈 学习状态:")
                print(f"  - 轮次: {status.get('roundCount')}")
                print(f"  - 完成题目: {status.get('completedProblems')}")
                print(f"  - 当前主题: {status.get('currentTopic')}")
                
            return True
        else:
            print(f"❌ 聊天请求失败: {response.status_code} - {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_thinky_stream():
    """测试ThinkyAI流式聊天功能"""
    print("\n🌊 测试ThinkyAI流式聊天功能...")
    
    url = f"{THINKY_API_BASE}/chat/stream"
    params = {
        "sessionId": "test-session-002",
        "message": "我不太理解为什么二次方程会有两个解，能不能帮我理解一下？",
        "subject": "数学",
        "problem": "二次方程概念理解"
    }
    
    try:
        response = requests.get(url, params=params, stream=True, timeout=30)
        
        if response.status_code == 200:
            print("✅ 流式响应开始...")
            
            chunk_count = 0
            structured_received = False
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data:'):
                        try:
                            data_str = line_str[5:].strip()
                            if data_str and data_str != '[DONE]':
                                data = json.loads(data_str)
                                
                                if data.get('type') == 'STREAM_ONLY':
                                    chunk_count += 1
                                    content = data.get('streamContent', '')
                                    if content:
                                        print(f"📝 流式块 {chunk_count}: {content[:50]}...")
                                        
                                elif data.get('type') == 'STRUCTURED_ONLY':
                                    structured_received = True
                                    print(f"🎯 接收到结构化输出: {data.get('structuredType')}")
                                    
                        except json.JSONDecodeError:
                            continue
                    elif line_str.startswith('event: structured'):
                        print("🔔 检测到结构化事件")
                        
            print(f"✅ 流式响应完成 - 共接收 {chunk_count} 个流式块")
            if structured_received:
                print("🎯 成功接收结构化输出")
            
            return True
        else:
            print(f"❌ 流式请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 流式请求异常: {e}")
        return False

def check_server_health():
    """检查服务器是否正常运行"""
    print("🏥 检查服务器健康状态...")
    
    try:
        # 检查基础健康端点
        response = requests.get(f"{BASE_URL}/actuator/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器健康检查失败: {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        try:
            # 备用检查 - 直接访问根路径
            response = requests.get(BASE_URL, timeout=10)
            if response.status_code in [200, 404, 405]:  # 任何非连接错误都表示服务器在运行
                print("✅ 服务器运行正常 (备用检查)")
                return True
            else:
                print(f"❌ 服务器无响应")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 服务器连接失败: {e}")
            return False

def main():
    """主测试函数"""
    print("🚀 开始ThinkyAI API测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(5)
    
    # 检查服务器健康状态
    if not check_server_health():
        print("\n❌ 服务器未运行，请先启动应用程序")
        print("💡 运行命令: mvn spring-boot:run -Dspring-boot.run.profiles=postgresql,gemini")
        return
    
    print("\n" + "=" * 50)
    
    # 测试ThinkyAI功能
    success_count = 0
    total_tests = 2
    
    if test_thinky_chat():
        success_count += 1
    
    if test_thinky_stream():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！ThinkyAI结构化输出功能正常工作")
    else:
        print("⚠️  部分测试失败，请检查日志")

if __name__ == "__main__":
    main()
