# GEMINI.md

## Project Overview

This is a Java-based AI assistant platform built with Spring Boot and LangChain4j. The application serves as a multi-functional AI helper with a focus on educational content, including English language tutoring and math problem-solving. It integrates with Google Gemini as the primary large language model and uses a PostgreSQL database with the pgvector extension for storing chat history and vector embeddings for RAG (Retrieval-Augmented Generation).

The project is structured as a modular monolith with a REST API that is consumed by a separate Vue.js frontend. It includes features for:

*   Conversational AI with chat memory
*   RAG-enhanced Q&A from a knowledge base
*   Multimodal chat with image and text input
*   Document processing (PDF, OCR)
*   Text-to-speech generation
*   Mathematical tool usage for solving equations, factorization, etc.

## Building and Running

### Prerequisites

*   Java (JDK 21+)
*   Maven 3.6+
*   PostgreSQL 15+ with the `pgvector` extension installed
*   Google Gemini API Key

### Backend

1.  **Configure the application:**
    *   Edit `src/main/resources/application-postgresql.yml` to set your Google Gemini API key and PostgreSQL database credentials.

2.  **Run the application:**
    *   To run the main AI assistant server:
        ```bash
        mvn spring-boot:run
        ```
    *   To run the document processing service:
        ```bash
        mvn spring-boot:run -Dspring-boot.run.main-class=com.hujun.pdfparser.MarkdownApplication
        ```

### Frontend (ai-code-helper-frontend)

*Note: The frontend code is not present in this directory, but the `README.md` indicates its existence.*

1.  **Navigate to the frontend directory:**
    ```bash
    cd ../ai-code-helper-frontend
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Run the development server:**
    ```bash
    npm run dev
    ```

## Development Conventions

*   **Code Style:** The project follows standard Java conventions. Lombok is used to reduce boilerplate code.
*   **Testing:** The project has a `src/test` directory, but no tests were found in the initial analysis. The `pom.xml` includes the `spring-boot-starter-test` dependency, so the project is set up for testing with JUnit and Spring Test.
*   **Documentation:** The `doc` directory contains extensive documentation on the project's architecture, APIs, and setup. This is a good place to look for more information.
*   **Configuration:** The application is configured using YAML files in `src/main/resources`. The main configuration file is `application.yml`, which is supplemented by profile-specific files like `application-postgresql.yml`.
*   **System Prompt:** The `system-prompt.txt` file defines the persona and behavior of the AI assistant.
