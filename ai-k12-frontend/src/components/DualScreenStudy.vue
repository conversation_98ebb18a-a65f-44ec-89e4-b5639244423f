<template>
  <div class="dual-screen-container">
    <!-- 左侧工具调用结果面板 -->
    <div class="tool-results-panel">
      <div class="panel-header">
        <h2>🎓 引导式学习助手</h2>
        <p>ThinkyAI 结构化解题步骤和学习诊断将在这里显示</p>
      </div>
      
      <!-- 可滚动内容区域 -->
      <div class="panel-content">
        <!-- ThinkyAI 结构化解题步骤区域 -->
        <div v-if="structuredSolution" class="structured-solution-section">
        <!-- 问题识别 -->
        <div v-if="structuredSolution.problem" class="problem-identification">
          <div class="problem-header">
            <div class="problem-title">
              <span class="problem-icon">📝</span>
              <span>{{ structuredSolution.problem?.subject || '数学' }}解题步骤</span>
            </div>
            <div class="problem-meta">
              <span class="difficulty-badge" :class="`difficulty-${(structuredSolution.problem?.difficulty || 'medium').toLowerCase()}`">
                {{ getDifficultyText(structuredSolution.problem?.difficulty) }}
              </span>
              <button class="close-btn" @click="closeStructuredSolution" title="关闭解题步骤">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          </div>
          <div class="problem-content">
            <div class="problem-details">
              <div v-if="structuredSolution.problem.topic" class="problem-type">
                <span class="label">题目类型：</span>{{ structuredSolution.problem.topic }}
              </div>
              <div v-if="structuredSolution.problem.problemText" class="problem-text">
                <span class="label">题目内容：</span>{{ structuredSolution.problem.problemText }}
              </div>
            </div>
          </div>
        </div>

        <!-- 解题步骤 -->
        <div v-if="structuredSolution.steps && structuredSolution.steps.length > 0" class="solution-steps">
          <div class="steps-header">
            <span class="steps-icon">🔍</span>
            <span class="steps-title">解题步骤</span>
          </div>
          <div class="steps-list">
            <div v-for="(step, index) in structuredSolution.steps" :key="index" class="step-item">
              <div class="step-header">
                <span class="step-number">{{ index + 1 }}</span>
                <span class="step-title">{{ step.title || `步骤 ${index + 1}` }}</span>
              </div>
              <div class="step-description" v-html="formatMessage(getStepDescription(step))"></div>
              <div v-if="step.reasoning || step.studentThinking" class="step-reasoning">
                <span class="step-label">💡 思路：</span>{{ step.reasoning || step.studentThinking }}
              </div>
              <div v-if="step.formula || step.keyFormula" class="step-formula">
                <span class="step-label">📐 公式：</span><code>{{ step.formula || step.keyFormula }}</code>
              </div>
              <div v-if="step.guidingQuestion" class="step-guide">
                <span class="step-label">🤔 引导问题：</span>{{ step.guidingQuestion }}
              </div>
            </div>
          </div>
        </div>

        <!-- 知识点分析 -->
        <div v-if="structuredSolution.knowledgePoints" class="knowledge-points">
          <div class="knowledge-header">
            <span class="knowledge-icon">📚</span>
            <span class="knowledge-title">相关知识点</span>
          </div>
          <div class="knowledge-content">
            <div v-if="structuredSolution.knowledgePoints.primaryConcepts" class="knowledge-group">
              <span class="group-label">核心概念：</span>
              <div class="knowledge-tags">
                <span v-for="(concept, index) in structuredSolution.knowledgePoints.primaryConcepts" :key="index" class="knowledge-tag primary">
                  {{ concept }}
                </span>
              </div>
            </div>
            <div v-if="structuredSolution.knowledgePoints.prerequisites" class="knowledge-group">
              <span class="group-label">前置知识：</span>
              <div class="knowledge-tags">
                <span v-for="(prereq, index) in structuredSolution.knowledgePoints.prerequisites" :key="index" class="knowledge-tag secondary">
                  {{ prereq }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 常见错误 -->
        <div v-if="structuredSolution.mistakes && structuredSolution.mistakes.length > 0" class="common-mistakes">
          <div class="mistakes-header">
            <span class="mistakes-icon">⚠️</span>
            <span class="mistakes-title">常见错误</span>
          </div>
          <div class="mistakes-list">
            <div v-for="(mistake, index) in structuredSolution.mistakes" :key="index" class="mistake-item">
              <div class="mistake-description">{{ mistake.description }}</div>
              <div v-if="mistake.correction" class="mistake-correction">
                <span class="correction-label">正确做法：</span>{{ mistake.correction }}
              </div>
            </div>
          </div>
        </div>

        <!-- 学习建议 -->
        <div v-if="structuredSolution.recommendations && structuredSolution.recommendations.length > 0" class="recommendations">
          <div class="recommendations-header">
            <span class="recommendations-icon">💡</span>
            <span class="recommendations-title">学习建议</span>
          </div>
          <div class="recommendations-list">
            <div v-for="(rec, index) in structuredSolution.recommendations" :key="index" class="recommendation-item">
              <span class="rec-icon">🎯</span>
              <span class="rec-text">{{ rec }}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="empty-placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">🤖</div>
          <h3>等待结构化输出</h3>
          <p>当您提交数学问题时，ThinkyAI 将在这里显示：</p>
          <ul>
            <li>📝 引导式解题步骤</li>
            <li>📚 相关知识点分析</li>
            <li>⚠️ 常见错误提醒</li>
            <li>💡 个性化学习建议</li>
          </ul>
          <div class="placeholder-tip">
            💡 试试输入："解方程 x² - 5x + 6 = 0"
          </div>
        </div>
      </div>
      </div>

      <!-- 交互式模拟区域 -->
      <div v-if="currentIframe" class="iframe-section">
        <div class="iframe-header">
          <div class="iframe-title">
            <span class="iframe-icon">🎯</span>
            <span>{{ currentIframe.title || '交互式模拟' }}</span>
          </div>
          <button class="iframe-close-btn" @click="closeIframe" title="关闭模拟">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="iframe-container">
          <iframe 
            :src="currentIframe.src" 
            :title="currentIframe.title"
            class="simulation-iframe"
            allowfullscreen
            @load="onIframeLoad"
          ></iframe>
          <div v-if="iframeLoading" class="iframe-loading">
            <div class="loading-spinner"></div>
            <p>正在加载交互式模拟...</p>
          </div>
        </div>
        <div class="iframe-description" v-if="currentIframe.description">
          <p>{{ currentIframe.description }}</p>
        </div>
      </div>
      
      <!-- 工具调用结果内容 -->
      <div class="tool-results-content" :class="{ 'has-iframe': currentIframe, 'has-structured': structuredSolution }">
        <div v-if="toolResults.length === 0 && !currentIframe && !structuredSolution" class="empty-state">
          <div class="empty-icon">🔧</div>
          <h3>暂无计算结果</h3>
          <p>当你提出数学问题时，AI工具的计算结果将在这里显示</p>
        </div>
        
        <div v-for="result in toolResults" :key="result.id" class="tool-result-item">
          <div class="tool-header">
            <span class="tool-name">{{ result.toolName || '数学计算' }}</span>
            <span class="tool-time">{{ formatTime(result.timestamp) }}</span>
          </div>
          
          <div v-if="result.toolInput" class="tool-input">
            <strong>输入：</strong>{{ result.toolInput }}
          </div>
          
          <div class="tool-output" v-html="formatMessage(result.content || '')"></div>
        </div>
      </div>
    </div>

    <!-- 右侧聊天窗口 -->
    <div class="chat-panel">
      <div class="chat-header">
          <div class="header-left">
          <h2>💬 {{ aiChatTitle }}</h2>
            <!-- 学习状态面板 -->
            <div v-if="learningStatus" class="learning-status-panel">
              <div class="status-header">
                <span class="status-title">📊 学习进度</span>
                <div class="phase-badge" :class="`phase-${getPhaseClass(learningStatus.learningPhase)}`">
                  {{ learningStatus.learningPhase || '开始学习' }}
                </div>
              </div>
              
              <div class="status-grid">
                <!-- 对话轮次 -->
                <div class="status-card rounds">
                  <div class="card-icon">💬</div>
                  <div class="card-content">
                    <div class="card-value">{{ learningStatus.roundCount || 0 }}</div>
                    <div class="card-label">轮对话</div>
                  </div>
                </div>
                
                <!-- 当前主题 -->
                <div class="status-card topic" v-if="learningStatus.currentTopic">
                  <div class="card-icon">🎯</div>
                  <div class="card-content">
                    <div class="card-value">{{ learningStatus.currentTopic }}</div>
                    <div class="card-label">学习主题</div>
                  </div>
                </div>
                
                <!-- 完成题目 -->
                <div class="status-card problems" v-if="learningStatus.completedProblems > 0">
                  <div class="card-icon">✅</div>
                  <div class="card-content">
                    <div class="card-value">{{ learningStatus.completedProblems }}</div>
                    <div class="card-label">已完成</div>
                  </div>
                </div>
                
                <!-- 学习时长 -->
                <div class="status-card time" v-if="learningStatus.studyTimeMinutes > 0">
                  <div class="card-icon">⏱️</div>
                  <div class="card-content">
                    <div class="card-value">{{ learningStatus.studyTimeMinutes }}</div>
                    <div class="card-label">分钟</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="header-actions">
            <!-- 语言选择器 -->
            <div class="language-selector" title="选择语言 / Language">
              <label class="language-label">Language:</label>
              <select v-model="language" class="global-lang-select">
                <option value="en">🇺🇸 English</option>
                <option value="zh">🇨🇳 中文</option>
              </select>
            </div>
            <!-- 语音模式开关 -->
            <div class="tts-toggle-container" title="语音播报模式">
              <label class="tts-toggle-switch">
                <input type="checkbox" v-model="ttsEnabled" @change="onTTSToggle">
                <span class="tts-toggle-slider">
                  <svg class="tts-icon" width="14" height="14" viewBox="0 0 16 16">
                    <path d="M3 6H5L8 3V13L5 10H3C2.45 10 2 9.55 2 9V7C2 6.45 2.45 6 3 6ZM11.5 3.5C11.78 3.22 12.25 3.22 12.53 3.5C14.1 5.07 14.1 7.68 12.53 9.25C12.25 9.53 11.78 9.53 11.5 9.25C11.22 8.97 11.22 8.5 11.5 8.22C12.64 7.08 12.64 5.67 11.5 4.53C11.22 4.25 11.22 3.78 11.5 3.5ZM9.88 5.12C10.17 4.83 10.64 4.83 10.92 5.12C11.7 5.9 11.7 7.1 10.92 7.88C10.64 8.17 10.17 8.17 9.88 7.88C9.6 7.6 9.6 7.13 9.88 6.84C10.06 6.66 10.16 6.43 10.16 6.18C10.16 5.93 10.06 5.7 9.88 5.52C9.6 5.24 9.6 4.77 9.88 4.48V5.12Z" fill="currentColor"/>
                  </svg>
                </span>
              </label>
              <span class="tts-toggle-label">{{ ttsEnabled ? '语音开' : '语音关' }}</span>
            </div>
            
            
            <button class="home-btn" @click="goHome" title="回到首页">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20A2 2 0 0 0 7 22H17A2 2 0 0 0 19 20V10M9 12V12C9 13.1046 9.89543 14 11 14V14C12.1046 14 13 13.1046 13 12V12C13 10.8954 12.1046 10 11 10V10C9.89543 10 9 10.8954 9 12Z"/>
              </svg>
              首页
            </button>
            <div class="status-indicator">
              <span class="status-dot"></span>
              <span>在线</span>
            </div>
          </div>
        </div>
      
      <!-- 消息列表 -->
      <div class="chat-messages" ref="messagesList">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="message assistant">
          <div class="message-avatar">🤖</div>
          <div class="message-content">
            <div class="message-bubble">
              {{ welcomeMessage }}
            </div>
            <div class="message-time">刚刚</div>
          </div>
        </div>
        
        <!-- 聊天消息 -->
        <div v-for="message in messages" :key="message.id" class="message" :class="{ 'user': message.isUser, 'assistant': !message.isUser }">
          <div class="message-avatar" v-if="!message.isUser">🤖</div>
          <div class="message-avatar" v-else>用户</div>
          <div class="message-content">
            <div class="message-bubble">
              <!-- 图片消息 -->
              <img v-if="message.image" :src="message.image" alt="Message image" class="message-image" />
              
              <!-- 文本消息 -->
              <div v-if="message.text" class="message-text" v-html="formatMessage(message.text || '')"></div>
              
              
              <!-- 流式响应指示器 -->
              <div v-if="message.isStreaming" class="streaming-indicator">
                <div class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
            
            <div class="message-meta">
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
              <button v-if="!message.isUser && message.text" class="tts-inline-btn" @click="playTTSForMessage(message)" title="播放语音播报">
                <svg width="14" height="14" viewBox="0 0 16 16">
                  <path d="M3 6H5L8 3V13L5 10H3C2.45 10 2 9.55 2 9V7C2 6.45 2.45 6 3 6ZM11.5 3.5C11.78 3.22 12.25 3.22 12.53 3.5C14.1 5.07 14.1 7.68 12.53 9.25C12.25 9.53 11.78 9.53 11.5 9.25C11.22 8.97 11.22 8.5 11.5 8.22C12.64 7.08 12.64 5.67 11.5 4.53C11.22 4.25 11.22 3.78 11.5 3.5ZM9.88 5.12C10.17 4.83 10.64 4.83 10.92 5.12C11.7 5.9 11.7 7.1 10.92 7.88C10.64 8.17 10.17 8.17 9.88 7.88C9.6 7.6 9.6 7.13 9.88 6.84C10.06 6.66 10.16 6.43 10.16 6.18C10.16 5.93 10.06 5.7 9.88 5.52C9.6 5.24 9.6 4.77 9.88 4.48V5.12Z" fill="currentColor"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天输入区域 -->
      <div class="chat-input">
        <div class="recording-indicator" :class="{ active: isRecording }">
          <span class="recording-dot"></span>
          <span>正在录音...</span>
        </div>
        
        <div class="input-container">
          <div class="input-wrapper">
            <!-- 图片预览 -->
            <div v-if="selectedImage" class="image-preview">
              <img :src="selectedImage" alt="Selected image" />
              <button class="remove-image" @click="removeImage">×</button>
            </div>
            
            <!-- 文本输入框 -->
            <textarea
              class="message-input"
              v-model="inputMessage"
              @keydown="handleKeyDown"
              @input="adjustHeight"
              ref="messageInput"
              :placeholder="inputPlaceholder"
              rows="1"
            ></textarea>
            
            <div class="input-actions">
              <!-- 语音输入按钮 -->
              <button class="input-btn" @click="toggleVoiceRecording" :class="{ active: isRecording }" title="语音输入">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                  <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                </svg>
              </button>
              
              <!-- 图片上传按钮 -->
              <button class="input-btn" @click="selectImage" title="上传图片">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                </svg>
              </button>
              
              <!-- 相机按钮 -->
              <button class="input-btn" @click="openMobileUpload" title="拍照上传">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>
            </div>
          </div>
          
          <button class="send-btn" @click="sendMessage" :disabled="!canSend || isLoading">{{ sendButtonText }}</button>
        </div>
        
        <!-- 隐藏的文件输入 -->
        <input type="file" ref="fileInput" @change="handleFileSelect" accept="image/*" style="display: none">
      </div>
    </div>

    <!-- 手机上传二维码弹窗 -->
    <DesktopQrModal
      :visible="qrModalVisible"
      :memory-id="memoryId"
      @close="qrModalVisible = false"
      @uploaded="handleMobileUploaded"
    />

    <!-- 隐藏的音频播放器（用于TTS自动播放） -->
    <audio ref="ttsAudio" preload="auto"></audio>

    <!-- 语音输入面板 -->
    <div v-if="showVoicePanel" class="voice-overlay">
      <div class="voice-panel">
        <div class="voice-header">
          <span class="voice-title">🎤 语音输入</span>
          <button class="voice-close" @click="stopVoiceInput" title="关闭语音输入">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div class="voice-main">
          <!-- 音频波形动画区域 -->
          <div class="voice-visual">
            <div class="wave-container" :class="{ active: isRecording }">
              <div class="wave-bar" v-for="i in 5" :key="i" :style="{
                animationDelay: (i * 0.1) + 's',
                height: isRecording ? (20 + Math.random() * 40 * (meter * 5 + 0.2)) + 'px' : '4px'
              }"></div>
            </div>

            <!-- 状态图标 -->
            <div class="status-icon">
              <div v-if="isRecording" class="recording-pulse">
                <div class="pulse-ring"></div>
                <div class="pulse-core"></div>
              </div>
              <div v-else-if="isRecognizing" class="processing-spinner">
                <svg class="spinner-svg" viewBox="0 0 50 50">
                  <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#007bff" stroke-width="2"></circle>
                </svg>
              </div>
              <div v-else class="standby-icon">⏸️</div>
            </div>
          </div>

          <!-- 状态文字 -->
          <div class="voice-status-section">
            <div class="voice-status-text">
              <template v-if="isRecording">正在聆听您的语音...</template>
              <template v-else-if="isRecognizing">正在识别语音内容...</template>
              <template v-else>{{ voiceStatus }}</template>
            </div>

            <div v-if="isRecording" class="voice-hint">
              清晰说话，完成后点击停止按钮
            </div>
          </div>

          <!-- 音量可视化 -->
          <div class="volume-section" v-if="isRecording">
            <div class="volume-label">音量</div>
            <div class="volume-meter">
              <div class="volume-bar" :style="{
                width: Math.max(0, Math.min(100, meter * 200)) + '%',
                background: meter > 0.3 ? '#4caf50' : meter > 0.1 ? '#ff9800' : '#f44336'
              }"></div>
            </div>
          </div>
        </div>

        <!-- 控制区域 -->
        <div class="voice-footer">
          <div class="voice-controls">
            <select class="lang-select" v-model="language" title="语音识别语言">
              <option value="zh">🇨🇳 中文</option>
              <option value="en">🇺🇸 English</option>
            </select>

            <button
              class="stop-voice-btn"
              @click="stopVoiceInput"
              :disabled="isRecognizing || (!isRecording && wsState !== 'connected')"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <rect x="6" y="6" width="12" height="12" rx="2" fill="currentColor"/>
              </svg>
              停止
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 右下角女老师 -->
    <div class="teacher-corner">
      <div class="teacher-image-container">
        <img src="../assets/images/teacher-avatar-4c860f.png" alt="数学老师" class="teacher-corner-image" @click="onTeacherClick" />
        <div class="teacher-speech-bubble" v-if="showTeacherTip">
          <div class="speech-content">
            {{ teacherTipText }}
          </div>
          <div class="speech-arrow"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { marked } from 'marked'
import { k12RagChatStream } from '../api/ragApi.js'
import { multimodalChatStream, aiChatStream, openAiTTS, openAiTTSStream, testOpenAiTTS, getOpenAiTTSStatus } from '../api/chatApi.js'
import { speakText, stopSpeaking } from '../api/speechApi.js'
import DesktopQrModal from './mobile/DesktopQrModal.vue'
import { thinkyAiChatStream } from '../api/thinkyApi.js'

export default {
  name: 'DualScreenStudy',
  components: { DesktopQrModal },
  data() {
    return {
      // ThinkyAI 结构化输出状态
      structuredSolution: null,        // 解题步骤数据
      learningStatus: null,           // 学习状态
      sessionId: null,                // ThinkyAI 会话ID
      currentSubject: '数学',         // 当前学科
      currentProblem: null,           // 当前问题类型
      
      // 工具调用结果状态
      toolResults: [],
      
      // iframe 相关状态
      currentIframe: null,
      iframeLoading: false,
      
      // 聊天相关状态
      inputMessage: '',
      selectedImage: null,
      selectedImageFile: null,
      messages: [], // 只存储普通AI回复消息
      memoryId: Date.now().toString(),
      currentEventSource: null,
      isLoading: false,
      qrModalVisible: false,
      ttsEnabled: false,
      ttsObjectUrl: null,
      ttsPlaying: false,
      ttsAbortController: null,
      ttsStream: null,
      ttsMse: null,

      // 语音识别相关
      isRecording: false,
      isRecognizing: false,
      showVoicePanel: false,
      meter: 0,
      ws: null,
      wsState: 'disconnected',
      audioContext: null,
      workletNode: null,
      sourceNode: null,
      sampleRate: 0,
      language: 'en',
      floatQueue: [],
      accFloat: null,
      frameSize: 0,
      voiceStatus: '待机',
      
      // 右下角女老师相关
      showTeacherTip: false,
      teacherTipText: '有问题随时问我哦！',
      teacherTipTimer: null
    }
  },
  computed: {
    welcomeMessage() {
      return this.language === 'en'
        ? "Hi! I'm your smart math tutor. I can help you solve all kinds of math problems. Let's solve this together!"
        : '你好！我是智能数学助手，我可以帮你解决各种数学问题。现在让我们一起来解决这道题吧！'
    },
    inputPlaceholder() {
      return this.language === 'en' ? 'Type your math question...' : '输入数学问题...'
    },
    sendButtonText() {
      return this.language === 'en' ? 'Send' : '发送'
    },
    aiChatTitle() {
      return this.language === 'en' ? 'AI Responses' : 'AI 对话回复'
    },
    canSend() {
      return (this.inputMessage.trim() || this.selectedImage) && !this.isLoading
    }
  },
  methods: {
    // ThinkyAI 相关方法
    
    // 关闭结构化解题步骤
    closeStructuredSolution() {
      this.structuredSolution = null
    },

    // 获取难度显示文本
    getDifficultyText(difficulty) {
      const difficultyMap = {
        easy: '简单',
        medium: '中等', 
        hard: '困难',
        expert: '专家'
      }
      return difficultyMap[difficulty] || '中等'
    },

    // 获取步骤描述（兼容不同数据格式）
    getStepDescription(step) {
      if (!step) return ''
      
      // 优先使用 description 字段
      if (step.description) {
        return step.description
      }
      
      // 备用方案：使用 explanation 字段
      if (step.explanation) {
        return step.explanation
      }
      
      // 备用方案：使用 guidingQuestion 字段
      if (step.guidingQuestion) {
        return `引导问题：${step.guidingQuestion}`
      }
      
      // 最后备用：使用 studentThinking 字段
      if (step.studentThinking) {
        return `思考要点：${step.studentThinking}`
      }
      
      return '暂无描述'
    },

    // 获取学习阶段对应的CSS类名
    getPhaseClass(phase) {
      const phaseMap = {
        '探索阶段': 'explore',
        '练习阶段': 'practice', 
        '提升阶段': 'improve',
        '精进阶段': 'master'
      }
      return phaseMap[phase] || 'default'
    },

    // 处理ThinkyAI结构化响应
    processThinkyStructuredResponse(data) {
      try {
        console.log('🎓 处理ThinkyAI结构化响应:', data)
        
        if (data.structuredData) {
          const structuredType = data.structuredType
          const structuredData = data.structuredData
          
          // 更新学习状态
          if (data.learningStatus) {
            this.learningStatus = data.learningStatus
          }
          
          // 处理不同类型的结构化输出
          if (structuredType === 'SOLUTION' && structuredData) {
            // 显示解题步骤
            this.structuredSolution = structuredData
            console.log('📝 显示解题步骤:', this.structuredSolution)
            
            // 滚动到结构化输出区域
            this.$nextTick(() => {
              const solutionElement = document.querySelector('.structured-solution-section')
              if (solutionElement) {
                solutionElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
              }
            })
          } else if (structuredType === 'DIAGNOSIS' && structuredData) {
            // 显示学习诊断
            console.log('🩺 收到学习诊断:', structuredData)
            this.showLearningDiagnosis(structuredData)
          } else if (structuredType === 'PROGRESS' && structuredData) {
            // 显示进度报告
            console.log('📊 收到进度报告:', structuredData)
            this.showProgressReport(structuredData)
          }
        }
        
      } catch (error) {
        console.error('[ThinkyAI] 处理结构化响应失败:', error)
      }
    },

    // 显示学习诊断（可以用弹窗或其他方式）
    showLearningDiagnosis(diagnosis) {
      // 暂时在控制台显示，后续可以实现弹窗
      console.log('🩺 学习诊断报告:', diagnosis)
      this.showTeacherTipMessage('收到学习诊断报告，请查看右下角提示！', 5000)
    },

    // 显示进度报告
    showProgressReport(progress) {
      // 暂时在控制台显示，后续可以实现弹窗
      console.log('📊 学习进度报告:', progress) 
      this.showTeacherTipMessage('恭喜！达到学习里程碑！', 4000)
    },

    // 生成或获取会话ID
    getOrCreateSessionId() {
      if (!this.sessionId) {
        this.sessionId = `thinky-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        console.log('🎓 创建新的ThinkyAI会话:', this.sessionId)
      }
      return this.sessionId
    },


    // 根据浏览器能力自动协商流式音频容器/编码
    getPreferredStreamingFormat() {
      try {
        if (!("MediaSource" in window) || !window.MediaSource.isTypeSupported) return null
        const webmOpus = 'audio/webm; codecs="opus"'
        const mp4Aac = 'audio/mp4; codecs="mp4a.40.2"'
        if (window.MediaSource.isTypeSupported(webmOpus)) {
          // 请求服务端生成 webm 容器（含 opus 编码）
          return { mime: webmOpus, responseFormat: 'webm', container: 'webm' }
        }
        if (window.MediaSource.isTypeSupported(mp4Aac)) {
          // 请求服务端生成 mp4 容器（含 aac 编码）
          return { mime: mp4Aac, responseFormat: 'mp4', container: 'mp4' }
        }
        return null
      } catch (_) {
        return null
      }
    },
    // 处理结构化响应块
    processStructuredChunk(chunk, aiMessage) {
      try {
        console.log('🔍 处理响应块:', chunk)
        
        // 检查是否是ThinkyAI特殊响应
        if (typeof chunk === 'string' && chunk.includes('"type":"')) {
          const data = JSON.parse(chunk)
          console.log('🎓 ThinkyAI特殊数据:', data)
          
          if (data.type === 'structured') {
            this.processThinkyStructuredResponse(data)
            return // 结构化数据不需要显示在聊天中
          } else if (data.type === 'learning_status') {
            // 更新学习状态
            this.learningStatus = data.learningStatus
            console.log('📊 更新学习状态:', this.learningStatus)
            this.$forceUpdate()
            return // 学习状态不需要显示在聊天中
          }
        }
        
        // 尝试解析为JSON格式的其他响应
        const data = JSON.parse(chunk)
        console.log('📊 解析后的数据:', data)
        
        if (data.type === 'tool_result') {
          // 工具调用结果，添加到左侧面板
          const toolResult = {
            id: Date.now() + Math.random(),
            toolName: data.toolName || '数学工具',
            toolInput: data.toolInput || '',
            content: data.content || '',
            timestamp: new Date()
          }
          
          this.toolResults.push(toolResult)
          console.log('🛠️ 添加工具结果:', toolResult)
          this.$forceUpdate()
          
        } else if (data.type === 'normal_reply') {
          // 普通AI回复，添加到右侧聊天窗口
          const content = data.content || ''
          aiMessage.text += content
          
          // 检查是否包含iframe
          this.extractAndShowIframe(content)
          
          this.$forceUpdate()
          this.scrollToBottom()
          
        } else {
          // 兼容非结构化格式，作为普通回复处理
          console.log('⚠️ 未知响应类型，作为普通回复处理:', data)
          aiMessage.text += chunk
          this.$forceUpdate()
          this.scrollToBottom()
        }
        
      } catch (e) {
        // 解析失败，检查是否包含工具结果特征或iframe
        if (this.containsToolResultPattern(chunk)) {
          console.log('🔧 检测到工具结果模式，直接添加到左侧面板')
          const toolResult = {
            id: Date.now() + Math.random(),
            toolName: this.extractToolName(chunk) || '数学工具',
            toolInput: this.extractToolInput(chunk) || '',
            content: chunk,
            timestamp: new Date()
          }
          
          this.toolResults.push(toolResult)
          this.$forceUpdate()
        } else {
          // 作为普通文本处理
          console.log('📝 作为普通文本处理:', chunk.substring(0, 50) + '...')
          aiMessage.text += chunk
          
          // 检查是否包含iframe
          this.extractAndShowIframe(chunk)
          
          this.$forceUpdate()
          this.scrollToBottom()
        }
      }
    },

    // 检查是否包含工具结果模式
    containsToolResultPattern(text) {
      const toolPatterns = [
        /📝\s*\*\*.*\*\*/,
        /📊\s*计算结果：/,
        /🧮\s*数学引擎/,
        /✅\s*计算完成/,
        /原方程：|原表达式：|原不等式：|计算结果：|分解结果：|化简结果：|展开结果：|解：|解集：/
      ]
      
      return toolPatterns.some(pattern => pattern.test(text))
    },

    // 提取工具名称
    extractToolName(text) {
      const titleMatch = text.match(/📝\s*\*\*([^*]+)\*\*/)
      if (titleMatch) {
        return titleMatch[1].trim()
      }
      
      if (text.includes('原方程：')) return '方程求解'
      if (text.includes('原表达式：')) return '表达式处理'
      if (text.includes('原不等式：')) return '不等式求解'
      if (text.includes('分解结果：')) return '因式分解'
      if (text.includes('化简结果：')) return '表达式化简'
      if (text.includes('展开结果：')) return '表达式展开'
      
      return '数学计算'
    },

    // 提取工具输入
    extractToolInput(text) {
      const inputPatterns = [
        /原方程：([^\n]+)/,
        /原表达式：([^\n]+)/,
        /原不等式：([^\n]+)/
      ]
      
      for (const pattern of inputPatterns) {
        const match = text.match(pattern)
        if (match) {
          return match[1].trim()
        }
      }
      
      return ''
    },

    // iframe 相关方法
    extractAndShowIframe(content) {
      // 使用正则表达式匹配iframe标签
      const iframeRegex = /<iframe[^>]*src=["']([^"']+)["'][^>]*>[\s\S]*?<\/iframe>/gi
      const match = iframeRegex.exec(content)
      
      if (match) {
        const src = match[1]
        console.log('🎯 检测到iframe:', src)
        
        // 提取标题信息
        let title = '交互式模拟'
        let description = ''
        
        // 从内容中提取标题和描述
        const titleMatch = content.match(/\*\*([^*]+)\*\*/i)
        if (titleMatch) {
          title = titleMatch[1]
        }
        
        // 提取描述信息
        const descMatch = content.match(/[：:]\s*([^<\n]+)/i)
        if (descMatch) {
          description = descMatch[1].trim()
        }
        
        // 设置当前iframe
        this.currentIframe = {
          src: src,
          title: title,
          description: description,
          timestamp: new Date()
        }
        
        this.iframeLoading = true
        console.log('🎯 显示iframe:', this.currentIframe)
      }
    },

    closeIframe() {
      this.currentIframe = null
      this.iframeLoading = false
    },

    onIframeLoad() {
      this.iframeLoading = false
      console.log('🎯 iframe加载完成')
    },

    // 回到首页
    goHome() {
      this.$router.push('/')
    },

    // TTS相关方法
    async playTTSForMessage(message) {
      try {
        if (!message || !message.text) return
        const content = this.cleanTextForSpeech(message.text)
        
        // 自动检测消息语言
        const detectedLang = this.detectLanguage(message.text)
        console.log(`[DualScreenStudy][TTS-inline] 消息语言检测: ${detectedLang}`)
        
        await this.playViaOpenAiTTS(content, detectedLang)
      } catch (e) {
        console.warn('[DualScreenStudy][TTS-inline] OpenAI TTS失败，回退浏览器TTS:', e)
        try {
          // 降级时也使用检测的语言
          const detectedLang = this.detectLanguage(message.text)
          await this.playViaBrowserTTS(message.text, detectedLang)
        } catch (err) {
          console.error('[DualScreenStudy][TTS-inline] 浏览器TTS也失败:', err)
        }
      }
    },

    async maybeSpeak(text) {
      try {
        if (!this.ttsEnabled) return
        const content = this.cleanTextForSpeech(text || '')
        if (!content.trim()) return
        
        // 自动检测语言，不依赖界面语言设置
        const detectedLang = this.detectLanguage(text)
        console.log(`[DualScreenStudy][TTS] 自动检测语言: ${detectedLang}, 界面语言: ${this.language}`)
        
        await this.playViaOpenAiTTS(content, detectedLang)
      } catch (e) {
        console.warn('[DualScreenStudy][TTS] OpenAI TTS播放失败，回退到浏览器TTS:', e)
        try {
          // 降级时也使用检测的语言
          const detectedLang = this.detectLanguage(text)
          await this.playViaBrowserTTS(text || '', detectedLang)
        } catch (err) {
          console.error('[DualScreenStudy][TTS] 浏览器TTS也失败:', err)
        }
      }
    },

    stopTTS() {
      try {
        if (this.ttsStream && typeof this.ttsStream.close === 'function') {
          this.ttsStream.close()
        }
      } catch (_) {}
      this.ttsStream = null

      try {
        if (this.ttsAbortController) {
          this.ttsAbortController.abort()
          this.ttsAbortController = null
        }
      } catch (_) {}

      try {
        const audio = this.$refs.ttsAudio
        if (audio) {
          audio.pause()
          audio.currentTime = 0
        }
      } catch (_) {}

      try { stopSpeaking() } catch (_) {}

      try {
        if (this.ttsMse) {
          try {
            if (this.ttsMse.mediaSource && this.ttsMse.mediaSource.readyState === 'open') {
              this.ttsMse.mediaSource.endOfStream()
            }
          } catch (_) {}
          this.ttsMse = null
        }
      } catch (_) {}

      try {
        if (this.ttsObjectUrl) {
          URL.revokeObjectURL(this.ttsObjectUrl)
          this.ttsObjectUrl = null
        }
      } catch (_) {}

      this.ttsPlaying = false
    },

    async playViaOpenAiTTS(text, lang = 'zh') {
      this.stopTTS()
      
      // 根据语言选择合适的语音
      const voiceMap = {
        'zh': 'zh-CN-XiaoxiaoNeural', // 中文使用专门的中文语音（晓晓）
        'en': 'alloy', // 英文使用alloy
        'ja': 'ja-JP-KeitaNeural' // 日语使用日语语音
      }
      
      const voice = voiceMap[lang] || 'alloy'
      try {
        console.log(`[DualScreenStudy] 使用OpenAI TTS流式合成: ${text.substring(0, 50)}..., 语音: ${voice}`)

        const tryStreamOnce = async (conf) => {
          const mime = conf.mime
          console.log(`[DualScreenStudy] 流式容器协商: mime=${mime}, responseFormat=${conf.responseFormat}, container=${conf.container}`)
          const audio = this.$refs.ttsAudio
          if (!audio) throw new Error('音频播放器未就绪')

          // 初始化 MediaSource
          const mediaSource = new MediaSource()
          const objectUrl = URL.createObjectURL(mediaSource)
          this.ttsObjectUrl = objectUrl
          this.ttsMse = { mediaSource, sourceBuffer: null, queue: [], appending: false }
          audio.src = objectUrl

          const ensureBuffer = () => new Promise((resolve, reject) => {
            const onOpen = () => {
              try {
                if (!this.ttsMse) return reject(new Error('MSE已清理'))
                this.ttsMse.sourceBuffer = mediaSource.addSourceBuffer(mime)
                this.ttsMse.sourceBuffer.addEventListener('updateend', () => {
                  if (!this.ttsMse) return
                  if (this.ttsMse.queue.length > 0 && !this.ttsMse.sourceBuffer.updating) {
                    const chunk = this.ttsMse.queue.shift()
                    try { this.ttsMse.sourceBuffer.appendBuffer(chunk) } catch (e) { console.error('append失败', e) }
                  }
                })
                resolve()
              } catch (e) {
                reject(e)
              }
            }
            if (mediaSource.readyState === 'open') onOpen()
            else mediaSource.addEventListener('sourceopen', onOpen, { once: true })
          })

          let firstChunk = true
          const appendChunk = async (bytes) => {
            if (!this.ttsMse || !this.ttsMse.sourceBuffer) return
            if (this.ttsMse.sourceBuffer.updating || this.ttsMse.queue.length > 0) {
              this.ttsMse.queue.push(bytes)
              return
            }
            // 首块嗅探
            if (firstChunk) {
              const b0 = bytes[0], b1 = bytes[1], b2 = bytes[2], b3 = bytes[3]
              if (conf.container === 'webm') {
                if (!(b0 === 0x1A && b1 === 0x45 && b2 === 0xDF && b3 === 0xA3)) {
                  throw new Error('返回数据非WebM容器，无法流式追加')
                }
              } else if (conf.container === 'mp4') {
                if (!(bytes.length > 8 && bytes[4] === 0x66 && bytes[5] === 0x74 && bytes[6] === 0x79 && bytes[7] === 0x70)) {
                  throw new Error('返回数据非MP4容器，无法流式追加')
                }
              }
            }

            try {
              this.ttsMse.sourceBuffer.appendBuffer(bytes)
              if (firstChunk) {
                firstChunk = false
                try {
                  this.ttsPlaying = true
                  await audio.play()
                } catch (e) {
                  console.warn('[DualScreenStudy] 自动播放被阻止，请与页面交互后重试', e)
                }
              }
            } catch (e) {
              console.error('[DualScreenStudy] appendBuffer错误:', e)
              throw e
            }
          }

          await ensureBuffer()

          const streamPromise = new Promise((resolve, reject) => {
            this.ttsStream = openAiTTSStream(text, {
              model: 'tts-1',
              voice: voice,
              speed: 1.0,
              responseFormat: conf.responseFormat,
              streamFormat: 'sse'
            }, {
              onChunk: (bytes) => {
                appendChunk(bytes).catch(err => reject(err))
              },
              onDone: () => {
                try {
                  if (this.ttsMse && this.ttsMse.mediaSource && this.ttsMse.mediaSource.readyState === 'open') {
                    this.ttsMse.mediaSource.endOfStream()
                  }
                } catch (_) {}
                this.ttsStream = null
                resolve()
              },
              onError: (e) => {
                reject(e)
              }
            })
          })

          console.log('[DualScreenStudy] 正在流式播放TTS...')
          await streamPromise
        }

        const primary = this.getPreferredStreamingFormat()
        if (!primary) throw new Error('浏览器不支持可流式容器（WebM/Opus 或 MP4/AAC）')

        const alt = (() => {
          try {
            if (!("MediaSource" in window) || !window.MediaSource.isTypeSupported) return null
            if (primary.container === 'webm' && window.MediaSource.isTypeSupported('audio/mp4; codecs="mp4a.40.2"')) return { mime: 'audio/mp4; codecs="mp4a.40.2"', responseFormat: 'mp4', container: 'mp4' }
            if (primary.container === 'mp4' && window.MediaSource.isTypeSupported('audio/webm; codecs="opus"')) return { mime: 'audio/webm; codecs="opus"', responseFormat: 'webm', container: 'webm' }
            return null
          } catch (_) { return null }
        })()

        try {
          await tryStreamOnce(primary)
        } catch (e) {
          // 容器不匹配或其他错误，尝试备选容器
          if (alt) {
            console.warn('[DualScreenStudy] 主容器流式失败，尝试备用容器...', e.message || e)
            // 清理MSE
            try { if (this.ttsMse && this.ttsMse.mediaSource && this.ttsMse.mediaSource.readyState === 'open') this.ttsMse.mediaSource.endOfStream() } catch (_) {}
            this.ttsMse = null
            try { if (this.ttsStream && typeof this.ttsStream.close === 'function') this.ttsStream.close() } catch (_) {}
            this.ttsStream = null
            try { if (this.ttsObjectUrl) URL.revokeObjectURL(this.ttsObjectUrl) } catch (_) {}
            this.ttsObjectUrl = null

            await tryStreamOnce(alt)
          } else {
            throw e
          }
        }

      } catch (error) {
        console.error('[DualScreenStudy] OpenAI 流式TTS失败，改用非流式:', error)
        // 清理当前MSE
        try {
          if (this.ttsMse && this.ttsMse.mediaSource && this.ttsMse.mediaSource.readyState === 'open') {
            this.ttsMse.mediaSource.endOfStream()
          }
        } catch (_) {}
        this.ttsMse = null

        // 回退到一次性下载再播
        try {
          const blob = await openAiTTS(text, {
            model: 'tts-1',
            voice: voice,
            speed: 1.0,
            responseFormat: 'mp3'
          })
          const url = URL.createObjectURL(blob)
          this.ttsObjectUrl = url
          const audio = this.$refs.ttsAudio
          if (!audio) throw new Error('音频播放器未就绪')
          audio.src = url
          this.ttsPlaying = true
          await audio.play()
          console.log('[DualScreenStudy] 回退非流式TTS播放成功')
        } catch (e2) {
          if (this.ttsObjectUrl) {
            URL.revokeObjectURL(this.ttsObjectUrl)
            this.ttsObjectUrl = null
          }
          this.ttsPlaying = false
          throw e2
        }
      }
    },

    async playViaBrowserTTS(text, lang = 'zh') {
      const language = lang === 'en' ? 'en-US' : 'zh-CN'
      await speakText(this.cleanTextForSpeech(text || ''), { language })
    },

    // 测试OpenAI TTS服务连接
    async testOpenAiTTSConnection() {
      try {
        console.log('[DualScreenStudy] 测试OpenAI TTS服务连接...')
        const result = await testOpenAiTTS()
        if (result.success) {
          console.log('[DualScreenStudy] ✅ OpenAI TTS服务连接正常:', result.message)
        } else {
          console.warn('[DualScreenStudy] ⚠️ OpenAI TTS服务连接异常:', result.message)
        }
      } catch (error) {
        console.error('[DualScreenStudy] ❌ OpenAI TTS服务连接测试失败:', error)
        // TTS服务不可用时不影响其他功能，只记录日志
      }
    },

    // 处理TTS开关切换
    onTTSToggle() {
      try {
        // 保存到localStorage
        localStorage.setItem('k12_auto_tts', this.ttsEnabled ? '1' : '0')
        console.log(`[DualScreenStudy] TTS自动播放已${this.ttsEnabled ? '开启' : '关闭'}`)
        
        // 如果关闭了TTS，停止当前播放
        if (!this.ttsEnabled) {
          this.stopTTS()
        }
        
        // 显示状态提示
        this.showTeacherTipMessage(
          this.ttsEnabled ? '🔊 语音播报已开启，AI回复将自动朗读' : '🔇 语音播报已关闭', 
          2000
        )
      } catch (error) {
        console.error('[DualScreenStudy] TTS开关切换失败:', error)
      }
    },

    // 自动检测文本语言
    detectLanguage(text) {
      if (!text || typeof text !== 'string') return 'en'
      
      // 清理文本，去除markdown和特殊字符
      const cleanText = this.cleanTextForSpeech(text)
      if (!cleanText.trim()) return 'en'
      
      // 检测中文字符
      const chineseRegex = /[\u4e00-\u9fa5]/g
      const chineseMatches = cleanText.match(chineseRegex)
      const chineseRatio = chineseMatches ? chineseMatches.length / cleanText.length : 0
      
      // 检测日文字符 
      const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/g
      const japaneseMatches = cleanText.match(japaneseRegex)
      const japaneseRatio = japaneseMatches ? japaneseMatches.length / cleanText.length : 0
      
      // 检测英文单词
      const englishRegex = /[a-zA-Z]+/g
      const englishMatches = cleanText.match(englishRegex)
      const englishWordCount = englishMatches ? englishMatches.length : 0
      
      console.log(`[DualScreenStudy] 语言检测 - 中文比例: ${chineseRatio.toFixed(2)}, 日文比例: ${japaneseRatio.toFixed(2)}, 英文单词: ${englishWordCount}`)
      
      // 语言判断逻辑
      if (chineseRatio > 0.1) {  // 中文字符超过10%
        return 'zh'
      } else if (japaneseRatio > 0.05) {  // 日文字符超过5%
        return 'ja'
      } else {
        return 'en'  // 默认英文
      }
    },

    cleanTextForSpeech(md = '') {
      try {
        let s = String(md)
        s = s.replace(/```[\s\S]*?```/g, ' ')
        s = s.replace(/`([^`]+)`/g, '$1')
        s = s.replace(/!\[([^\]]*)\]\([^\)]+\)/g, '$1')
        s = s.replace(/\[([^\]]+)\]\(([^\)]+)\)/g, '$1')
        s = s.replace(/[>*_#\-]{1,}/g, ' ')
        s = s.replace(/\s+/g, ' ').trim()
        if (s.length > 4000) s = s.slice(0, 4000)
        return s
      } catch (_) {
        return md
      }
    },

    // 语音输入相关方法
    toggleVoiceRecording() {
      if (this.isRecording) {
        this.stopVoiceInput()
      } else {
        this.startVoiceInput()
      }
    },

    async requestMicStream(constraints) {
      const isLocalhost = ['localhost', '127.0.0.1', '::1'].includes(location.hostname)
      const isSecure = location.protocol === 'https:' || isLocalhost || (window.isSecureContext === true)
      const mediaDevices = navigator && navigator.mediaDevices
      const hasStdAPI = !!(mediaDevices && mediaDevices.getUserMedia)
      const legacyGUM = (navigator && (navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia)) || null

      if (!hasStdAPI && !legacyGUM) {
        if (!isSecure) {
          throw new Error('浏览器禁用了麦克风采集：请使用 HTTPS 域名或通过反向代理启用 TLS')
        }
        throw new Error('浏览器不支持 getUserMedia（请升级浏览器或使用 Chrome/Edge/Safari 最新版）')
      }

      if (hasStdAPI) {
        return await mediaDevices.getUserMedia(constraints)
      }
      return await new Promise((resolve, reject) => {
        legacyGUM.call(navigator, constraints, resolve, reject)
      })
    },

    async startVoiceInput() {
      try {
        this.voiceStatus = '请求音频权限...'
        this.isRecording = true
        this.showVoicePanel = true
        
        const stream = await this.requestMicStream({ 
          audio: { 
            channelCount: 1, 
            echoCancellation: true, 
            noiseSuppression: true, 
            autoGainControl: true 
          } 
        })
        
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)({ latencyHint: 'interactive' })
        
        if (!this.audioContext.audioWorklet) {
          throw new Error('浏览器不支持 AudioWorklet，请更换现代浏览器')
        }
        
        await this.audioContext.audioWorklet.addModule('/worklets/pcm-processor.js')
        this.sampleRate = this.audioContext.sampleRate
        this.frameSize = Math.round(this.sampleRate / 50)
        this.sourceNode = this.audioContext.createMediaStreamSource(stream)
        this.workletNode = new AudioWorkletNode(this.audioContext, 'pcm-processor')
        
        this.workletNode.port.onmessage = (e) => {
          if (!e.data || e.data.type !== 'audio') return
          const float32 = e.data.samples
          let sum = 0
          for (let i = 0; i < float32.length; i++) sum += float32[i] * float32[i]
          this.meter = Math.sqrt(sum / float32.length)
          this.enqueueFloat(float32)
          this.flushFrames()
        }
        
        this.sourceNode.connect(this.workletNode)
        this.workletNode.connect(this.audioContext.destination)

        await this.openVoiceWS()
        this.voiceStatus = '录音中（流式发送）...'
      } catch (e) {
        console.error(e)
        this.voiceStatus = '启动失败: ' + e.message
        this.isRecording = false
      }
    },

    async openVoiceWS() {
      const { getWhisperWsUrl } = await import('../config/env.js')
      const url = getWhisperWsUrl()
      this.ws = new WebSocket(url)
      this.ws.binaryType = 'arraybuffer'
      this.wsState = 'connecting'
      
      this.ws.onopen = () => {
        this.wsState = 'connected'
        this.ws.send(JSON.stringify({ event: 'start', sampleRate: this.sampleRate, language: this.language }))
      }
      
      this.ws.onmessage = (evt) => {
        try {
          const msg = JSON.parse(evt.data)
          if (msg.event === 'ack') {
            this.voiceStatus = '开始流式发送...'
            this.isRecognizing = false
          } else if (msg.event === 'final') {
            const recognizedText = msg.text || ''
            this.voiceStatus = '已收到最终结果'
            this.isRecognizing = false
            if (recognizedText.trim()) {
              this.inputMessage = recognizedText.trim()
              this.adjustHeight()
            }
            try { this.ws.close() } catch {}
          } else if (msg.event === 'error') {
            this.voiceStatus = '服务错误: ' + msg.message
            this.isRecognizing = false
          }
        } catch (_) {}
      }
      
      this.ws.onclose = () => {
        this.wsState = 'disconnected'
        if (!this.isRecording) {
          this.voiceStatus = '待机'
          this.showVoicePanel = false
        }
        this.isRecognizing = false
      }
      
      this.ws.onerror = () => { this.wsState = 'error' }
    },

    enqueueFloat(chunk) {
      if (!this.accFloat || this.accFloat.length === 0) {
        this.accFloat = chunk
      } else {
        const merged = new Float32Array(this.accFloat.length + chunk.length)
        merged.set(this.accFloat, 0)
        merged.set(chunk, this.accFloat.length)
        this.accFloat = merged
      }
    },

    flushFrames() {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return
      while (this.accFloat && this.accFloat.length >= this.frameSize) {
        const frame = this.accFloat.subarray(0, this.frameSize)
        const rest = this.accFloat.subarray(this.frameSize)
        const ab = new ArrayBuffer(frame.length * 2)
        const view = new DataView(ab)
        for (let i = 0; i < frame.length; i++) {
          let s = Math.max(-1, Math.min(1, frame[i]))
          view.setInt16(i * 2, s < 0 ? s * 0x8000 : s * 0x7FFF, true)
        }
        this.ws.send(ab)
        const remain = new Float32Array(rest.length)
        remain.set(rest, 0)
        this.accFloat = remain
      }
    },

    stopVoiceInput() {
      try {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          this.ws.send(JSON.stringify({ event: 'end' }))
        }
      } catch {}
      
      try { if (this.workletNode) this.workletNode.disconnect() } catch {}
      try { if (this.sourceNode) this.sourceNode.disconnect() } catch {}
      try { if (this.audioContext && this.audioContext.state !== 'closed') this.audioContext.close() } catch {}
      
      this.workletNode = null
      this.sourceNode = null
      this.audioContext = null
      this.isRecording = false
      this.voiceStatus = '已停止，等待识别结果...'
      this.isRecognizing = true
    },

    cleanupVoice() {
      this.isRecording = false
      try { if (this.workletNode) this.workletNode.disconnect() } catch {}
      try { if (this.sourceNode) this.sourceNode.disconnect() } catch {}
      try { if (this.audioContext && this.audioContext.state !== 'closed') this.audioContext.close() } catch {}
      try { if (this.ws && this.ws.readyState === WebSocket.OPEN) this.ws.close() } catch {}
      this.ws = null
      this.accFloat = null
      this.wsState = 'disconnected'
      this.voiceStatus = '待机'
      this.isRecognizing = false
      this.showVoicePanel = false
    },

    // 输入处理方法
    handleKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    adjustHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.messageInput
        if (textarea) {
          textarea.style.height = 'auto'
          textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
        }
      })
    },

    // 消息发送和处理方法
    sendMessage() {
      if (!this.canSend || this.isLoading) return
      const raw = this.inputMessage.trim()
      const messageText = raw || (this.selectedImage ? '请解析我上传的图片' : '')
      if (!messageText && !this.selectedImage) return

      const imageFileToSend = this.selectedImageFile
      const previewUrl = this.selectedImage
      const imageUrlToSend = (!imageFileToSend && previewUrl && !previewUrl.startsWith('data:')) ? previewUrl : null

      const userMessage = {
        id: Date.now(),
        text: messageText,
        image: this.selectedImage,
        isUser: true,
        timestamp: new Date()
      }

      this.messages.push(userMessage)

      this.inputMessage = ''
      this.selectedImage = null
      this.selectedImageFile = null
      this.resetTextareaHeight()

      this.$nextTick(() => {
        this.scrollToBottom()
      })

      this.sendToRAG(messageText, imageFileToSend, imageUrlToSend)
    },

    // ThinkyAI API调用方法
    sendToRAG(message, imageFile = null, imageUrl = null) {
      this.stopTTS()

      if (this.currentEventSource) {
        this.currentEventSource.close()
      }

      this.isLoading = true

      // 创建AI响应消息，只用于普通回复
      const aiMessage = {
        id: Date.now() + 1,
        text: '',
        isUser: false,
        timestamp: new Date(),
        isStreaming: true
      }

      this.messages.push(aiMessage)
      this.scrollToBottom()

      // 获取会话ID
      const sessionId = this.getOrCreateSessionId()

      if (imageFile || imageUrl) {
        // 如果有图片，先使用多模态接口处理图片，然后再调用ThinkyAI
        this.currentEventSource = multimodalChatStream(
          this.memoryId,
          message,
          imageFile || imageUrl,
          this.language, // 传递语言参数
          (chunk) => {
            if (chunk === '[DONE]' || chunk.trim() === '[DONE]') return
            this.processStructuredChunk(chunk, aiMessage)
          },
          (error) => {
            console.error('[DualScreenStudy] 多模态聊天错误:', error)
            if (!aiMessage.text || aiMessage.text.trim() === '') {
              aiMessage.text = this.language === 'en' ? 'Sorry, the service is temporarily unavailable. Please try again later.' : '抱歉，服务暂时不可用，请稍后重试。'
            }
            aiMessage.isStreaming = false
            this.isLoading = false
            this.$forceUpdate()
            if (this.ttsEnabled && aiMessage.text && aiMessage.text.trim()) {
              this.maybeSpeak(aiMessage.text)
            }
          },
          () => {
            aiMessage.isStreaming = false
            this.isLoading = false
            this.currentEventSource = null
            this.$forceUpdate()
            this.scrollToBottom()
            this.maybeSpeak(aiMessage.text)
          }
        )
      } else {
        // 使用ThinkyAI接口进行引导式学习
        console.log('🎓 调用ThinkyAI接口:', { sessionId, message, subject: this.currentSubject, problem: this.currentProblem })
        
        this.currentEventSource = thinkyAiChatStream(
          sessionId,
          message,
          this.currentSubject,
          this.currentProblem,
          this.language, // 传递语言参数
          (chunk) => {
            if (chunk === '[DONE]' || chunk.trim() === '[DONE]') return
            this.processStructuredChunk(chunk, aiMessage)
          },
          (error) => {
            console.error('[DualScreenStudy] ThinkyAI聊天错误:', error)
            if (!aiMessage.text || aiMessage.text.trim() === '') {
              aiMessage.text = this.language === 'en' ? 'Sorry, ThinkyAI service is temporarily unavailable. Please try again later.' : '抱歉，ThinkyAI服务暂时不可用，请稍后重试。'
            }
            aiMessage.isStreaming = false
            this.isLoading = false
            this.$forceUpdate()
            if (this.ttsEnabled && aiMessage.text && aiMessage.text.trim()) {
              this.maybeSpeak(aiMessage.text)
            }
          },
          () => {
            console.log('🎓 ThinkyAI响应完成')
            aiMessage.isStreaming = false
            this.isLoading = false
            this.currentEventSource = null
            this.$forceUpdate()
            this.scrollToBottom()
            this.maybeSpeak(aiMessage.text)
          }
        )
      }
    },

    // 工具方法
    sameOriginUrl(u) {
      try {
        if (!u) return ''
        const parsed = new URL(u, window.location.origin)
        const origin = window.location.origin.replace(/\/+$/, '')
        return origin + parsed.pathname + parsed.search + parsed.hash
      } catch (_) {
        if (typeof u === 'string' && u.startsWith('/')) return window.location.origin.replace(/\/+$/, '') + u
        return u || ''
      }
    },

    openMobileUpload() {
      this.qrModalVisible = true
    },

    // 图片处理方法
    selectImage() {
      this.$refs.fileInput.click()
    },

    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file && file.type.startsWith('image/')) {
        this.selectedImageFile = file

        const reader = new FileReader()
        reader.onload = (e) => {
          this.selectedImage = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },

    removeImage() {
      this.selectedImage = null
      this.selectedImageFile = null
      this.$refs.fileInput.value = ''
    },

    handleMobileUploaded(payload) {
      try {
        if (payload && payload.imageUrl) {
          this.selectedImage = this.sameOriginUrl(payload.imageUrl)
          this.selectedImageFile = null
          this.qrModalVisible = false
          this.$nextTick(() => this.scrollToBottom())
        }
      } catch (_) {}
    },

    // 消息处理工具方法
    formatMessage(text) {
      if (!text || typeof text !== 'string') {
        return ''
      }
      return marked(text, { breaks: true, gfm: true })
    },

    formatTime(timestamp) {
      const locale = this.language === 'en' ? 'en-US' : 'zh-CN'
      return new Date(timestamp).toLocaleTimeString(locale, {
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    resetTextareaHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.messageInput
        if (textarea) {
          textarea.style.height = 'auto'
        }
      })
    },

    scrollToBottom() {
      this.$nextTick(() => {
        const messagesList = this.$refs.messagesList
        if (messagesList) {
          messagesList.scrollTop = messagesList.scrollHeight
        }
      })
    },

    // 女老师提示相关方法
    showTeacherTipMessage(message = (this.language === 'en' ? "Ask me anything anytime!" : '有问题随时问我哦！'), duration = 3000) {
      this.teacherTipText = message
      this.showTeacherTip = true
      
      // 清除之前的定时器
      if (this.teacherTipTimer) {
        clearTimeout(this.teacherTipTimer)
      }
      
      // 设置自动隐藏
      this.teacherTipTimer = setTimeout(() => {
        this.showTeacherTip = false
      }, duration)
    },

    hideTeacherTip() {
      this.showTeacherTip = false
      if (this.teacherTipTimer) {
        clearTimeout(this.teacherTipTimer)
        this.teacherTipTimer = null
      }
    },

    onTeacherClick() {
      const tips = [
        '点击左侧知识面板查看相关公式！',
        '试试语音输入功能吧！',
        '有数学问题尽管问我！',
        '可以上传图片让我帮你解题哦！',
        '我会一步步教你解题方法！'
      ]
      const randomTip = tips[Math.floor(Math.random() * tips.length)]
      this.showTeacherTipMessage(randomTip, 4000)
    },

    // 检查是否有从 ChatWindow 传递过来的待处理问题
    checkPendingQuestion() {
      try {
        const pendingData = sessionStorage.getItem('pendingQuestion')
        if (pendingData) {
          const questionData = JSON.parse(pendingData)
          
          // 清除 sessionStorage 中的数据，避免重复处理
          sessionStorage.removeItem('pendingQuestion')
          
          // 恢复用户输入的数据
          this.inputMessage = questionData.text || ''
          this.selectedImage = questionData.image || null
          this.selectedImageFile = questionData.imageFile || null
          this.memoryId = questionData.memoryId || Date.now().toString()
          this.language = questionData.language || 'en' // 获取语言参数
          
          console.log('🌍 从 ChatWindow 获取语言参数:', this.language)
          
          // 创建用户消息
          const userMessage = {
            id: Date.now(),
            text: questionData.text,
            image: questionData.image,
            isUser: true,
            timestamp: new Date()
          }
          
          this.messages.push(userMessage)
          
          // 显示老师提示
          this.showTeacherTipMessage(this.language === 'en' ? 'Let me help you solve this problem!' : '让我来帮你解决这个问题！', 3000)
          
          // 延迟发送消息，确保页面完全加载
          this.$nextTick(() => {
            setTimeout(() => {
              this.sendToRAG(
                questionData.text,
                questionData.imageFile,
                questionData.image && !questionData.image.startsWith('data:') ? questionData.image : null
              )
            }, 500)
          })
        }
      } catch (error) {
        console.error('处理待发送问题时出错:', error)
        // 清除可能损坏的数据
        sessionStorage.removeItem('pendingQuestion')
      }
    }
  },

  mounted() {
    this.language = 'en' // 默认英文，与 ChatWindow 保持一致
    try {
      const tts = localStorage.getItem('k12_auto_tts')
      if (tts === '1') this.ttsEnabled = true
    } catch (_) {}
    
    // 初始化ThinkyAI会话
    this.sessionId = this.getOrCreateSessionId()
    console.log('🎓 ThinkyAI会话已初始化:', this.sessionId)
    
    // 检查是否有从 ChatWindow 传递过来的问题
    this.checkPendingQuestion()
    
    // 测试OpenAI TTS服务连接
    this.testOpenAiTTSConnection()
    
    // 页面加载后显示欢迎提示
    setTimeout(() => {
      this.showTeacherTipMessage(this.language === 'en' ? 'Welcome to ThinkyAI math classroom!' : '欢迎来到ThinkyAI数学学习课堂！', 4000)
    }, 2000)
  },

  watch: {
    language(newLang) {
      try { localStorage.setItem('asrLanguage', newLang) } catch (_) {}
    },
    ttsEnabled(val) {
      try { localStorage.setItem('k12_auto_tts', val ? '1' : '0') } catch (_) {}
    }
  },

  beforeUnmount() {
    if (this.currentEventSource) this.currentEventSource.close()
    this.cleanupVoice()
    this.stopTTS()
  }
}
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100vh;
}

.dual-screen-container {
  width: 100%;
  max-width: 1400px;
  height: 95vh;
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  display: flex;
  overflow: hidden;
  margin: 2.5vh auto;
}

/* 左侧工具结果面板 */
.tool-results-panel {
  width: 40%;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.panel-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  flex-shrink: 0;
}

.panel-header h2 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
}

.panel-header p {
  font-size: 12px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.4;
}

/* 面板内容区域 */
.panel-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  height: 0; /* 强制flex子元素使用可用空间 */
}

/* 面板内容滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(0,0,0,0.05);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(103, 58, 183, 0.3);
  border-radius: 3px;
  transition: all 0.2s ease;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(103, 58, 183, 0.5);
}

/* 交互式模拟区域 */
.iframe-section {
  background: white;
  margin: 0 20px 12px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border: 2px solid #e3f2fd;
  overflow: hidden;
  flex-shrink: 0;
}

.iframe-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
}

.iframe-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
}

.iframe-icon {
  font-size: 16px;
}

.iframe-close-btn {
  background: rgba(255,255,255,0.1);
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iframe-close-btn:hover {
  background: rgba(255,255,255,0.2);
  transform: scale(1.05);
}

.iframe-container {
  position: relative;
  width: 100%;
  height: 300px;
  background: #f5f5f5;
}

.simulation-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.iframe-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e3f2fd;
  border-top: 4px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.iframe-loading p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.iframe-description {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.iframe-description p {
  margin: 0;
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

/* ThinkyAI 结构化解题步骤区域 */
.structured-solution-section {
  margin: 0 20px 0 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  padding: 0 8px 20px 0;
}

/* 自定义滚动条样式 */
.structured-solution-section::-webkit-scrollbar {
  width: 6px;
}

.structured-solution-section::-webkit-scrollbar-track {
  background: rgba(0,0,0,0.05);
  border-radius: 3px;
}

.structured-solution-section::-webkit-scrollbar-thumb {
  background: rgba(103, 58, 183, 0.3);
  border-radius: 3px;
  transition: all 0.2s ease;
}

.structured-solution-section::-webkit-scrollbar-thumb:hover {
  background: rgba(103, 58, 183, 0.5);
}

/* 占位符区域 */
.empty-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.placeholder-content {
  text-align: center;
  max-width: 300px;
  color: #6c757d;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.placeholder-content h3 {
  color: #495057;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
}

.placeholder-content p {
  margin-bottom: 16px;
  line-height: 1.5;
  font-size: 14px;
}

.placeholder-content ul {
  text-align: left;
  margin-bottom: 20px;
  padding-left: 20px;
}

.placeholder-content li {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.placeholder-tip {
  background: linear-gradient(135deg, #e3f2fd 0%, #f8f9ff 100%);
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 12px;
  font-size: 12px;
  color: #1565c0;
  font-weight: 500;
}

/* 问题识别区域 */
.problem-identification {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #e8f4fd;
  overflow: hidden;
}

.problem-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
}

.problem-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
}

.problem-icon {
  font-size: 16px;
}

.problem-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.difficulty-badge {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
  background: rgba(255,255,255,0.2);
}

.difficulty-easy { background: rgba(76, 175, 80, 0.2) !important; }
.difficulty-medium { background: rgba(255, 152, 0, 0.2) !important; }
.difficulty-hard { background: rgba(244, 67, 54, 0.2) !important; }
.difficulty-expert { background: rgba(156, 39, 176, 0.2) !important; }

.close-btn {
  background: rgba(255,255,255,0.1);
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255,255,255,0.2);
  transform: scale(1.05);
}

.problem-content {
  padding: 12px 16px;
}

.problem-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.problem-type, .problem-text {
  font-size: 13px;
  line-height: 1.4;
}

.label {
  font-weight: 600;
  color: #2E7D32;
}

/* 解题步骤区域 */
.solution-steps {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #e8f4fd;
  overflow: hidden;
}

.steps-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.steps-icon {
  font-size: 16px;
}

.steps-list {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.step-item {
  padding: 10px;
  background: #fafffe;
  border-radius: 6px;
  border: 1px solid #e8f5e8;
  transition: all 0.2s ease;
}

.step-item:hover {
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.1);
  transform: translateY(-1px);
}

.step-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.step-number {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-title {
  font-weight: 600;
  color: #2E7D32;
  font-size: 13px;
}

.step-description {
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
}

.step-reasoning, .step-formula, .step-guide {
  margin-top: 6px;
  font-size: 11px;
  color: #666;
  padding: 6px 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.step-guide {
  background: #e8f4fd;
  border-left: 3px solid #2196F3;
  color: #1565c0;
}

.step-label {
  font-weight: 600;
}

.step-formula code {
  background: #e8f5e8;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  color: #2E7D32;
}

/* 知识点区域 */
.knowledge-points {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #e8f4fd;
  overflow: hidden;
}

.knowledge-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.knowledge-icon {
  font-size: 16px;
}

.knowledge-content {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.knowledge-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.group-label {
  font-weight: 600;
  color: #673AB7;
  font-size: 12px;
}

.knowledge-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.knowledge-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.knowledge-tag.primary {
  background: #e8eaf6;
  color: #3f51b5;
  border: 1px solid #c5cae9;
}

.knowledge-tag.secondary {
  background: #f3e5f5;
  color: #7b1fa2;
  border: 1px solid #e1bee7;
}

/* 常见错误区域 */
.common-mistakes {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #e8f4fd;
  overflow: hidden;
}

.mistakes-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.mistakes-icon {
  font-size: 16px;
}

.mistakes-list {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mistake-item {
  padding: 12px;
  background: #fff8e1;
  border-radius: 8px;
  border: 1px solid #ffecb3;
}

.mistake-description {
  font-size: 12px;
  color: #e65100;
  margin-bottom: 6px;
  line-height: 1.4;
}

.mistake-correction {
  font-size: 11px;
  color: #2e7d32;
  padding: 6px 10px;
  background: #e8f5e8;
  border-radius: 4px;
}

.correction-label {
  font-weight: 600;
}

/* 学习建议区域 */
.recommendations {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #e8f4fd;
  overflow: hidden;
}

.recommendations-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #FF5722 0%, #D84315 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.recommendations-icon {
  font-size: 16px;
}

.recommendations-list {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #FF5722;
}

.rec-icon {
  font-size: 14px;
  margin-top: 1px;
  flex-shrink: 0;
}

.rec-text {
  font-size: 12px;
  line-height: 1.4;
  color: #333;
}


/* 工具结果内容区域 */
.tool-results-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  transition: all 0.3s ease;
  height: 0; /* 强制flex子元素使用可用空间 */
}

.tool-results-content.has-iframe {
  max-height: 40%;
}

.tool-results-content.has-structured {
  display: none;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: #495057;
}

.empty-state p {
  font-size: 14px;
  line-height: 1.5;
}

.tool-result-item {
  background: white;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s;
}

.tool-result-item:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.tool-name {
  font-weight: 600;
  color: #667eea;
  font-size: 14px;
}

.tool-time {
  font-size: 12px;
  color: #adb5bd;
}

.tool-input {
  margin-bottom: 12px;
  font-size: 13px;
  color: #6c757d;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.tool-output {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* 右侧聊天窗口 */
.chat-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.chat-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.header-left {
  flex: 1;
  min-width: 0;
}

.chat-header h2 {
  font-size: 18px;
  color: #333;
  margin-bottom: 8px;
}

/* 学习状态面板 */
.learning-status-panel {
  margin-top: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border-radius: 12px;
  border: 1px solid rgba(33, 150, 243, 0.1);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-title {
  font-size: 14px;
  font-weight: 600;
  color: #1565c0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.phase-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.phase-explore {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.phase-practice {
  background: linear-gradient(135deg, #2196f3, #1976d2);
}

.phase-improve {
  background: linear-gradient(135deg, #4caf50, #388e3c);
}

.phase-master {
  background: linear-gradient(135deg, #9c27b0, #7b1fa2);
}

.phase-default {
  background: linear-gradient(135deg, #757575, #424242);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: white;
  border-radius: 10px;
  border: 1px solid rgba(33, 150, 243, 0.1);
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.status-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

.card-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: rgba(33, 150, 243, 0.1);
}

.card-content {
  flex: 1;
  min-width: 0;
}

.card-value {
  font-size: 16px;
  font-weight: 700;
  color: #1565c0;
  line-height: 1.2;
  margin-bottom: 2px;
  word-break: break-all;
}

.card-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 不同卡片的特色颜色 */
.status-card.rounds .card-icon {
  background: rgba(33, 150, 243, 0.1);
}

.status-card.topic .card-icon {
  background: rgba(76, 175, 80, 0.1);
}

.status-card.problems .card-icon {
  background: rgba(255, 152, 0, 0.1);
}

.status-card.time .card-icon {
  background: rgba(156, 39, 176, 0.1);
}

.status-card.rounds .card-value {
  color: #1976d2;
}

.status-card.topic .card-value {
  color: #388e3c;
}

.status-card.problems .card-value {
  color: #f57c00;
}

.status-card.time .card-value {
  color: #7b1fa2;
}

.status-text {
  line-height: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 顶部语言选择器样式（与 ChatWindow 保持一致风格） */
.language-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 8px;
  background: rgba(255,255,255,0.9);
  border: 1px solid #eee;
}

.language-label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.global-lang-select {
  min-width: 110px;
  height: 28px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #333;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.global-lang-select:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.global-lang-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

/* TTS语音开关样式 */
.tts-toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tts-toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
  cursor: pointer;
}

.tts-toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.tts-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 24px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 2px;
}

.tts-toggle-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.tts-toggle-switch input:checked + .tts-toggle-slider {
  background-color: #4CAF50;
}

.tts-toggle-switch input:checked + .tts-toggle-slider:before {
  transform: translateX(24px);
}

.tts-toggle-switch:hover .tts-toggle-slider {
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
}

.tts-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #666;
  transition: all 0.3s ease;
  z-index: 1;
}

.tts-toggle-switch input:checked + .tts-toggle-slider .tts-icon {
  color: white;
}

.tts-toggle-label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
  transition: color 0.3s ease;
  white-space: nowrap;
}

.debug-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #FF9800;
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(255, 152, 0, 0.2);
}

.debug-btn:hover {
  background: #F57C00;
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(255, 152, 0, 0.3);
}

.debug-btn:active {
  transform: translateY(0);
}

.home-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.home-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.home-btn:active {
  transform: translateY(0);
}

.status-indicator {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #6c757d;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #28a745;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #fafbfc;
}

.message {
  display: flex;
  margin-bottom: 20px;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin: 0 10px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.message.user .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message.assistant .message-avatar {
  background: #f1f3f5;
  color: #333;
}

.message-content {
  max-width: 70%;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.5;
}

.message.user .message-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-bubble {
  background: white;
  color: #333;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 4px;
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.message-time {
  font-size: 11px;
  color: #adb5bd;
}

.message.user .message-time {
  text-align: right;
}

.message.assistant .message-time {
  text-align: left;
}

.tts-inline-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 22px;
  padding: 0;
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 6px;
  background: #f7f7f7;
  color: #444;
  cursor: pointer;
}

.tts-inline-btn:hover {
  background: #eef7f2;
  color: #0f9d58;
}


/* 流式响应指示器 */
.streaming-indicator {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #007bff;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.chat-input {
  padding: 20px;
  background: white;
  border-top: 1px solid #e9ecef;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 10px;
}

.input-wrapper {
  flex: 1;
  position: relative;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  padding: 12px 16px;
  display: flex;
  align-items: flex-end;
  gap: 10px;
  transition: all 0.3s;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.image-preview {
  position: relative;
  flex-shrink: 0;
}

.image-preview img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.5;
  max-height: 120px;
  min-height: 20px;
}

.input-actions {
  display: flex;
  gap: 5px;
}

.input-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #adb5bd;
  transition: all 0.3s;
  border-radius: 50%;
}

.input-btn:hover {
  background: #f1f3f5;
  color: #667eea;
}

.input-btn.active {
  background: #ff4444;
  color: white;
  animation: pulse 1s infinite;
}

.send-btn {
  padding: 10px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
}

.send-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.send-btn:active {
  transform: translateY(0);
}

.send-btn:disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 录音指示器 */
.recording-indicator {
  display: none;
  align-items: center;
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border-radius: 20px;
  font-size: 13px;
  margin-bottom: 10px;
  animation: recording 1.5s infinite;
}

@keyframes recording {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.recording-indicator.active {
  display: flex;
}

.recording-dot {
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 1s infinite;
}

/* 语音输入面板 */
.voice-overlay {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  pointer-events: none;
  z-index: 2000;
  animation: slideUpIn 0.3s ease-out;
}

@keyframes slideUpIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.voice-panel {
  margin: 16px;
  width: min(580px, calc(100% - 32px));
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  backdrop-filter: blur(20px);
  pointer-events: auto;
  z-index: 2001;
  overflow: hidden;
}

.voice-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0,0,0,0.06);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.voice-title {
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.voice-close {
  border: none;
  background: rgba(255,255,255,0.1);
  color: white;
  cursor: pointer;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-close:hover {
  background: rgba(255,255,255,0.2);
  transform: scale(1.05);
}

.voice-main {
  padding: 24px 20px;
}

.voice-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  margin-bottom: 24px;
}

.wave-container {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 60px;
  justify-content: center;
}

.wave-bar {
  width: 4px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  transition: all 0.2s ease;
  opacity: 0.3;
}

.wave-container.active .wave-bar {
  opacity: 1;
  animation: waveMove 1.2s ease-in-out infinite;
}

@keyframes waveMove {
  0%, 100% {
    transform: scaleY(0.2);
  }
  50% {
    transform: scaleY(1);
  }
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
}

.recording-pulse {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-core {
  width: 16px;
  height: 16px;
  background: #ff4444;
  border-radius: 50%;
  z-index: 2;
}

.pulse-ring {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 2px solid #ff4444;
  border-radius: 50%;
  animation: pulseRing 1.5s ease-out infinite;
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.processing-spinner {
  width: 40px;
  height: 40px;
}

.spinner-svg {
  width: 100%;
  height: 100%;
  animation: spin 1s linear infinite;
}

.spinner-circle {
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: spinStroke 1.5s ease-in-out infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spinStroke {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

.standby-icon {
  font-size: 32px;
  opacity: 0.6;
}

.voice-status-section {
  text-align: center;
  margin-bottom: 20px;
}

.voice-status-text {
  font-size: 18px;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8px;
}

.voice-hint {
  font-size: 14px;
  color: #718096;
}

.volume-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 0 8px;
}

.volume-label {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
  min-width: 40px;
}

.volume-meter {
  flex: 1;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.volume-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.1s ease, background 0.2s ease;
  position: relative;
}

.voice-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(0,0,0,0.06);
  background: rgba(247,250,252,0.5);
}

.voice-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.lang-select {
  height: 36px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #2d3748;
  padding: 0 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.stop-voice-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 10px;
  border: none;
  background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
  color: white;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(255,68,68,0.25);
}

.stop-voice-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff3333 0%, #bb2222 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(255,68,68,0.35);
}

.stop-voice-btn:disabled {
  background: #cbd5e0;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f3f5;
}

::-webkit-scrollbar-thumb {
  background: #adb5bd;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #868e96;
}

/* 右下角女老师样式 */
.teacher-corner {
  position: fixed;
  bottom: 160px;
  right: 40px;
  z-index: 1500;
  pointer-events: none;
}

.teacher-image-container {
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.teacher-corner-image {
  width: 120px;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  animation: teacherFloat 4s ease-in-out infinite;
  pointer-events: auto;
  cursor: pointer;
}

.teacher-corner-image:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

@keyframes teacherFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.teacher-speech-bubble {
  position: absolute;
  bottom: 100%;
  right: 10px;
  margin-bottom: 15px;
  background: white;
  border-radius: 18px;
  padding: 12px 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  max-width: 200px;
  pointer-events: auto;
  animation: bubbleAppear 0.3s ease-out;
}

@keyframes bubbleAppear {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.speech-content {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
}

.speech-arrow {
  position: absolute;
  bottom: -8px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .tool-results-panel {
    width: 35%;
  }
  
  .chat-panel {
    width: 65%;
  }
}

@media (max-width: 768px) {
  .dual-screen-container {
    flex-direction: column;
    height: 100vh;
    padding: 10px;
  }

  .tool-results-panel {
    width: 100%;
    height: 40%;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  .chat-panel {
    width: 100%;
    height: 60%;
  }

  /* 移动端iframe样式调整 */
  .iframe-section {
    margin: 0 10px 15px 10px;
  }

  .iframe-container {
    height: 300px;
  }

  .iframe-header {
    padding: 10px 12px;
  }

  .iframe-title {
    font-size: 13px;
  }

  .iframe-description {
    padding: 10px 12px;
  }

  .iframe-description p {
    font-size: 12px;
  }

  .tool-results-content.has-iframe {
    max-height: 40%;
  }

  .voice-panel {
    margin: 12px;
    width: calc(100% - 24px);
    border-radius: 16px;
  }

  .voice-main {
    padding: 20px 16px;
  }

  .wave-container {
    height: 50px;
  }

  .voice-status-text {
    font-size: 16px;
  }

  .voice-controls {
    flex-direction: column;
    gap: 12px;
  }

  .lang-select, .stop-voice-btn {
    width: 100%;
    justify-content: center;
  }

  .header-actions {
    gap: 8px;
  }

  .tts-toggle-container {
    gap: 4px;
  }

  .tts-toggle-switch {
    width: 40px;
    height: 20px;
  }

  .tts-toggle-slider:before {
    height: 16px;
    width: 16px;
  }

  .tts-toggle-switch input:checked + .tts-toggle-slider:before {
    transform: translateX(20px);
  }

  .tts-toggle-label {
    font-size: 10px;
  }

  .tts-icon {
    width: 10px;
    height: 10px;
  }

  .home-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .teacher-corner {
    bottom: 100px;
    right: 20px;
  }

  .teacher-corner-image {
    width: 80px;
  }

  .teacher-speech-bubble {
    max-width: 150px;
    padding: 10px 12px;
    margin-bottom: 10px;
  }

  .speech-content {
    font-size: 12px;
  }
}
</style>
