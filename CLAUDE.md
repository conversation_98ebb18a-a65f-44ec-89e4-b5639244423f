# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI K12 education assistant project with multiple components: a Spring Boot backend (ai-code-helper-master), a Vue.js frontend (ai-k12-frontend), and various AI integration tools. The application provides educational content, math problem solving, speech synthesis, and RAG-enhanced knowledge base queries.

### Key Technologies
- **Backend**: Java 21, Spring Boot 3.5.4, LangChain4j 1.1.0, Maven
- **Frontend**: Vue.js 3.3.4, Vite 4.4.9, Vue Router 4.5.1
- **AI**: Google Gemini API, BigModel API, Azure Speech SDK, Symja math engine
- **Database**: PostgreSQL with PGVector for vector storage
- **Additional**: OCR (Tesseract), PDF processing (Apache Tika, PDFBox), TTS integration

## Quick Start Commands

### Backend (ai-code-helper-master)
```bash
cd ai-code-helper-master
./mvnw spring-boot:run           # Start backend service (port 8081)
./mvnw package                   # Build project
./mvnw test                      # Run tests
./mvnw test -Dtest=ClassName     # Run specific test class
```

### Frontend (ai-k12-frontend)
```bash
cd ai-k12-frontend
npm install                      # Install dependencies
npm run dev                      # Start dev server (port 5173)
npm run build                    # Build for production
npm run preview                  # Preview production build
```

## Architecture Overview

### Backend Structure (ai-code-helper-master)
```
src/main/java/com/hujun/aicodehelper/
├── AiCodeHelperApplication.java    # Main Spring Boot entry point
├── ai/                            # AI service layer
│   ├── catalog/                   # AI provider catalog system
│   ├── model/                     # AI model configurations
│   ├── rag/                       # RAG implementation
│   └── tools/                     # AI tools and utilities
├── chat/                          # Chat memory and management
├── config/                        # Spring configuration
├── controller/                    # REST API controllers
├── mobileupload/                  # Mobile file upload handling
├── model/                         # Data models and entities
└── services/                      # Business logic services
```

### API Endpoints

#### General Chat
- `GET /api/ai/chat?memoryId=123&message=hello` - SSE streaming chat

#### RAG Enhanced Features
- `POST /api/ai/rag/chat` - Sync RAG Q&A with knowledge sources
- `GET /api/ai/rag/chat-stream` - SSE streaming RAG chat
- `POST /api/ai/rag/search` - Knowledge base search
- `GET /api/ai/rag/stats` - Knowledge base statistics

### Frontend Structure (ai-k12-frontend)
```
ai-k12-frontend/
├── src/
│   ├── components/        # Vue components
│   ├── router/           # Vue Router configuration
│   ├── views/            # Page-level components
│   ├── assets/           # Static assets
│   ├── App.vue           # Main application component
│   └── main.js           # Application entry point
├── public/               # Public static files
└── dist/                 # Build output directory
```

## Configuration

### Required Setup
1. **API Keys**: Configure in `ai-code-helper-master/src/main/resources/application.yml`
   - Google AI API key for Gemini models
   - BigModel API key for alternative AI provider
   - Azure Speech SDK credentials (optional)
   - PostgreSQL connection (port 5433 by default)

2. **Environment**: Ensure PostgreSQL with PGVector extension is running

### Key Configuration Files
- `application.yml` - Main configuration with profiles (postgresql, gemini, bigmodel)
- `data/` - Knowledge base and training data
- Environment variables for API keys and service URLs

## Development Workflow

### Adding New Features
1. **Backend**: Add new endpoints in `AiController.java`
2. **RAG Enhancement**: Extend `RagService.java` for new knowledge sources
3. **Frontend**: Create new Vue components in `src/components/`
4. **Testing**: Use `./mvnw test` to validate changes

### Knowledge Base Updates
- Add new `.md` files to `src/main/resources/docs/`
- Restart backend to trigger re-indexing
- Use `/api/ai/rag/stats` to verify document count

### Common Development Tasks
- **Debug RAG**: Check `/api/ai/rag/stats` and browser console logs
- **Test API**: Use curl commands from RAG_API_GUIDE.md
- **Frontend Changes**: Hot reload with `npm run dev`
- **Database Issues**: Verify PostgreSQL connection and PGVector extension

## Service URLs
- **Frontend**: http://localhost:5173 (ai-k12-frontend)
- **Backend API**: http://localhost:8081/api (ai-code-helper-master)
- **Database**: PostgreSQL with PGVector on port 5433

## Key Features
- **K12 Education Focus**: Specialized for educational content and math problem solving
- **Multi-AI Provider Support**: Gemini and BigModel integration with catalog system
- **RAG Integration**: Knowledge base queries with JSON and markdown document support
- **Speech Synthesis**: TTS integration with language detection
- **OCR Support**: Image and PDF text extraction using Tesseract and Tika
- **Math Engine**: Symja integration for mathematical computations
- **Mobile Upload**: Specialized mobile file upload handling
- **Streaming Responses**: Real-time AI responses via Server-Sent Events