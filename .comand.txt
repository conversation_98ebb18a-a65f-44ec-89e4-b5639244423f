scp -i "hujun.pem" /Users/<USER>/Desktop/edu-ai/ai-code-helper-master/target/ai-code-helper-0.0.1-SNAPSHOT.jar <EMAIL>:/home/<USER>/

scp -i "hujun.pem" -r /Users/<USER>/Desktop/edu-ai/ai-k12-frontend/dist <EMAIL>:/home/<USER>/

scp -i "hujun.pem" /Users/<USER>/Desktop/edu-ai/ai-code-helper-master/src/main/resources/nginx.conf <EMAIL>:/home/<USER>/



sudo cp -r assets/ /var/www/thinkyai/
sudo cp -r worklets/ /var/www/thinkyai/
sudo cp -r images /var/www/thinkyai/
sudo cp index.html /var/www/thinkyai/



 sudo docker run -d -p 5050:5050  travisvn/openai-edge-tts:latest


http://www.thinkyai.net/.well-known/acme-challenge/test.txt

https://www.thinkyai.net

https://thinkyai.net