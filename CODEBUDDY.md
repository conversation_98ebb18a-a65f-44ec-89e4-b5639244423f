# CODEBUDDY.md

This document provides guidance for operating in this AI K12 education assistant repository.

## Project Overview

This is a comprehensive AI K12 education platform with multiple integrated components:
- **Backend**: Spring Boot application (`ai-code-helper-master/`) with Java 21, LangChain4j 1.1.0, and <PERSON>ven
- **Frontend**: Vue.js 3 application (`ai-k12-frontend/`) with Vite 4.4.9 and Vue Router
- **AI Integration**: Google Gemini API, BigModel API, Azure Speech SDK, Symja math engine
- **Database**: PostgreSQL with PGVector for vector storage and chat memory
- **Document Processing**: OCR (Tesseract), PDF processing (Apache Tika, PDFBox), TTS integration

## Essential Commands

### Backend Development (ai-code-helper-master/)
```bash
cd ai-code-helper-master
./mvnw spring-boot:run                    # Start main AI service (port 8081)
./mvnw spring-boot:run -Dspring-boot.run.profiles=postgresql  # With PostgreSQL profile
./mvnw spring-boot:run -Dspring-boot.run.main-class=com.hujun.pdfparser.MarkdownApplication  # Document processing mode
./mvnw clean package                      # Build JAR to target/
./mvnw test                              # Run all tests
./mvnw test -Dtest=ClassName             # Run specific test class
```

### Frontend Development (ai-k12-frontend/)
```bash
cd ai-k12-frontend
npm install                              # Install dependencies
npm run dev                              # Start dev server (port 5173)
npm run build                            # Build for production
npm run preview                          # Preview production build
```

## Architecture Overview

### Multi-Application Structure
The repository contains two main Spring Boot applications:
1. **AiCodeHelperApplication** - Main AI assistant service at port 8081
2. **MarkdownApplication** - Document processing service (markdown-only mode)

### Backend Architecture (ai-code-helper-master/)
```
src/main/java/com/hujun/
├── aicodehelper/                    # Main AI assistant module
│   ├── ai/                          # AI service layer
│   │   ├── catalog/                 # AI provider catalog system
│   │   ├── model/                   # AI model configurations  
│   │   ├── rag/                     # RAG implementation with RagService
│   │   ├── tools/                   # AI tools and utilities
│   │   └── SmartAiCodeHelperService.java
│   ├── chat/                        # Chat memory management
│   ├── controller/                  # REST API controllers
│   ├── mobileupload/               # Mobile file upload handling
│   └── services/                   # Business logic services
└── pdfparser/                      # Document processing module
    ├── markdown/                   # Markdown conversion services
    └── MarkdownApplication.java
```

### Frontend Architecture (ai-k12-frontend/)
```
src/
├── api/                            # API client modules
├── components/                     # Vue components
├── config/                         # Configuration files
├── router/                         # Vue Router setup
├── utils/                          # Utility functions
├── App.vue                         # Main application component
└── main.js                         # Application entry point
```

## Configuration Requirements

### Database Setup
PostgreSQL with PGVector extension is required:
```sql
CREATE EXTENSION IF NOT EXISTS vector;
```

### API Keys Configuration
Configure in `ai-code-helper-master/src/main/resources/application-postgresql.yml`:
- Google AI API key for Gemini models
- BigModel API key for alternative AI provider  
- Azure Speech SDK credentials (optional)
- PostgreSQL connection details (default port 5433)

### Spring Profiles
The application uses multiple profiles that can be activated:
- `postgresql` - Database configuration
- `gemini` - Google Gemini AI provider
- `bigmodel` - BigModel AI provider
- `modelscope` - ModelScope AI provider
- `markdown` - Document processing mode

## Key API Endpoints

### General Chat
- `GET /api/ai/chat?memoryId=123&message=hello` - SSE streaming chat

### RAG Enhanced Features  
- `POST /api/ai/rag/chat` - Synchronous RAG Q&A with knowledge sources
- `GET /api/ai/rag/chat-stream` - SSE streaming RAG chat
- `POST /api/ai/rag/search` - Knowledge base search
- `GET /api/ai/rag/stats` - Knowledge base statistics

### Document Processing
- Available when running MarkdownApplication for PDF parsing and format conversion

## Service URLs
- **Frontend**: http://localhost:5173 (ai-k12-frontend)
- **Backend API**: http://localhost:8081/api (ai-code-helper-master)  
- **Database**: PostgreSQL with PGVector on port 5433

## Development Workflow

### Adding RAG Knowledge
- Add `.md` files to `src/main/resources/docs/`
- Add JSON data to `data/cambridge/` or `data/myelt/` directories
- Restart backend to trigger re-indexing
- Verify with `/api/ai/rag/stats` endpoint

### Testing RAG Features
- Use `/api/ai/rag/stats` to verify document count
- Check browser console logs for debugging
- Ensure PostgreSQL connection and PGVector extension are working

### Code Conventions
- Java: 4-space indent, PascalCase classes, camelCase methods/fields
- Packages organized by layer: `controller`, `service`, `config`, `model`
- Vue: PascalCase components, camelCase utilities
- Constructor injection preferred for dependencies
- JUnit 5 + Spring Boot Test for testing

## Key Features
- **K12 Education Focus**: Specialized for educational content and math problem solving
- **Multi-AI Provider Support**: Gemini and BigModel integration with catalog system
- **RAG Integration**: Knowledge base queries with JSON and markdown document support  
- **Speech Synthesis**: TTS integration with language detection
- **OCR Support**: Image and PDF text extraction using Tesseract and Tika
- **Math Engine**: Symja integration for mathematical computations
- **Mobile Upload**: Specialized mobile file upload handling
- **Streaming Responses**: Real-time AI responses via Server-Sent Events