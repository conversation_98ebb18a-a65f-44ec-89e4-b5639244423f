# Repository Guidelines

## Project Structure & Module Organization
- Backend: Spring Boot under `src/main/java` and resources in `src/main/resources`; tests in `src/test/java`.
- Frontend: Vue 3 + Vite in `ai-k12-frontend/` (`public/` for static assets, `src/` for code).
- Data & Docs: `data/` (sample JSON/knowledge), `doc/` (design/notes).
- Entrypoints: `com.hujun.aicodehelper.AiCodeHelperApplication` (main API) and `com.hujun.pdfparser.MarkdownApplication` (markdown-only mode).

## Build, Test, and Development Commands
- Backend dev: `mvn spring-boot:run` (profiles: `-Dspring-boot.run.profiles=postgresql` or `markdown`).
- Alternate main: `mvn spring-boot:run -Dspring-boot.run.main-class=com.hujun.pdfparser.MarkdownApplication`.
- Backend build: `mvn clean package` → JAR in `target/`.
- Backend tests: `mvn test` (JUnit 5 + Spring Boot Test).
- Frontend dev: `cd ai-k12-frontend && npm install && npm run dev`.
- Frontend build: `cd ai-k12-frontend && npm run build` → `ai-k12-frontend/dist`.

## Coding Style & Naming Conventions
- Java: 4-space indent. Classes `PascalCase`; methods/fields `camelCase`; constants `UPPER_SNAKE_CASE`.
- Packages: `com.hujun.aicodehelper` or `com.hujun.pdfparser`; group by layer (`controller`, `service`, `config`, `model`).
- Dependency injection: prefer constructor injection; keep Lombok minimal and consistent.
- Vue: components in `src/components` as `PascalCase.vue`; utilities in `src/utils` as `camelCase.js`.

## Testing Guidelines
- Frameworks: JUnit 5 and Spring Boot Test.
- Location/naming: mirror packages under `src/test/java`; suffix with `*Test.java` (e.g., `RagConfigJsonTest`).
- Scope: unit tests for pure logic (e.g., JSON/string utils); slice/integration tests for Spring beans.
- Run: `mvn test`. Keep tests fast, deterministic; avoid external network calls.

## Commit & Pull Request Guidelines
- Commits: clear, action-oriented subject in present tense; keep changes scoped; separate refactors from behavior changes.
- Branches: `feature/<short-name>` or `fix/<issue-id>-<short-name>`.
- Pull requests: include purpose, key changes, testing notes, and screenshots or API samples; link issues. Ensure `mvn test` and frontend build pass locally.

## Security & Configuration Tips
- Do not commit secrets. Backend keys via Spring profiles (e.g., `application-postgresql.yml`); frontend uses `.env` with `VITE_*`.
- RAG features require PostgreSQL + pgvector. Verify `CREATE EXTENSION IF NOT EXISTS vector;` before enabling the `postgresql` profile.

